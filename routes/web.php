<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\CarRentalController;
use App\Http\Controllers\DriverController;
use App\Http\Controllers\HireDriverController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ShuttleController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PageController;
use App\Http\Controllers\ThemeController;
use App\Http\Controllers\LayoutController;
use App\Http\Controllers\TaxiController;

// Public routes (accessible without login)
Route::get('/', [PageController::class, 'login'])->name('login');
Route::get('theme-switcher/{activeTheme}', [ThemeController::class, 'switch'])->name('theme-switcher');
Route::get('layout-switcher/{activeLayout}', [LayoutController::class, 'switch'])->name('layout-switcher');

// Protected routes (require authentication)
Route::middleware(['auth'])->group(function () {
    Route::controller(PageController::class)->group(function () {
        // Admin Routes
        Route::get('view_drivers', 'viewDrivers')->name('view_drivers');
        Route::get('/register_driver', 'registerDriver')->name('register_driver');
        Route::get('/view_driver_profile/{driver}', [DriverController::class, 'show'])->name('view-driver-profile');
        Route::get('/edit_driver/{driver}', [DriverController::class, 'edit'])->name('edit-driver-profile');

        // Pricing Routes
        Route::get('view_car_models', 'viewCarModels')->name('view_car_models');
        Route::get('car_rental_pricing', 'manageCarRentals')->name('car_rental_pricing');
        Route::get('car_rental_sales', 'manageCarRentalSales')->name('car_rental_sales');
        Route::get('cab_rates', 'manageCabRates')->name('cab_rates');
        Route::get('driver_rental_rates', 'manageDriverHiringRates')->name('driver_rental_rates');
        Route::get('city_city_shuttle_rates', 'manageCityShuttleRates')->name('city_city_shuttle_rates');
        
        Route::get('accident_reports', 'accidentReports')->name('accident_reports');
        
        // Cab trips
        Route::get('view_cab_trips', 'manageCabBookings')->name('view_cab_trips');
        Route::get('/view_trip/{trip}', [TaxiController::class, 'show'])->name('view-trip');

        // Shuttle Bookings
        Route::get('view_shuttle_trips', 'manageShuttleBookings')->name('view_shuttle_trips');
        Route::get('/view_shuttle_trip/{trip}', [ShuttleController::class, 'show'])->name('view-shuttle-trip');

        // Hire driver bookings 
        Route::get('view_hire_driver_bookings', 'manageDriverBookings')->name('view_hire_driver_bookings');
        Route::get('/hire_driver/{book}', [HireDriverController::class, 'show'])->name('view-hire-details');

        // Car Rentals 
        Route::get('view_car_rental_bookings', 'manageCarRentalsBookings')->name('view_car_rental_bookings');
        Route::get('/car-rental/{book}', [CarRentalController::class, 'show'])->name('view-car-rental-details');

        // Payments Routes
        Route::get('view_payments', 'viewPayments')->name('view_payments');
        Route::get('/payments/{payment}', [PaymentController::class, 'show'])->name('view-payment-details');

        // Dashboard Routes
        Route::get('dashboard_view', 'viewDashboard')->name('dashboard_view');
        Route::get('/view_home', 'viewHome')->name('view_home');
        Route::get('/users', 'viewUsers')->name('view_users');
    });
});

// API routes should also be protected
Route::middleware(['auth:sanctum'])->prefix('api')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    
    // Route::get('/cab_rates', [PricingController::class, 'index']);
    // Route::post('/clients', [SupabaseAPIController::class, 'saveClientData']);
    // ... other API routes
});
