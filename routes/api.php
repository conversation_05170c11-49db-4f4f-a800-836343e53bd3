<?php

use App\Http\Controllers\API\PaynowController;
use App\Http\Controllers\API\PaynowShuttleController;
use App\Http\Controllers\API\PricingController;
use App\Http\Controllers\API\SupabaseAPIController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('/cab_rates', [PricingController::class, 'index']);

Route::post('/clients', [SupabaseAPIController::class, 'saveClientData']);

Route::post('/paynow/pay', [PaynowController::class, 'pay']);
Route::post('/paynow/shuttle/pay', [PaynowShuttleController::class, 'pay']);
Route::get('/paynow-return-shuttle/{orderNumber}', [PaynowShuttleController::class, 'paynowReturn']);
Route::get('/paynow-return/{orderNumber}', [PaynowController::class, 'paynowReturn']);
Route::get('/get_payments/{client_id}', [PaynowController::class, 'getPayments']);
