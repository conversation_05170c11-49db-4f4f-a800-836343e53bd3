<div>
    <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
        <div class="col-span-12 intro-y sm:col-span-6">
            <div class="mt-4">
                <x-impala-components.form.date-picker
                    wire:model="medicalTestsIssueDate"
                    id="medicalTestsIssueDate"
                    name="medicalTestsIssueDate"
                    label="Medical Test Issue Date" />
            </div>

            {{-- <div class="mt-4">
                <x-impala-components.form.file-upload
                    :image="$medicalTestFile"
                    wire:model="medicalTestFile"
                    name="medicalTestFile"
                    title="Upload Medical Test"
                    label="Upload Drivers
                     Licence"  />
            </div> --}}

            <div class="mt-4">
                <x-impala-components.form.file_pond_input
                title="Upload Medical Test"
                name="medicalTestFile"
            />
            </div>

            <div class="mt-4">
                <x-impala-components.form.date-picker
                    wire:model="impalaCeritificationDate"
                    id="impalaCeritificationDate"
                    name="impalaCeritificationDate"
                    label="Impala Certificate Issue Date" />
            </div>
        </div>
        <div class="col-span-12 intro-y sm:col-span-6">
            <div class="mt-4">
                <x-impala-components.form.date-picker
                    wire:model="policeClearanceIssueDate"
                    id="policeClearanceIssueDate"
                    name="policeClearanceIssueDate"
                    label="Police Clearance Issue Date" />
            </div>

            {{-- <div class="mt-4">
                <x-impala-components.form.file-upload
                    :image="$policeClearanceFile"
                    wire:model="policeClearanceFile"
                    name="policeClearanceFile"
                    title="Upload Police Clearence"
                    label="Upload Police
                     Clearance"  />
            </div> --}}

            <div class="mt-4">
                <x-impala-components.form.file_pond_input
                title="Upload Police Clearence"
                name="policeClearanceFile"
            />
            </div>

            {{-- <div class="mt-4">
                <x-impala-components.form.file-upload
                    :image="$proofOfResidenceFile"
                    wire:model="proofOfResidenceFile"
                    name="proofOfResidenceFile"
                    title="Proof Of Residence"
                    label="Upload Of Residence"  />
            </div> --}}
            <div class="mt-4">
                <x-impala-components.form.file_pond_input
                title="Upload Of Residence"
                name="proofOfResidenceFile"
            />
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/flowbite@2.4.1/dist/flowbite.min.js"></script>
<script>
    document.addEventListener('livewire:initialized', () => {
        console.log('JS comp initializedMM');
        let component = @this;
       
        const medicalTestsIssueDate = document.getElementById('medicalTestsIssueDate');
        const policeClearanceIssueDate = document.getElementById('policeClearanceIssueDate');
        medicalTestsIssueDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('medicalTestsIssueDate', event.target.value);
        });

        policeClearanceIssueDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('policeClearanceIssueDate', event.target.value);
        });
    });
</script>

</div>
@pushOnce('styles')
@vite('resources/css/vendors/filepond.css')
@endPushOnce

@pushOnce('vendors')
@vite('resources/js/vendors/filepond.js')
@endPushOnce

