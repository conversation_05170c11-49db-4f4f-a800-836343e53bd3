
    
    <div>
    <div class="intro-y mt-8 flex items-center">
        <h2 class="mr-auto text-lg font-medium"></h2>
    </div>
    <div class="intro-y mt-5 grid grid-cols-12 gap-6">
        <div class="col-span-12 lg:col-span-12">
     
            <x-base.preview-component class="intro-y box mt-5">
                <div
                    class="flex flex-col items-center border-b border-slate-200/60 p-5 dark:border-darkmode-400 sm:flex-row">
                    <h2 class="mr-auto text-base font-medium">Edit Driver Profile</h2>
                    
                </div>
                <div class="p-5">
                    <x-base.preview>
                        <x-base.tab.group>
                            <x-base.tab.list variant="boxed-tabs">
                                <x-base.tab
                                    id="example-t"
                                    selected="{{ $show_driver_details ? true : false }}"     
                                >
                                    <x-base.tab.button
                                        wire:click="show_driver_details_panel"
                                        class="w-full py-2"
                                        as="button"
                                    >
                                       Driver Details
                                    </x-base.tab.button>
                                </x-base.tab>
                                <x-base.tab
                                 id="nextofkin_tab"
                                 selected="{{ $show_next_of_kin_details ? true : false }}" 
                                 >
                                    <x-base.tab.button
                                        wire:click="show_nextofkin_panel"
                                        class="w-full py-2"
                                        as="button"
                                    >
                                        Next Of Kin
                                    </x-base.tab.button>
                                </x-base.tab>
                                <x-base.tab 
                                id="licensing_tab"
                                selected="{{ $show_licensing_details ? true : false }}" 
                                >
                                    <x-base.tab.button
                                      wire:click="show_licensing_panel"
                                        class="w-full py-2"
                                        as="button"
                                    >
                                        Licensing
                                    </x-base.tab.button>
                                </x-base.tab>
                                <x-base.tab 
                                id="impala_training_tab"
                                selected="{{ $show_impala_training_details ? true : false }}" 
                                >
                                    <x-base.tab.button
                                    wire:click="show_training_panel"
                                        class="w-full py-2"
                                        as="button"
                                    >
                                        Impala Training
                                    </x-base.tab.button>
                                </x-base.tab>
                                
                               
                                <x-base.tab
                                selected="{{ $show_banking_details ? true : false }}" 
                                >
                                    <x-base.tab.button
                                        id="banking"
                                         wire:click="show_banking_panel"
                                        class="w-full py-2"
                                        as="button"
                                    >
                                        Banking Details
                                    </x-base.tab.button>
                                </x-base.tab>
                            </x-base.tab.list>
                            <x-base.tab.panels class="mt-5">
                                {{-- <x-base.tab.panel
                                    class="leading-relaxed"
                                    id="example-t"
                                    selected
                                > --}}
                                <div {{ $show_driver_details ? '' : 'hidden' }}>
                                    <div class="text-base font-medium">Driver details</div>
                                    <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
                                        <div class="col-span-12 intro-y sm:col-span-6">
                                            <x-impala-components.form.primary-text-input
                                                wire:model="driverFirstname"
                                                name="driverFirstname"
                                                label="First Name"
                                                placeholder="Driver firstname"  />
                
                                            <div class="mt-4">
                                                <x-impala-components.form.date-picker
                                                    value="{{ $driverDOB }}"
                                                    wire:model="driverDOB"
                                                    id="driverDOB"
                                                    name="driverDOB"
                                                    label="Date Of Birth" />
                                            </div>
                                            <div class="mt-4">
                                                <x-impala-components.form.primary-text-input
                                                    wire:model="driverMobileNumber"
                                                    name="driverMobileNumber"
                                                    label="Mobile Number"
                                                    placeholder="Enter mobile number"  />
                
                                            </div>
                                            <div class="mt-8">
                                                <x-impala-components.form.primary-text-input
                                                    wire:model="driverEmail"
                                                    name="driverEmail"
                                                    label="Email"
                                                    placeholder="Enter email address"  />
                                            </div>
                
                                            <div class="mt-4">
                                                <x-impala-components.form.primary-text-input
                                                    wire:model="nationalIdNumber"
                                                    name="nationalIdNumber"
                                                    label="National ID Number"
                                                    placeholder="National id number"  />
                                            </div>
                
                                            <div class="mt-4">
                                                <x-impala-components.form.file_pond_input
                                                title="Upload national Id"
                                                name="nationalIdFile"
                                            />
                                                {{-- <x-impala-components.form.file-upload
                                                    :image="$nationalIdFile"
                                                    wire:model="nationalIdFile"
                                                    name="nationalIdFile"
                                                    title="Upload Id"
                                                    label="Upload Id" /> --}}
                                            </div>
                
                                            <div class="mt-4">
                                                <x-impala-components.form.file_pond_input
                                                title="Upload Driver Photo"
                                                name="driverProfilePhotoFile"
                                            />
                                            </div>
                                            {{-- <div class="mt-4">
                                                <x-impala-components.form.file-upload
                                                    :image="$driverProfilePhotoFile"
                                                    wire:model="driverProfilePhotoFile"
                                                    name="driverProfilePhotoFile"
                                                    title="Upload Driver Photo"
                                                    label="Upload Driver Photo" />
                                            </div> --}}
                                        </div>
                                        <div class="col-span-12 intro-y sm:col-span-6">
                
                                            <x-impala-components.form.primary-text-input wire:model="driverLastname"  name="driverLastname" label="Last Name" placeholder="Driver lastname"  />
                                            <div class="mt-4">
                                                <x-impala-components.form.select-dropdown
                                                    wire:model="selectedGenderId"
                                                    id="selectedGenderId"
                                                    name="selectedGenderId"
                                                    placeholder="Gender"
                                                    label="Select Gender"
                                                    :selected="$selectedGenderId"
                                                    :options="$genders" />
                
                                            </div>
                                            <div class="mt-4">
                                                <x-impala-components.form.primary-text-input
                                                    wire:model="driverSecondMobileNumber"
                                                    name="driverSecondMobileNumber"
                                                    label="Second Mobile Number"
                                                    placeholder="Enter mobile number"  />
                                            </div>
                                            <div class="mt-4">
                                                <x-impala-components.form.textarea
                                                    wire:model="driverAddress"
                                                    label="Physical Address"
                                                    name="driverAddress"
                                                    placeholder="Enter physical address" />
                                            </div>
                
                                            <div class="mt-4">
                                                <x-impala-components.form.primary-text-input
                                                    wire:model="passportNumber"
                                                    name="passportNumber"
                                                    label="Passport Number"
                                                    placeholder="Passport number"  />
                                            </div>
                
                                            {{-- <div class="mt-4">
                                                <x-impala-components.form.file-upload
                                                    :image="$passportFile"
                                                    wire:model="passportFile"
                                                    name="passportFile"
                                                    title="Upload passport"
                                                    label="Upload Passport" />
                                            </div> --}}
                
                                            <div class="mt-4">
                                                <x-impala-components.form.file_pond_input
                                                title="Upload passport"
                                                name="passportFile"
                                            />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full border-t border-gray-200 mt-8"></div>
                                <div class="mt-4">
                                    <x-impala-components.form.<x-impala-components.form.primary-button
                                     wire:click="saveDriverDetailsChanges"
                                     name="Save Changes" />
                                </div>

                            </div>

                            <div {{ session('driver_changes_saved') ? '' : 'hidden' }} class="w-1/4">
                                <x-base.alert
                                class="flex justify-between mb-2"
                                variant="primary"
                                hidden
                            >
    
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                    {{ session('driver_changes_saved')  }}
                                <x-base.alert.dismiss-button
                                    class="text-white"
                                    type="button"
                                    aria-label="Close"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                </x-base.alert.dismiss-button>
                            </x-base.alert>
                            </div>
                        </div>
                                </div>
                                
                                {{-- </x-base.tab.panel> --}}
                            <div {{  $show_next_of_kin_details ? '' : 'hidden' }} >
                                <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5" >
                                    <div class="col-span-12 intro-y sm:col-span-6">
                                        <x-impala-components.form.primary-text-input
                                            wire:model="nextOfKinFullname"
                                            name="nextOfKinFullname"
                                            label="Full Name"
                                            placeholder="Next of kin full name"  />
    
                                        <div class="mt-4">
                                            <x-impala-components.form.textarea
                                                wire:model="nextOfKinAddress"
                                                label="Physical Address"
                                                name="nextOfKinAddress"
                                                placeholder="Address" />
                                        </div>
                                    </div>
                                    <div class="col-span-12 intro-y sm:col-span-6">
                                        <x-impala-components.form.primary-text-input
                                            wire:model="nextOfKinMobileNumber"
                                            name="nextOfKinMobileNumber"
                                            label="Mobile Number"
                                            placeholder="Mobile number"  />
    
                                    </div>
                                </div>
                                <div class="w-full border-t border-gray-200 mt-8"></div>
                                <div class="mt-4">
                                    <x-impala-components.form.<x-impala-components.form.primary-button
                                    wire:click="saveNextOfKinChanges"
                                     name="Save Changes" />
                                </div>
                                <div {{ session('nextofkin_changes_saved') ? '' : 'hidden' }} class="w-1/4">
                                    <x-base.alert
                                    class="flex justify-between mb-2"
                                    variant="primary"
                                    hidden
                                >
        
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                            {{ session('nextofkin_changes_saved')  }}
                                        <x-base.alert.dismiss-button
                                            class="text-white"
                                            type="button"
                                            aria-label="Close"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                        </x-base.alert.dismiss-button>
                                    </x-base.alert>
                                </div>
                            </div>
                                
                           
                                {{-- <x-base.tab.panel
                                    class="leading-relaxed"
                                    id="licensing_tab"
                                > --}}

                                <div {{ $show_licensing_details ? '' : 'hidden' }}>
                                    <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
                                        <div class="col-span-12 intro-y sm:col-span-6">
                                        
                                            <x-impala-components.form.primary-text-input
                                                wire:model="drivingExperience"
                                                type="number"
                                                name="drivingExperience"
                                                label="Driving Experience"
                                                placeholder="Driving experience"  />
                
                                            <div class="mt-4">
                                                <x-impala-components.form.date-picker
                                                    value="{{ $licenceIssueDate }}"
                                                    wire:model="licenceIssueDate"
                                                    id="licenceIssueDate"
                                                    name="licenceIssueDate"
                                                    label="Licence Issue Date" />
                                            </div>
                
                                            <div class="mt-4">
                                                <x-impala-components.form.file_pond_input
                                                title="Upload Drivers Licence"
                                                name="licenceFile"
                                            />
                                            </div>
                
                                            <div class="mt-4">
                                                <x-impala-components.form.file_pond_input
                                                title="Upload International Drivers Licence"
                                                name="internationalDrivingLicenceFile"
                                            />
                                            </div>
                
                
                                        </div>
                                        <div class="col-span-12 intro-y sm:col-span-6">
                                            <x-impala-components.form.primary-text-input
                                                wire:model="driversLicenceNumber"
                                                name="driversLicenceNumber"
                                                label="Drivers licence number"
                                                placeholder="Licence number"  />
                                            <div class="mt-4">
                                                <x-impala-components.form.select-dropdown
                                                    wire:model="selectedClassId"
                                                    id="selectedClassId"
                                                    name="selectedClassId"
                                                    placeholder="Drivers licence class"
                                                    label="Select class"
                                                    selected="$selectedClassId"
                                                    :options="$licenceClasses" />
                                            </div>
                                            <div class="mt-4">
                                                <x-impala-components.form.file_pond_input
                                                title="Upload Defensive Licence"
                                                name="defencelicenceFile"
                                            />
                                            </div>
                                            <div class="mt-4">
                                                <x-impala-components.form.date-picker
                                                    wire:model="defencelicenceExpiryDate"
                                                    name="defencelicenceExpiryDate"
                                                    id="defencelicenceExpiryDate"
                                                    label="Defensive Licence Expiry Date" />
                                            </div>
                                            <div class="mt-4">
                                                <x-impala-components.form.file_pond_input
                                                title="First Aid Certificate"
                                                name="firstAidCertificateFile"
                                            />
                                            </div>
                                        
                                        </div>
                                        
                                    </div>
                                    <div class="w-full border-t border-gray-200 mt-8"></div>
                                    <div class="mt-4">
                                        <x-impala-components.form.<x-impala-components.form.primary-button
                                        wire:click="saveLicensingChanges"
                                         name="Save Changes" />
                                    </div>
                                    <div {{ session('licence_changes_saved') ? '' : 'hidden' }} class="w-1/4">
                                        <x-base.alert
                                        class="flex justify-between mb-2"
                                        variant="primary"
                                        hidden
                                    >
            
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                                {{ session('licence_changes_saved')  }}
                                            <x-base.alert.dismiss-button
                                                class="text-white"
                                                type="button"
                                                aria-label="Close"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                            </x-base.alert.dismiss-button>
                                        </x-base.alert>
                                    </div>
                                </div>
                                
                            
                            
                            <div {{ $show_impala_training_details ? '' : 'hidden' }}>
                            
                                    @livewire('driver.edit-impla-training-component',
                                    [
                                        'driver' => $driver,
                                        'trainings' => $trainings,
                                        ])
                                        
                              
                            </div>

                            <div class="{{ $show_banking_details ? '' : 'hidden'  }} ">
                                <div class="text-base font-medium">Banking Details</div>
                                 <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
                                    <div class="col-span-12 intro-y sm:col-span-4">
                
                                        <div class="mt-4">
                                            <x-impala-components.form.select-dropdown
                                                wire:model="selectedBankId"
                                                id="selectedBankId"
                                                name="selectedBankId"
                                                placeholder="Bank"
                                                label="Select Bank"
                                                :selected="$selectedBankId"
                                                :options="$banks" />
                
                                        </div>
                
                                        <div class="mt-4">
                                            <x-impala-components.form.primary-text-input
                                                wire:model="accountNumber"
                                                name="acocuntNumber"
                                                label="Account Number"
                                                placeholder="Bank account number"  />
                                        </div>
                                    </div>
                
                                    <div class="col-span-12 intro-y sm:col-span-4">
                                        <div class="mt-4">
                                            <x-impala-components.form.primary-text-input
                                                wire:model="ecocashNumber"
                                                name="ecocashNumber"
                                                label="Ecocash Number"
                                                placeholder="Ecocash number"  />
                                        </div>
                
                                        <div class="mt-4">
                                            <x-impala-components.form.primary-text-input
                                                wire:model="innBucksNumber"
                                                name="innBucksNumber"
                                                label="Innbucks Number"
                                                placeholder="Inbucks number"  />
                                        </div>
                
                                      
                                        <div class="mt-4">
                                    {{-- @if (session('driver_created'))
                                            <x-base.alert
                                            class="flex justify-between mb-2"
                                            variant="primary"
                                        >
                
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                                Driver profile created successfully
                                                <x-base.alert.dismiss-button
                                                    class="text-white"
                                                    type="button"
                                                    aria-label="Close"
                                                >
                
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                            </x-base.alert.dismiss-button>
                                          </x-base.alert>
                                        @endif --}}
                                       
                            
                                        </div>   
                                    </div>
                                  
                     
                                </div>
                                <div class="w-full border-t border-gray-200 mt-8"></div>
                            <div class="mt-4">
                                <x-impala-components.form.<x-impala-components.form.primary-button 
                                wire:click="saveBankingDetails"
                                name="Save Changes" />
                            </div>
                            <div {{ session('banking_changes_saved') ? '' : 'hidden' }}>
                                <x-base.alert
                                class="flex justify-between mb-2"
                                variant="primary"
                                hidden
                            >
    
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                    {{ session('banking_changes_saved')  }}
                                <x-base.alert.dismiss-button
                                    class="text-white"
                                    type="button"
                                    aria-label="Close"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                </x-base.alert.dismiss-button>
                            </x-base.alert>
                            </div>
                            </div>
                            
                           
                            
                            {{-- </x-base.tab.panel> --}}
                            </x-base.tab.panels>
                        </x-base.tab.group>
                    </x-base.preview>
                </div>
            </x-base.preview-component>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/flowbite@2.4.1/dist/flowbite.min.js"></script>
<script>
    document.addEventListener('livewire:initialized', () => {
        console.log('JS comp initializedvvvvvv');
        let component = @this;
        const dateOfBirth = document.getElementById('driverDOB');
        const licenceIssueDate = document.getElementById('licenceIssueDate');
        const defencelicenceExpiryDate = document.getElementById('defencelicenceExpiryDate');
        const medicalTestsIssueDate = document.getElementById('medicalTestsIssueDate');
        // const policeClearanceIssueDate = document.getElementById('policeClearanceIssueDate');
      
        dateOfBirth.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('driverDOB', event.target.value);
        });

        licenceIssueDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('licenceIssueDate', event.target.value);
        });

        defencelicenceExpiryDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('defencelicenceExpiryDate', event.target.value);
        });

        medicalTestsIssueDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('medicalTestsIssueDate', event.target.value);
        });

        // policeClearanceIssueDate.addEventListener('blur', (event) => {
        //     console.log(event.target.value);
        //     component.set('policeClearanceIssueDate', event.target.value);
        // });
    });
</script>

</div>
@pushOnce('styles')
@vite('resources/css/vendors/filepond.css')
@endPushOnce

@pushOnce('vendors')
@vite('resources/js/vendors/filepond.js')
@endPushOnce
