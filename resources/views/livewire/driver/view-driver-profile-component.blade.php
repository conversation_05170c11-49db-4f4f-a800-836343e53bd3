<div>
    <div class="intro-y mt-8 flex items-center">
        <h2 class="mr-auto text-lg font-medium">Driver Profile</h2>
    </div>
    <x-base.tab.group>
        <!-- BEGIN: Profile Info -->
        <div class="intro-y box mt-5 px-5 pt-5">
            <div class="-mx-5 flex flex-col border-b border-slate-200/60 pb-5 dark:border-darkmode-400 lg:flex-row">
                <div class="flex flex-1 items-center justify-center px-5 lg:justify-start">
                    <div class="image-fit relative h-20 w-20 flex-none sm:h-24 sm:w-24 lg:h-32 lg:w-32">
                        <img
                            class="rounded-full"
                            src="{{ asset('storage/' . $driver->profile_photo_file) }}"
                            alt="Midone - Tailwind Admin Dashboard Template"
                        />
                    </div>
                    <div class="ml-5">
                        <div class="w-24 truncate text-lg font-medium sm:w-40 sm:whitespace-normal">
                            {{ $driver->driver_firstname . ' ' . $driver->driver_lastname }}
                        </div>
                        <div class="text-slate-500">{{ $driver->driversLicence->licence_class }}</div>
                    </div>
                </div>

                
                <div
                    class="mt-6 flex-1 border-l border-r border-t border-slate-200/60 px-5 pt-5 dark:border-darkmode-400 lg:mt-0 lg:border-t-0 lg:pt-0">
                    <div class="text-center font-medium lg:mt-3 lg:text-left">
                        Contact Details
                    </div>
                    <div class="mt-4 flex flex-col items-center justify-center lg:items-start">
                        <div class="flex items-center truncate sm:whitespace-normal">
                            <x-base.lucide
                                class="mr-2 h-4 w-4"
                                icon="Mail"
                            />
                            {{ $driver->driver_email }}
                        </div>
                        <div class="mt-3 flex items-center truncate sm:whitespace-normal">
                            <x-base.lucide
                                class="mr-2 h-4 w-4"
                                icon="phone"
                            /> 
                            {{ $driver->driver_mobile }}, {{ $driver->driver_second_mobile ? 'Second mobile ('. $driver->driver_second_mobile . ')' : ''  }}
                        </div>
                        <div class="mt-3 flex items-center truncate sm:whitespace-normal">
                            <x-base.lucide
                                class="mr-2 h-4 w-4"
                                icon="home"
                            /> 
                            {{ $driver->driver_address }}
                        </div>
                    </div>
                </div>
                <div
                    class="mt-6 flex flex-1 items-center justify-center border-t border-slate-200/60 px-5 pt-5 dark:border-darkmode-400 lg:mt-0 lg:border-0 lg:pt-0">
                    <div class="w-20 rounded-md py-3 text-center">
                        <div class="text-xl font-medium text-primary">201</div>
                        <div class="text-slate-500">Trips</div>
                    </div>
                    <div class="w-20 rounded-md py-3 text-center">
                        <div class="text-xl font-medium text-primary">0</div>
                        <div class="text-slate-500">Accident(s)</div>
                    </div>
                    <div class="w-20 rounded-md py-3 text-center">
                        <div class="text-xl font-medium text-primary">1</div>
                        <div class="text-slate-500">Reviews</div>
                    </div>
                </div>
            </div>
            <x-base.tab.list
                class="flex-col justify-center text-center sm:flex-row lg:justify-start"
                variant="link-tabs"
            >
                <x-base.tab
                    id="profile-tab"
                    :fullWidth="false"
                    selected
                >
                    <x-base.tab.button class="flex cursor-pointer items-center py-4">
                        <x-base.lucide
                            class="mr-2 h-4 w-4"
                            icon="User"
                        /> Profile
                    </x-base.tab.button>
                </x-base.tab>
                <x-base.button
                    wire:click="toggleBankingDetails"
                    variant="outline-secondary" 
                    class="ml-5"
                >
                    <x-base.lucide
                        class="mr-2 h-4 w-4"
                        icon="CreditCard"
                    /> Banking Details
                </x-base.button>
            </x-base.tab.list>

            <!-- Modal -->
            <div
                id="banking-details-modal"
                class="modal"
                tabindex="-1"
                aria-hidden="true"
                data-tw-backdrop="static"
                x-show="$wire.showBankingDetails"
            >
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <!-- Header -->
                        <div class="modal-header flex items-center justify-between p-5 border-b border-slate-200/60">
                            <h2 class="font-medium text-lg">Banking Details</h2>
                            <button 
                                @click="$wire.toggleBankingDetails()"
                                class="hover:text-slate-500 transition duration-200 ease-in-out"
                            >
                                <x-base.lucide icon="X" class="w-5 h-5" />
                            </button>
                        </div>

                        <!-- Body -->
                        <div class="modal-body p-6">
                            <div class="space-y-6">
                                <!-- Bank Details Section -->
                                <div class="space-y-4">
                                    <x-impala-helper.licensing-medical-tile 
                                        title="Bank Name"
                                        sub_title="{{ optional($driver->bank)->bank ?? 'Not Set' }}"
                                    />

                                    <x-impala-helper.licensing-medical-tile 
                                        title="Account Number"
                                        sub_title="{{ $driver->account_number ?? 'Not Set' }}"
                                    />
                                </div>

                                <!-- Mobile Money Section -->
                                <div class="space-y-4 pt-6 border-t border-slate-200/60">
                                    <h3 class="font-medium text-base text-slate-900">Mobile Money</h3>
                                    <x-impala-helper.licensing-medical-tile 
                                        title="Ecocash Number"
                                        sub_title="{{ $driver->ecocash_number ?? 'Not Set' }}"
                                    />

                                    <x-impala-helper.licensing-medical-tile 
                                        title="InnBucks Number"
                                        sub_title="{{ $driver->innbucks_number ?? 'Not Set' }}"
                                    />
                                </div>
                            </div>
                        </div>

                        <!-- Footer -->
                        <div class="modal-footer flex items-center justify-end p-5 border-t border-slate-200/60">
                            <x-base.button
                                @click="$wire.toggleBankingDetails()"
                                variant="outline-secondary"
                                class="w-20"
                            >
                                Close
                            </x-base.button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END: Profile Info -->
        <x-base.tab.panels class="mt-5">
            <!-- Profile Tab Panel -->
            <x-base.tab.panel 
                id="profile-content"
                selected>
                <div class="grid grid-cols-12 gap-6">
                    <!-- BEGIN: Latest Uploads -->
                    <div class="intro-y box col-span-12 lg:col-span-6">
                        <div
                            class="flex items-center border-b border-slate-200/60 px-5 py-5 dark:border-darkmode-400 sm:py-3">
                            
                            <x-base.menu class="ml-auto sm:hidden">
                                <x-base.menu.button
                                    class="block h-5 w-5"
                                    href="#"
                                    tag="a"
                                >
                                    <x-base.lucide
                                        class="h-5 w-5 text-slate-500"
                                        icon="MoreHorizontal"
                                    />
                                </x-base.menu.button>
                                <x-base.menu.items class="w-40">
                                    <x-base.menu.item>All Files</x-base.menu.item>
                                </x-base.menu.items>
                            </x-base.menu>
                            <x-base.button
                             wire:click="downloadNationalId"
                                class="hidden sm:flex"
                                variant="outline-secondary"
                            >
                                Documents
                            </x-base.button>
                        </div>
                        <div class="p-5">
                            <x-impala-helper.file-display-view 
                            file_path="{{ $driver->national_id_file_path }}" 
                            title="National Id"
                            size="{{ $this->getFileSize($driver->national_id_file_path) }}">
                            
                            <x-base.button
                             wire:click="downloadNationalId"
                                class="hidden sm:flex"
                                variant="outline-secondary"
                            >
                            <div wire:loading wire:target="downloadNationalId" class="mr-2">
                                <x-base.loading-icon icon="oval" class="w-4 h-4" />
                            </div>
                            <span wire:loading.remove wire:target="downloadNationalId">
                                Download
                            </span>
                            <span wire:loading wire:target="downloadNationalId">
                                Downloading...
                            </span>
                            </x-base.button>

                            </x-impala-helper.file-display-view>

                            <x-impala-helper.file-display-view 
                            file_path="{{ $driver->passport_file_path }}" 
                            title="Passport"
                            size="{{ $this->getFileSize($driver->passport_file_path) }}"
                            >

                                <x-base.button
                             wire:click="downloadPassport"
                                class="hidden sm:flex"
                                variant="outline-secondary"
                            >
                            <div wire:loading wire:target="downloadPassport" class="mr-2">
                                <x-base.loading-icon icon="oval" class="w-4 h-4" />
                            </div>
                            <span wire:loading.remove wire:target="downloadPassport">
                                Download
                            </span>
                            <span wire:loading wire:target="downloadPassport">
                                Downloading...
                            </span>
                            </x-base.button>

                        
                            </x-impala-helper.file-display-view>

                            <x-impala-helper.file-display-view 
                            file_path="{{ $driver->licence_file_path }}" 
                            title="Drivers licence"
                            size="{{ $this->getFileSize($driver->licence_file_path) }}"
                            >
                        
                            <x-base.button
                            wire:click="downloadDriversLicence"
                               class="hidden sm:flex"
                               variant="outline-secondary"
                           >
                            <div wire:loading wire:target="downloadDriversLicence" class="mr-2">
                                <x-base.loading-icon icon="oval" class="w-4 h-4" />
                            </div>
                            <span wire:loading.remove wire:target="downloadDriversLicence">
                                Download
                            </span>
                            <span wire:loading wire:target="downloadDriversLicence">
                                Downloading...
                            </span>
                           </x-base.button>

                            </x-impala-helper.file-display-view>

                            <x-impala-helper.file-display-view 
                            file_path="{{ $driver->defence_licence_file_path }}" 
                            title="Defensive Drivers licence"
                            size="{{ $this->getFileSize($driver->defence_licence_file_path) }}"
                            >
                                <x-base.button
                                wire:click="downloadDefenceDriversLicence"
                                class="hidden sm:flex"
                                variant="outline-secondary"
                            >
                            <div wire:loading wire:target="downloadDefenceDriversLicence" class="mr-2">
                                <x-base.loading-icon icon="oval" class="w-4 h-4" />
                            </div>
                            <span wire:loading.remove wire:target="downloadDefenceDriversLicence">
                                Download
                            </span>
                            <span wire:loading wire:target="downloadDefenceDriversLicence">
                                Downloading...
                            </span>
                            </x-base.button>
                            </x-impala-helper.file-display-view>

                            

                            <x-impala-helper.file-display-view 
                            file_path="{{ $driver->idl_licence_path }}" 
                            title="International Drivers licence"
                            size="{{ $this->getFileSize($driver->idl_licence_path) }}"
                            >
                        
                                <x-base.button
                                wire:click="downloadIDL"
                                class="hidden sm:flex"
                                variant="outline-secondary"
                                >
                                <div wire:loading wire:target="downloadIDL" class="mr-2">
                                    <x-base.loading-icon icon="oval" class="w-4 h-4" />
                                </div>
                                <span wire:loading.remove wire:target="downloadIDL">
                                    Download
                                </span>
                                <span wire:loading wire:target="downloadIDL">
                                    Downloading...
                                </span>
                                </x-base.button>
                            </x-impala-helper.file-display-view>

                            <x-impala-helper.file-display-view 
                            file_path="{{ $driver->first_aid_certificate_file_path }}" 
                            title="First aid medical certificate"
                            size="{{ $this->getFileSize($driver->first_aid_certificate_file_path) }}"
                            >
                                <x-base.button
                                wire:click="downloadFirstAid"
                                class="hidden sm:flex"
                                variant="outline-secondary"
                                >
                                <div wire:loading wire:target="downloadFirstAid" class="mr-2">
                                    <x-base.loading-icon icon="oval" class="w-4 h-4" />
                                </div>
                                <span wire:loading.remove wire:target="downloadFirstAid">
                                    Download
                                </span>
                                <span wire:loading wire:target="downloadFirstAid">
                                    Downloading...
                                </span>
                                </x-base.button>
                            </x-impala-helper.file-display-view>

                            <x-impala-helper.file-display-view 
                            file_path="{{ $driver->medical_test_file_path }}" 
                            title="Medical test"
                            size="{{ $this->getFileSize($driver->medical_test_file_path) }}"
                            >
                                <x-base.button
                                wire:click="downloadMedicalAidTest"
                                class="hidden sm:flex"
                                variant="outline-secondary"
                                >
                                <div wire:loading wire:target="downloadMedicalAidTest" class="mr-2">
                                    <x-base.loading-icon icon="oval" class="w-4 h-4" />
                                </div>
                                <span wire:loading.remove wire:target="downloadMedicalAidTest">
                                    Download
                                </span>
                                <span wire:loading wire:target="downloadMedicalAidTest">
                                    Downloading...
                                </span>
                                
                                </x-base.button>
                            </x-impala-helper.file-display-view>

                            <x-impala-helper.file-display-view 
                            file_path="{{ $driver->police_clearance_file_path}}" 
                            title="Police clearance"
                            size="{{ $this->getFileSize($driver->police_clearance_file_path) }}"
                            >
                                <x-base.button
                                wire:click="downloadPoliceClearence"
                                class="hidden sm:flex"
                                variant="outline-secondary"
                                >
                                <div wire:loading wire:target="downloadPoliceClearence" class="mr-2">
                                    <x-base.loading-icon icon="oval" class="w-4 h-4" />
                                </div>
                                <span wire:loading.remove wire:target="downloadPoliceClearence">
                                    Download
                                </span>
                                <span wire:loading wire:target="downloadPoliceClearence">
                                    Downloading...
                                </span>
                                </x-base.button>
                            </x-impala-helper.file-display-view>

                        </div>


                    </div>
                    <!-- END: Latest Uploads -->
                    <!-- BEGIN: Work In Progress -->
                    <x-base.tab.group class="intro-y box col-span-12 lg:col-span-6">
                        <div
                            class="flex items-center border-b border-slate-200/60 px-5 py-5 dark:border-darkmode-400 sm:py-0">
                            <h2 class="mr-auto text-base font-medium p-4">
                                Licensing and Clearance
                            </h2>
                            <x-base.menu class="ml-auto sm:hidden">
                                <x-base.menu.button
                                    class="block h-5 w-5"
                                    href="#"
                                    tag="a"
                                >
                                    <x-base.lucide
                                        class="h-5 w-5 text-slate-500"
                                        icon="MoreHorizontal"
                                    />
                                </x-base.menu.button>
                                {{-- <x-base.menu.items class="w-40">
                                    <x-base.menu.item
                                        class="w-full"
                                        id="work-in-progress-mobile-new-tab"
                                        target="work-in-progress-new"
                                        as="x-base.tab.button"
                                        unstyled
                                        selected
                                    >
                                        New
                                    </x-base.menu.item>
                                    <x-base.menu.item
                                        class="w-full"
                                        id="work-in-progress-mobile-last-week-tab"
                                        target="work-in-progress-last-week"
                                        as="x-base.tab.button"
                                        unstyled
                                        :selected="false"
                                    >
                                        Last Week
                                    </x-base.menu.item>
                                </x-base.menu.items> --}}
                            </x-base.menu>
                            
                        </div>
                        <div class="p-5">
                            <div class="text-center  text-base font-bold lg:mt-3 lg:text-left">
                                Licence Details
                            </div>

                            <x-impala-helper.licensing-medical-tile 
                                title="Drivers licence number"
                                sub_title="{{ $driver->drivers_licence }}"
                            />

                            <x-impala-helper.licensing-medical-tile 
                            title="Drivers Licence Date Issued"
                            sub_title="{{ convertDateToHumanFormat($driver->licence_issue_date) }}"
                        />

                        <x-impala-helper.licensing-medical-tile 
                            title="Defensive Driving Expiry Date"
                            sub_title="{{ convertDateToHumanFormat($driver->defence_licence_expiry_date) }}"
                        />

                        <x-impala-helper.licensing-medical-tile 
                        title="International Drivers Licence Issue Date"
                        sub_title="{{ convertDateToHumanFormat($driver->defence_licence_expiry_date) }}"
                    />

                    <div class="text-center  text-base font-bold lg:mt-3 lg:text-left mt-10">
                        Medicals
                    </div>

                    <x-impala-helper.licensing-medical-tile 
                        title="Medical Test Issue Date"
                        sub_title="{{ convertDateToHumanFormat($driver->medical_tests_issue_date) }}"
                    />
                           
                    
                    <div class="text-center  text-base font-bold lg:mt-3 lg:text-left mt-10">
                        Clearance
                    </div>

                    <x-impala-helper.licensing-medical-tile 
                        title="Police clearance issued data"
                        sub_title="{{ convertDateToHumanFormat($driver->medical_tests_issue_date) }}"
                    />
                        </div>
                    </x-base.tab.group>
                    <!-- END: Work In Progress -->
                    <!-- BEGIN: Daily Sales -->
                    {{-- <div class="intro-y box col-span-12 lg:col-span-6">
                        <div
                            class="flex items-center border-b border-slate-200/60 px-5 py-5 dark:border-darkmode-400 sm:py-3">
                            <h2 class="mr-auto text-base font-medium">Daily Sales</h2>
                            <x-base.menu class="ml-auto sm:hidden">
                                <x-base.menu.button
                                    class="block h-5 w-5"
                                    href="#"
                                    tag="a"
                                >
                                    <x-base.lucide
                                        class="h-5 w-5 text-slate-500"
                                        icon="MoreHorizontal"
                                    />
                                </x-base.menu.button>
                                <x-base.menu.items class="w-40">
                                    <x-base.menu.item>
                                        <x-base.lucide
                                            class="mr-2 h-4 w-4"
                                            icon="File"
                                        /> Download
                                        Excel
                                    </x-base.menu.item>
                                </x-base.menu.items>
                            </x-base.menu>
                            <x-base.button
                                class="hidden sm:flex"
                                variant="outline-secondary"
                            >
                                <x-base.lucide
                                    class="mr-2 h-4 w-4"
                                    icon="File"
                                /> Download
                                Excel
                            </x-base.button>
                        </div>
                        <div class="p-5">
                            <div class="relative flex items-center">
                                <div class="image-fit h-12 w-12 flex-none">
                                    <img
                                        class="rounded-full"
                                        src="{{ Vite::asset($fakers[0]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                                <div class="ml-4 mr-auto">
                                    <a
                                        class="font-medium"
                                        href=""
                                    >
                                        {{ $fakers[0]['users'][0]['name'] }}
                                    </a>
                                    <div class="mr-5 text-slate-500 sm:mr-5">
                                        Bootstrap 4 HTML Admin Template
                                    </div>
                                </div>
                                <div class="font-medium text-slate-600 dark:text-slate-500">
                                    +$19
                                </div>
                            </div>
                            <div class="relative mt-5 flex items-center">
                                <div class="image-fit h-12 w-12 flex-none">
                                    <img
                                        class="rounded-full"
                                        src="{{ Vite::asset($fakers[1]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                                <div class="ml-4 mr-auto">
                                    <a
                                        class="font-medium"
                                        href=""
                                    >
                                        {{ $fakers[1]['users'][0]['name'] }}
                                    </a>
                                    <div class="mr-5 text-slate-500 sm:mr-5">
                                        Tailwind Admin Dashboard Template
                                    </div>
                                </div>
                                <div class="font-medium text-slate-600 dark:text-slate-500">
                                    +$25
                                </div>
                            </div>
                            <div class="relative mt-5 flex items-center">
                                <div class="image-fit h-12 w-12 flex-none">
                                    <img
                                        class="rounded-full"
                                        src="{{ Vite::asset($fakers[2]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                                <div class="ml-4 mr-auto">
                                    <a
                                        class="font-medium"
                                        href=""
                                    >
                                        {{ $fakers[2]['users'][0]['name'] }}
                                    </a>
                                    <div class="mr-5 text-slate-500 sm:mr-5">
                                        Vuejs HTML Admin Template
                                    </div>
                                </div>
                                <div class="font-medium text-slate-600 dark:text-slate-500">
                                    +$21
                                </div>
                            </div>
                        </div>
                    </div> --}}
                    <!-- END: Daily Sales -->
                    <!-- BEGIN: Latest Tasks -->
                    {{-- <x-base.tab.group class="intro-y box col-span-12 lg:col-span-6">
                        <div
                            class="flex items-center border-b border-slate-200/60 px-5 py-5 dark:border-darkmode-400 sm:py-0">
                            <h2 class="mr-auto text-base font-medium">
                                Latest Tasks
                            </h2>
                            <x-base.menu class="ml-auto sm:hidden">
                                <x-base.menu.button
                                    class="block h-5 w-5"
                                    href="#"
                                    tag="a"
                                >
                                    <x-base.lucide
                                        class="h-5 w-5 text-slate-500"
                                        icon="MoreHorizontal"
                                    />
                                </x-base.menu.button>
                                <x-base.menu.items class="w-40">
                                    <x-base.menu.item
                                        class="w-full"
                                        id="latest-tasks-mobile-new-tab"
                                        target="latest-tasks-new"
                                        as="x-base.tab.button"
                                        unstyled
                                        selected
                                    >
                                        New
                                    </x-base.menu.item>
                                    <x-base.menu.item
                                        class="w-full"
                                        id="latest-tasks-mobile-last-week-tab"
                                        target="latest-tasks-last-week"
                                        as="x-base.tab.button"
                                        unstyled
                                        :selected="false"
                                    >
                                        Last Week
                                    </x-base.menu.item>
                                </x-base.menu.items>
                            </x-base.menu>
                            <x-base.tab.list
                                class="ml-auto hidden w-auto sm:flex"
                                variant="link-tabs"
                            >
                                <x-base.tab
                                    id="latest-tasks-new-tab"
                                    :fullWidth="false"
                                    selected
                                >
                                    <x-base.tab.button class="cursor-pointer py-5">
                                        New
                                    </x-base.tab.button>
                                </x-base.tab>
                                <x-base.tab
                                    id="latest-tasks-last-week-tab"
                                    :fullWidth="false"
                                    :selected="false"
                                >
                                    <x-base.tab.button class="cursor-pointer py-5">
                                        Last Week
                                    </x-base.tab.button>
                                </x-base.tab>
                            </x-base.tab.list>
                        </div>
                        <div class="p-5">
                            <x-base.tab.panels>
                                <x-base.tab.panel
                                    id="latest-tasks-new"
                                    selected
                                >
                                    <div class="flex items-center">
                                        <div class="border-l-2 border-primary pl-4 dark:border-primary">
                                            <a
                                                class="font-medium"
                                                href=""
                                            >
                                                Create New Campaign
                                            </a>
                                            <div class="text-slate-500">10:00 AM</div>
                                        </div>
                                        <x-base.form-switch class="ml-auto">
                                            <x-base.form-switch.input type="checkbox" />
                                        </x-base.form-switch>
                                    </div>
                                    <div class="mt-5 flex items-center">
                                        <div class="border-l-2 border-primary pl-4 dark:border-primary">
                                            <a
                                                class="font-medium"
                                                href=""
                                            >
                                                Meeting With Client
                                            </a>
                                            <div class="text-slate-500">02:00 PM</div>
                                        </div>
                                        <x-base.form-switch class="ml-auto">
                                            <x-base.form-switch.input type="checkbox" />
                                        </x-base.form-switch>
                                    </div>
                                    <div class="mt-5 flex items-center">
                                        <div class="border-l-2 border-primary pl-4 dark:border-primary">
                                            <a
                                                class="font-medium"
                                                href=""
                                            >
                                                Create New Repository
                                            </a>
                                            <div class="text-slate-500">04:00 PM</div>
                                        </div>
                                        <x-base.form-switch class="ml-auto">
                                            <x-base.form-switch.input type="checkbox" />
                                        </x-base.form-switch>
                                    </div>
                                </x-base.tab.panel>
                            </x-base.tab.panels>
                        </div>
                    </x-base.tab.group> --}}
                    <!-- END: Latest Tasks -->
                    <!-- BEGIN: New Products -->
                    {{-- <div class="intro-y box col-span-12">
                        <div class="flex items-center border-b border-slate-200/60 px-5 py-3 dark:border-darkmode-400">
                            <h2 class="mr-auto text-base font-medium">
                                New Products
                            </h2>
                            <x-base.button
                                class="tiny-slider-navigator mr-2 px-2"
                                data-carousel="new-products"
                                data-target="prev"
                                variant="outline-secondary"
                            >
                                <x-base.lucide
                                    class="h-4 w-4"
                                    icon="ChevronLeft"
                                />
                            </x-base.button>
                            <x-base.button
                                class="tiny-slider-navigator px-2"
                                data-carousel="new-products"
                                data-target="next"
                                variant="outline-secondary"
                            >
                                <x-base.lucide
                                    class="h-4 w-4"
                                    icon="ChevronRight"
                                />
                            </x-base.button>
                        </div>
                        <x-base.tiny-slider
                            class="py-5"
                            id="new-products"
                        >
                            @foreach (array_slice($fakers, 0, 5) as $faker)
                                <div class="px-5">
                                    <div class="flex flex-col items-center pb-5 lg:flex-row">
                                        <div
                                            class="flex flex-col items-center border-slate-200/60 pr-5 dark:border-darkmode-400 sm:flex-row lg:border-r">
                                            <div class="sm:mr-5">
                                                <div class="image-fit h-20 w-20">
                                                    <img
                                                        class="rounded-full"
                                                        src="{{ Vite::asset($faker['images'][0]) }}"
                                                        alt="Midone - Tailwind Admin Dashboard Template"
                                                    />
                                                </div>
                                            </div>
                                            <div class="mr-auto mt-3 text-center sm:mt-0 sm:text-left">
                                                <a
                                                    class="text-lg font-medium"
                                                    href=""
                                                >
                                                    {{ $faker['products'][0]['name'] }}
                                                </a>
                                                <div class="mt-1 text-slate-500 sm:mt-0">
                                                    {{ $faker['news'][0]['short_content'] }}
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="mt-6 flex w-full flex-1 items-center justify-center border-t border-slate-200/60 px-5 pt-4 dark:border-darkmode-400 lg:mt-0 lg:w-auto lg:border-t-0 lg:pt-0">
                                            <div class="w-20 rounded-md py-3 text-center">
                                                <div class="text-xl font-medium text-primary">
                                                    {{ $faker['totals'][0] }}
                                                </div>
                                                <div class="text-slate-500">Orders</div>
                                            </div>
                                            <div class="w-20 rounded-md py-3 text-center">
                                                <div class="text-xl font-medium text-primary">
                                                    {{ $faker['totals'][1] }}k
                                                </div>
                                                <div class="text-slate-500">Purchases</div>
                                            </div>
                                            <div class="w-20 rounded-md py-3 text-center">
                                                <div class="text-xl font-medium text-primary">
                                                    {{ $faker['totals'][0] }}
                                                </div>
                                                <div class="text-slate-500">Reviews</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        class="flex flex-col items-center border-t border-slate-200/60 pt-5 dark:border-darkmode-400 sm:flex-row">
                                        <div
                                            class="flex w-full items-center justify-center border-b border-slate-200/60 pb-5 dark:border-darkmode-400 sm:w-auto sm:justify-start sm:border-b-0 sm:pb-0">
                                            <div
                                                class="mr-3 rounded bg-primary/10 px-3 py-2 font-medium text-primary dark:bg-darkmode-400 dark:text-slate-300">
                                                {{ $faker['dates'][0] }}
                                            </div>
                                            <div class="text-slate-500">
                                                Date of Release
                                            </div>
                                        </div>
                                        <div class="mt-5 flex sm:ml-auto sm:mt-0">
                                            <x-base.button
                                                class="ml-auto w-20"
                                                variant="secondary"
                                            >
                                                Preview
                                            </x-base.button>
                                            <x-base.button
                                                class="ml-2 w-20"
                                                variant="secondary"
                                            >
                                                Details
                                            </x-base.button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </x-base.tiny-slider>
                    </div> --}}
                    <!-- END: New Products -->
                    <!-- BEGIN: New Authors -->
                    {{-- <div class="intro-y box col-span-12">
                        <div class="flex items-center border-b border-slate-200/60 px-5 py-3 dark:border-darkmode-400">
                            <h2 class="mr-auto text-base font-medium">New Authors</h2>
                            <x-base.button
                                class="tiny-slider-navigator mr-2 px-2"
                                data-carousel="new-authors"
                                data-target="prev"
                                variant="outline-secondary"
                            >
                                <x-base.lucide
                                    class="h-4 w-4"
                                    icon="ChevronLeft"
                                />
                            </x-base.button>
                            <x-base.button
                                class="tiny-slider-navigator px-2"
                                data-carousel="new-authors"
                                data-target="next"
                                variant="outline-secondary"
                            >
                                <x-base.lucide
                                    class="h-4 w-4"
                                    icon="ChevronRight"
                                />
                            </x-base.button>
                        </div>
                        <x-base.tiny-slider
                            class="py-5"
                            id="new-authors"
                        >
                            @foreach (array_slice($fakers, 0, 5) as $faker)
                                <div class="px-5">
                                    <div class="flex flex-col items-center pb-5 lg:flex-row">
                                        <div
                                            class="flex flex-1 flex-col items-center border-slate-200/60 pr-5 dark:border-darkmode-400 sm:flex-row lg:border-r">
                                            <div class="sm:mr-5">
                                                <div class="image-fit h-20 w-20">
                                                    <img
                                                        class="rounded-full"
                                                        src="{{ Vite::asset($faker['photos'][0]) }}"
                                                        alt="Midone - Tailwind Admin Dashboard Template"
                                                    />
                                                </div>
                                            </div>
                                            <div class="mr-auto mt-3 text-center sm:mt-0 sm:text-left">
                                                <a
                                                    class="text-lg font-medium"
                                                    href=""
                                                >
                                                    {{ $faker['users'][0]['name'] }}
                                                </a>
                                                <div class="mt-1 text-slate-500 sm:mt-0">
                                                    {{ $faker['jobs'][0] }}
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="mt-6 flex w-full flex-1 flex-col items-center justify-center border-t border-slate-200/60 px-5 pt-4 dark:border-darkmode-400 lg:mt-0 lg:w-auto lg:items-start lg:border-t-0 lg:pt-0">
                                            <div class="flex items-center">
                                                <a
                                                    class="mr-2 flex h-8 w-8 items-center justify-center rounded-full border text-slate-400"
                                                    href=""
                                                >
                                                    <x-base.lucide
                                                        class="h-3 w-3 fill-current"
                                                        icon="Facebook"
                                                    />
                                                </a>
                                                {{ $faker['users'][0]['email'] }}
                                            </div>
                                            <div class="mt-2 flex items-center">
                                                <a
                                                    class="mr-2 flex h-8 w-8 items-center justify-center rounded-full border text-slate-400"
                                                    href=""
                                                >
                                                    <x-base.lucide
                                                        class="h-3 w-3 fill-current"
                                                        icon="Twitter"
                                                    />
                                                </a>
                                                {{ $faker['users'][0]['name'] }}
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        class="flex flex-col items-center border-t border-slate-200/60 pt-5 dark:border-darkmode-400 sm:flex-row">
                                        <div
                                            class="flex w-full items-center justify-center border-b border-slate-200/60 pb-5 dark:border-darkmode-400 sm:w-auto sm:justify-start sm:border-b-0 sm:pb-0">
                                            <div
                                                class="mr-3 rounded bg-primary/10 px-3 py-2 font-medium text-primary dark:bg-darkmode-400 dark:text-slate-300">
                                                {{ $faker['dates'][0] }}
                                            </div>
                                            <div class="text-slate-500">Joined Date</div>
                                        </div>
                                        <div class="mt-5 flex sm:ml-auto sm:mt-0">
                                            <x-base.button
                                                class="ml-auto w-20"
                                                variant="secondary"
                                            >
                                                Message
                                            </x-base.button>
                                            <x-base.button
                                                class="ml-2 w-20"
                                                variant="secondary"
                                            >
                                                Profile
                                            </x-base.button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </x-base.tiny-slider>
                    </div> --}}
                    <!-- END: New Authors -->
                </div>
            </x-base.tab.panel>

            <!-- Banking Details Tab Panel -->
            <x-base.tab.panel 
                id="account-content">
                <div class="grid grid-cols-12 gap-6">
                    <div class="intro-y box col-span-12 lg:col-span-6">
                        <div class="flex items-center border-b border-slate-200/60 px-5 py-5">
                            <h2 class="font-medium text-base mr-auto">Banking Details</h2>
                        </div>
                        <div class="p-5">
                            <x-impala-helper.licensing-medical-tile 
                                title="Bank Name"
                                sub_title="{{ optional($driver->bank)->bank ?? 'Not Set' }}"
                            />

                            <x-impala-helper.licensing-medical-tile 
                                title="Account Number"
                                sub_title="{{ $driver->account_number ?? 'Not Set' }}"
                            />

                            <div class="mt-6 border-t border-slate-200/60 pt-5">
                                <h3 class="font-medium text-base">Mobile Money</h3>
                            </div>

                            <x-impala-helper.licensing-medical-tile 
                                title="Ecocash Number"
                                sub_title="{{ $driver->ecocash_number ?? 'Not Set' }}"
                            />

                            <x-impala-helper.licensing-medical-tile 
                                title="InnBucks Number"
                                sub_title="{{ $driver->innbucks_number ?? 'Not Set' }}"
                            />
                        </div>
                    </div>
                </div>
            </x-base.tab.panel>
        </x-base.tab.panels>
    </x-base.tab.group>
</div>
