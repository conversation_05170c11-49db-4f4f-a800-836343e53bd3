<div class="grid grid-cols-12 gap-6">
    <div class="col-span-12 lg:col-span-4 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-4">
        @foreach($trainingTypes as $trainingType)
            <div class="mt-4" wire:key="{{ $trainingType->id }}">
                 
                @if($trainingType->id == 1)
                    <span class="font-medium">Impala Drivers Induction training</span>
                    <div>
                        <x-impala-components.form.date-picker
                            wire:model="impalaInductionTrainingDate"
                            name="impalaInductionTrainingDate"
                            id="impalaInductionTrainingDate"
                            label="Date of completion"
                            value="{{ $impalaInductionTrainingDate ?? 'Choose date'}}"
                            />
                    </div>
                @elseif($trainingType->id == 2)
                    <span class="font-medium">Impala drivers Code of conduct training</span>
                    <div>
                        <x-impala-components.form.date-picker
                            wire:model="impalaDriversCodeOfConductDate"
                            name="impalaDriversCodeOfConductDate"
                            id="impalaDriversCodeOfConductDate"
                            label="Date of completion"
                            value="{{ $impalaDriversCodeOfConductDate ?? 'Choose date' }}"
                        />
                    </div>
            @elseif($trainingType->id == 3)
            <span class="font-medium">Impala Safe driving training</span>
                <div>
                    <x-impala-components.form.date-picker
                        wire:model="impalaSafeDrivingDate"
                        name="impalaSafeDrivingDate"
                        id="impalaSafeDrivingDate"
                        label="Date of completion"
                        value="{{ $impalaSafeDrivingDate ?? 'Choose date' }}"

                    />
                </div>
            @elseif($trainingType->id == 4)
            <span class="font-medium">Impala Chauffeur drivers grooming and etiquette training</span>
                    <div>
                        <x-impala-components.form.date-picker
                            wire:model="impalaChaufferDriversCourseDate"
                            name="impalaChaufferDriversCourseDate"
                            id="impalaChaufferDriversCourseDate"
                            label="Date of completion"
                            value="{{ $impalaChaufferDriversCourseDate ?? 'Choose date' }}"
                        />
                    </div>
            @elseif($trainingType->id == 5)
                    <span class="font-medium">CMED VIP driving course</span>
                    <div>
                        <x-impala-components.form.date-picker
                            wire:model="impalaCMEDVipDrivingCourseDate"
                            name="impalaCMEDVipDrivingCourseDate"
                            id="impalaCMEDVipDrivingCourseDate"
                            label="Date of completion"
                            value="{{ $impalaCMEDVipDrivingCourseDate ?? 'Choose date' }}"
                        />
                    </div>

            @elseif($trainingType->id == 6)
                    <span class="font-medium"> Impala drivers inhouse retest yearly</span>
                    <div>
                        <x-impala-components.form.date-picker
                            wire:model="impalaDriversInHouseTrainingDate"
                            name="impalaDriversInHouseTrainingDate"
                            id="impalaDriversInHouseTrainingDate"
                            label="Date of completion"
                            value="{{ $impalaDriversInHouseTrainingDate ?? 'Choose date' }}"
                        />
                    </div>
            @endif
        </div>
    @endforeach
    <div class="w-full border-t border-gray-200 mt-8"></div>
        <div class="mt-4">
            <x-impala-components.form.<x-impala-components.form.primary-button
                wire:click="saveTrainingChanges"
                name="Save Changes" />
        </div>
        <div {{ session('tranining_changes_saved') ? '' : 'hidden' }} class="w-full">
            <x-base.alert
            class="flex justify-between mb-2"
            variant="primary"
            hidden
        >

        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                {{ session('tranining_changes_saved')  }}
            <x-base.alert.dismiss-button
                class="text-white"
                type="button"
                aria-label="Close"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
            </x-base.alert.dismiss-button>
        </x-base.alert>
    </div>
   
    </div>
    <div class="col-span-12 lg:col-span-8 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-8">
        <div class="text-base font-medium">Medical and Police Clearance</div>
                <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
                    <div class="col-span-12 intro-y sm:col-span-6">
                        <div class="mt-4">
                            <x-impala-components.form.date-picker
                                wire:model="medicalTestsIssueDate"
                                id="medicalTestsIssueDate"
                                name="medicalTestsIssueDate"
                                label="Medical Test Issue Date"
                                value="{{ $medicalTestsIssueDate }}"    

                                />
                        </div>
                        <div class="mt-4">
                            <x-impala-components.form.file_pond_input
                            title="Upload Medical Test"
                            name="medicalTestFile"
                        />
                        </div>

                        
                    </div>
                    <div class="col-span-12 intro-y sm:col-span-6">
                        <div class="mt-4">
                            <x-impala-components.form.date-picker
                                wire:model="policeClearanceIssueDate"
                                id="policeClearanceIssueDate"
                                name="policeClearanceIssueDate"
                                label="Police Clearance Issue Date" 
                                value="{{ $policeClearanceIssueDate }}"    
                                />
                        </div>

                        <div class="mt-4">
                            <x-impala-components.form.file_pond_input
                            title="Upload Police Clearence"
                            name="policeClearanceFile"
                        />
                        </div>
                        <div class="mt-4">
                            <x-impala-components.form.file_pond_input
                            title="Upload Of Residence"
                            name="proofOfResidenceFile"
                        />
                        </div>
                    </div>
                </div>
                <div class="w-full border-t border-gray-200 mt-8"></div>
        <div class="mt-4">
            <x-impala-components.form.primary-button
                wire:click="saveMedicalChanges"
                name="Save Changes" />
        </div>
        <div {{ session('medical_changes_saved') ? '' : 'hidden' }} class="w-full">
            <x-base.alert
            class="flex justify-between mb-2"
            variant="primary"
            hidden
        >

        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                {{ session('medical_changes_saved')  }}
            <x-base.alert.dismiss-button
                class="text-white"
                type="button"
                aria-label="Close"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
            </x-base.alert.dismiss-button>
        </x-base.alert>
    </div>
    </div>
</div>
    

<script src="https://cdn.jsdelivr.net/npm/flowbite@2.4.1/dist/flowbite.min.js"></script>
<script>
    document.addEventListener('livewire:initialized', () => {
        console.log('JS comp initialized');
        let component = @this;
        const impalaInductionTrainingDate = document.getElementById('impalaInductionTrainingDate');
        const impalaDriversCodeOfConductDate = document.getElementById('impalaDriversCodeOfConductDate');
        const impalaSafeDrivingDate = document.getElementById('impalaSafeDrivingDate');
        const impalaChaufferDriversCourseDate = document.getElementById('impalaChaufferDriversCourseDate');
        const impalaCMEDVipDrivingCourseDate = document.getElementById('impalaCMEDVipDrivingCourseDate');
        const impalaDriversInHouseTrainingDate = document.getElementById('impalaDriversInHouseTrainingDate');

    
        impalaInductionTrainingDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('impalaInductionTrainingDate', event.target.value);
            component.set('impalaInductionTrainingId', 1);
        });

        impalaDriversCodeOfConductDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('impalaDriversCodeOfConductDate', event.target.value);
            component.set('impalaDriversCodeOfConductId', 2)   
        });

        impalaSafeDrivingDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('impalaSafeDrivingDate', event.target.value);
            component.set('impalaSafeDrivingId', 3)  
        });

        impalaChaufferDriversCourseDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('impalaChaufferDriversCourseDate', event.target.value);
            component.set('impalaChaufferDriversCourseId', 4);
        });

        impalaCMEDVipDrivingCourseDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('impalaCMEDVipDrivingCourseDate', event.target.value);
            component.set('impalaCMEDVipDrivingCourseId', 5);
            
        });

        impalaDriversInHouseTrainingDate.addEventListener('blur', (event) => {
            console.log(event.target.value);
            component.set('impalaDriversInHouseTrainingDate', event.target.value);
            component.set('isImpalaDriversInHouseTrainingId', 6);
        });
    });
</script>
</div>
@pushOnce('styles')
@vite('resources/css/vendors/filepond.css')
@endPushOnce

@pushOnce('vendors')
@vite('resources/js/vendors/filepond.js')
@endPushOnce