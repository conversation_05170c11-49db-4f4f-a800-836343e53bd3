<div>

    <div class="flex items-center mt-8">
        <h2 class="mr-auto text-lg font-medium intro-y">Driver Onboarding</h2>
    </div>
    <!-- BEGIN: Wizard Layout -->
    <div class="py-10 mt-5 intro-y box sm:py-20">
        <div class="flex flex-row">
          <div
            class="relative w-full flex flex-col justify-center px-5 before:absolute before:bottom-0 before:top-0 before:mt-4 before:hidden before:h-[3px] before:w-[69%] before:bg-slate-100 before:dark:bg-darkmode-400 sm:px-20 lg:flex-row before:lg:block">
            <div class="z-10 flex items-center flex-1 intro-x lg:block lg:text-center">
                <x-base.button
                    class="w-10 h-10 rounded-full"
                    variant="primary"
                >
                    1
                </x-base.button>
                <div class="ml-3 text-base font-medium lg:mx-auto lg:mt-3 lg:w-32">
                    Driver Details
                </div>
            </div>

            <div class="z-10 flex items-center flex-1 mt-5 intro-x lg:mt-0 lg:block lg:text-center">
                <x-base.button
                    class="h-10 w-10 rounded-full {{ $currentStep >= 2 ? '' : 'bg-slate-100 text-slate-500 dark:border-darkmode-400 dark:bg-darkmode-400' }}"
                    variant="{{ $currentStep >= 2 ? 'primary' : 'secondary' }}"

                >
                    2
                </x-base.button>
                <div class="ml-3 text-base text-slate-600 dark:text-slate-400 lg:mx-auto lg:mt-3 lg:w-32">
                    Next Of Kin
                </div>
            </div>
            <div class="z-10 flex items-center flex-1 mt-5 intro-x lg:mt-0 lg:block lg:text-center">
                <x-base.button
                    class="h-10 w-10 rounded-full {{ $currentStep >= 3 ? '' : 'bg-slate-100 text-slate-500 dark:border-darkmode-400 dark:bg-darkmode-400' }}"
                    variant="{{ $currentStep >= 3 ? 'primary' : 'secondary' }}" >
                    3
                </x-base.button>
                <div class="ml-3 text-base text-slate-600 dark:text-slate-400 lg:mx-auto lg:mt-3 lg:w-32">
                    Licensing
                </div>
            </div>
              <div class="z-10 flex items-center flex-1 mt-5 intro-x lg:mt-0 lg:block lg:text-center">
                  <x-base.button
                      class="h-10 w-10 rounded-full {{ $currentStep >= 4 ? '' : 'bg-slate-100 text-slate-500 dark:border-darkmode-400 dark:bg-darkmode-400' }}"
                      variant="{{ $currentStep >= 4 ? 'primary' : 'secondary' }}"
                  >
                      4
                  </x-base.button>
                  <div class="ml-3 text-base text-slate-600 dark:text-slate-400 lg:mx-auto lg:mt-3 lg:w-32">
                      Impala Training
                  </div>
              </div>
            <div class="z-10 flex items-center flex-1 mt-5 intro-x lg:mt-0 lg:block lg:text-center">
                <x-base.button
                    class="h-10 w-10 rounded-full {{ $currentStep >= 5 ? '' : 'bg-slate-100 text-slate-500 dark:border-darkmode-400 dark:bg-darkmode-400' }}"
                    variant="{{ $currentStep >= 5 ? 'primary' : 'secondary' }}"
                >
                    5
                </x-base.button>
                <div class="ml-3 text-base text-slate-600 dark:text-slate-400 lg:mx-auto lg:mt-3 lg:w-32">
                    Medical and Clearance
                </div>
            </div>
            <div class="z-10 flex items-center flex-1 mt-5 intro-x lg:mt-0 lg:block lg:text-center">
                <x-base.button
                    class="h-10 w-10 rounded-full {{ $currentStep == 6 ? '' : 'bg-slate-100 text-slate-500 dark:border-darkmode-400 dark:bg-darkmode-400' }}"
                    variant="{{ $currentStep == 6 ? 'primary' : 'secondary' }}"
                >
                    6
                </x-base.button>
                <div class="ml-3 text-base text-slate-600 dark:text-slate-400 lg:mx-auto lg:mt-3 lg:w-32">
                    Banking Details
                </div>
            </div>
           </div>
        </div>
        <div class="px-5 pt-10 mt-10 border-t border-slate-200/60 dark:border-darkmode-400 sm:px-20">

                 <div class="{{ $currentStep == 1 ? '' : 'hidden'}}">
                    <div class="text-base font-medium">Driver details</div>
                    <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
                        <div class="col-span-12 intro-y sm:col-span-6">
                            <x-impala-components.form.primary-text-input
                                wire:model="driverFirstname"
                                name="driverFirstname"
                                label="First Name"
                                placeholder="Driver firstname"  />

                            <div class="mt-4">
                                <x-impala-components.form.date-picker
                                    wire:model="driverDOB"
                                    id="driverDOB"
                                    name="driverDOB"
                                    label="Date Of Birth"
                                    value="Date of birth"
                                    readonly="readonly"
                                    onkeydown="return false"
                                    onpaste="return false"
                                    ondrop="return false"
                                    autocomplete="off"
                                    x-on:keydown.prevent=""
                                    />
                                @error('driverDOB')
                                    <span class="mt-2 text-sm text-red-600">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="mt-4">
                                <x-impala-components.form.primary-text-input
                                    wire:model="driverMobileNumber"
                                    name="driverMobileNumber"
                                    label="Mobile Number"
                                    placeholder="Enter mobile number"  />

                            </div>
                            <div class="mt-8">
                                <x-impala-components.form.primary-text-input
                                    wire:model="driverEmail"
                                    name="driverEmail"
                                    label="Email"
                                    placeholder="Enter email address"  />
                            </div>

                            <div class="mt-4">
                                <x-impala-components.form.primary-text-input
                                    wire:model="nationalIdNumber"
                                    name="nationalIdNumber"
                                    label="National ID Number"
                                    placeholder="National id number"  />
                            </div>

                            <div class="mt-4">
                                <x-impala-components.form.file_pond_input
                                title="Upload national Id"
                                name="nationalIdFile"
                            />
                          
                                {{-- <x-impala-components.form.file-upload
                                    :image="$nationalIdFile"
                                    wire:model="nationalIdFile"
                                    name="nationalIdFile"
                                    title="Upload Id"
                                    label="Upload Id" /> --}}
                            </div>

                            <div class="mt-4">
                                <x-impala-components.form.file_pond_input
                                title="Upload Driver Photo"
                                name="driverProfilePhotoFile"
                            />
                            </div>
                            {{-- <div class="mt-4">
                                <x-impala-components.form.file-upload
                                    :image="$driverProfilePhotoFile"
                                    wire:model="driverProfilePhotoFile"
                                    name="driverProfilePhotoFile"
                                    title="Upload Driver Photo"
                                    label="Upload Driver Photo" />
                            </div> --}}
                        </div>
                        <div class="col-span-12 intro-y sm:col-span-6">

                            <x-impala-components.form.primary-text-input wire:model="driverLastname"  name="driverLastname" label="Last Name" placeholder="Driver lastname"  />
                            <div class="mt-4">
                                <x-impala-components.form.select-dropdown
                                    wire:model="selectedGenderId"
                                    id="selectedGenderId"
                                    name="selectedGenderId"
                                    placeholder="Gender"
                                    label="Select Gender"
                                    :selected="$selectedGenderId"
                                    :options="$genders" />

                            </div>
                            <div class="mt-4">
                                <x-impala-components.form.primary-text-input
                                    wire:model="driverSecondMobileNumber"
                                    name="driverSecondMobileNumber"
                                    label="Second Mobile Number"
                                    placeholder="Enter mobile number"  />
                            </div>
                            <div class="mt-4">
                                <x-impala-components.form.textarea
                                    wire:model="driverAddress"
                                    label="Physical Address"
                                    name="driverAddress"
                                    placeholder="Enter physical address" />
                            </div>

                            <div class="mt-4">
                                <x-impala-components.form.primary-text-input
                                    wire:model="passportNumber"
                                    name="passportNumber"
                                    label="Passport Number"
                                    placeholder="Passport number"  />
                            </div>

                            {{-- <div class="mt-4">
                                <x-impala-components.form.file-upload
                                    :image="$passportFile"
                                    wire:model="passportFile"
                                    name="passportFile"
                                    title="Upload passport"
                                    label="Upload Passport" />
                            </div> --}}

                            <div class="mt-4">
                                <x-impala-components.form.file_pond_input
                                title="Upload passport"
                                name="passportFile"
                            />
                            </div>
                        </div>
                    </div>
                </div>

                 <div class="{{ $currentStep == 2 ? '' : 'hidden'}}">
                          <div class="text-base font-medium">Next of kin details</div>
                            <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
                                <div class="col-span-12 intro-y sm:col-span-6">
                                    <x-impala-components.form.primary-text-input
                                        wire:model="nextOfKinFullname"
                                        name="nextOfKinFullname"
                                        label="Full Name"
                                        placeholder="Next of kin full name"  />

                                    <div class="mt-4">
                                        <x-impala-components.form.textarea
                                            wire:model="nextOfKinAddress"
                                            label="Physical Address"
                                            name="nextOfKinAddress"
                                            placeholder="Address" />
                                    </div>
                                </div>
                                <div class="col-span-12 intro-y sm:col-span-6">
                                    <x-impala-components.form.primary-text-input
                                        wire:model="nextOfKinMobileNumber"
                                        name="nextOfKinMobileNumber"
                                        label="Mobile Number"
                                        placeholder="Mobile number"  />

                                </div>
                           </div>
                        </div>


                <div class="{{ $currentStep == 3 ? '' : 'hidden'  }} ">
                    <div class="text-base font-medium">Licensing information</div>
                    <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
                        <div class="col-span-12 intro-y sm:col-span-6">
                            <x-impala-components.form.primary-text-input
                                wire:model="drivingExperience"
                                type="number"
                                name="drivingExperience"
                                label="Driving Experience"
                                placeholder="Driving experience"  />

                            <div class="mt-4">
                                <x-impala-components.form.date-picker
                                    wire:model="licenceIssueDate"
                                    id="licenceIssueDate"
                                    name="licenceIssueDate"
                                    label="Licence Issue Date"
                                    value="Licence Issue Date"
                                    onkeydown="return false"
                                    onpaste="return false"
                                    ondrop="return false"
                                    autocomplete="off"
                                    x-on:keydown.prevent=""
                                />
                                @error('licenceIssueDate')
                                    <span class="text-sm text-red-600">{{ $message }}</span>
                                @enderror
                            </div>

                            

                            {{-- <div class="mt-4">
                                <x-impala-components.form.file-upload
                                    :image="$licenceFile"
                                    wire:model="licenceFile"
                                    title="Upload Licence"
                                    label="Upload Drivers
                                 Licence" name="licenceFile" />
                            </div> --}}

                            <div class="mt-4">
                                <x-impala-components.form.file_pond_input
                                title="Upload Drivers Licence"
                                name="licenceFile"
                            />
                            </div>

                            {{-- <div class="mt-4">
                                <x-impala-components.form.file-upload
                                    :image="$internationalDrivingLicenceFile"
                                    wire:model="internationalDrivingLicenceFile"
                                    name="internationalDrivingLicenceFile"
                                    title="Inter driving licence"
                                    label="Inter driving licence"
                                />
                            </div> --}}

                            <div class="mt-4">
                                <x-impala-components.form.file_pond_input
                                title="Upload International Drivers Licence"
                                name="internationalDrivingLicenceFile"
                            />
                            </div>


                        </div>
                        <div class="col-span-12 intro-y sm:col-span-6">
                            <x-impala-components.form.primary-text-input
                                wire:model="driversLicenceNumber"
                                name="driversLicenceNumber"
                                label="Drivers licence number"
                                placeholder="Licence number"  />
                            <div class="mt-4">
                                <x-impala-components.form.select-dropdown
                                    wire:model="selectedClassId"
                                    id="selectedClassId"
                                    name="selectedClassId"
                                    placeholder="Drivers licence class"
                                    label="Select class"
                                    selected="$selectedClassId"
                                    :options="$licenceClasses" />
                            </div>

                            {{-- <div class="mt-4">
                                <x-impala-components.form.file-upload
                                    :image="$defencelicenceFile"
                                    name="defensiveDriversLicence"
                                    wire:model="defencelicenceFile"
                                    title="Upload Defensive Licence"
                                    label="Upload Defensive
                                 Licence" />
                            </div> --}}

                            <div class="mt-4">
                                <x-impala-components.form.file_pond_input
                                title="Upload Defensive Licence"
                                name="defencelicenceFile"
                            />
                            </div>
                            <div class="mt-4">
                                <x-impala-components.form.date-picker
                                    wire:model="defencelicenceExpiryDate"
                                    name="defencelicenceExpiryDate"
                                    id="defencelicenceExpiryDate"
                                    label="Defensive Licence Expiry Date" 
                                    value="Defensive Licence Expiry Date"
                                    onkeydown="return false"
                                    onpaste="return false"
                                    ondrop="return false"
                                    autocomplete="off"
                                    x-on:keydown.prevent=""
                                    />
                            </div>
                            @error('defencelicenceExpiryDate')
                                <span class="text-sm text-red-600">{{ $message }}</span>
                            @enderror

                            {{-- <div class="mt-4">
                                <x-impala-components.form.file-upload
                                    :image="$firstAidCertificateFile"
                                    wire:model="firstAidCertificateFile"
                                    name="firstAidCertificateFile"
                                    title="First Aid Certificate"
                                    label="First Aid Certificate"
                                />
                            </div> --}}
                            <div class="mt-4">
                                <x-impala-components.form.file_pond_input
                                title="First Aid Certificate"
                                name="firstAidCertificateFile"
                            />
                            </div>
                        </div>
                    </div>
                </div>



            <div class="{{ $currentStep == 4 ? '' : 'hidden'  }} ">
                <div class="text-base font-medium">Impala Training</div>
                <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
                    <div class="col-span-12 intro-y sm:col-span-6">

                        @foreach($trainingTypes as $trainingType)
                            <div class="mt-4" wire:key="{{ $trainingType->id }}">
                                    <x-impala-components.form.check-box
                                        value="{{ $trainingType->name }}"
                                        wire:click="toggleTraining(
                                        {{ $trainingType->id }},
                                        $event.target.checked)"
                                        name=""
                                        label="{{ $trainingType->name }}"
                                        value="{{ $trainingType->id }}"
                                    />

                                @if($trainingType->id == 1)
                                    <div {{ $isImpalaInductionTrainingChecked ? '' : 'hidden' }}>
                                        <x-impala-components.form.date-picker
                                            wire:model="impalaInductionTrainingDate"
                                            name="impalaInductionTrainingDate"
                                            id="impalaInductionTrainingDate"
                                            value="Date of completion"
                                            label="Date of completion"
                                            onkeydown="return false"
                                            onpaste="return false"
                                            ondrop="return false"
                                            autocomplete="off"
                                            x-on:keydown.prevent="" />
                                    </div>
                                @elseif($trainingType->id == 2)
                                    <div  {{ $isImpalaDriversCodeOfConductChecked ? '' : 'hidden' }}>
                                        <x-impala-components.form.date-picker
                                            wire:model="impalaDriversCodeOfConductDate"
                                            name="impalaDriversCodeOfConductDate"
                                            id="impalaDriversCodeOfConductDate"
                                            value="Date of completion"
                                            label="Date of completion"
                                            onkeydown="return false"
                                            onpaste="return false"
                                            ondrop="return false"
                                            autocomplete="off"
                                            x-on:keydown.prevent=""
                                            />
                                    </div>

                            @elseif($trainingType->id == 3)
                                <div {{ $isImpalaSafeDrivingChecked ? '' : 'hidden' }}>
                                    <x-impala-components.form.date-picker
                                        wire:model="impalaSafeDrivingDate"
                                        name="impalaSafeDrivingDate"
                                        id="impalaSafeDrivingDate"
                                        value="Date of completion"
                                        label="Date of completion"
                                        onkeydown="return false"
                                        onpaste="return false"
                                        ondrop="return false"
                                        autocomplete="off"
                                        x-on:keydown.prevent=""
                                    />
                                </div>

                            @elseif($trainingType->id == 4)
                                    <div {{ $isImpalaChaufferDriversCourseChecked ? '' : 'hidden' }}>
                                        <x-impala-components.form.date-picker
                                            wire:model="impalaChaufferDriversCourseDate"
                                            name="impalaChaufferDriversCourseDate"
                                            id="impalaChaufferDriversCourseDate"
                                            value="Date of completion"
                                            label="Date of completion"
                                            onkeydown="return false"
                                            onpaste="return false"
                                            ondrop="return false"
                                            autocomplete="off"
                                            x-on:keydown.prevent=""
                                                />
                                    </div>

                            @elseif($trainingType->id == 5)
                                    <div {{ $isImpalaCMEDVipDrivingCourseChecked ? '' : 'hidden' }}>
                                        <x-impala-components.form.date-picker
                                            wire:model="impalaCMEDVipDrivingCourseDate"
                                            name="impalaCMEDVipDrivingCourseDate"
                                            id="impalaCMEDVipDrivingCourseDate"
                                            value="Date of completion"
                                            label="Date of completion"
                                            onkeydown="return false"
                                            onpaste="return false"
                                            ondrop="return false"
                                            autocomplete="off"
                                            x-on:keydown.prevent=""
                                        />
                                    </div>

                            @elseif($trainingType->id == 6)
                                    <div  {{ $isImpalaDriversInHouseTrainingChecked ? '' : 'hidden' }}>
                                        <x-impala-components.form.date-picker
                                            wire:model="impalaDriversInHouseTrainingDate"
                                            name="impalaDriversInHouseTrainingDate"
                                            id="impalaDriversInHouseTrainingDate"
                                            value="Date of completion"
                                            label="Date of completion"
                                            onkeydown="return false"
                                            onpaste="return false"
                                            ondrop="return false"
                                            autocomplete="off"
                                            x-on:keydown.prevent=""
                                        />
                                    </div>
                            @endif

                        </div>

                    @endforeach
                </div>

            </div>
        </div>

        <div class="{{ $currentStep == 5 ? '' : 'hidden'  }} ">
                <div class="text-base font-medium">Medical and Police Clearance</div>
                <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
                    <div class="col-span-12 intro-y sm:col-span-6">
                        <div class="mt-4">
                            <x-impala-components.form.date-picker
                                wire:model="medicalTestsIssueDate"
                                id="medicalTestsIssueDate"
                                name="medicalTestsIssueDate"
                                label="Medical Test Issue Date" 
                                value="Medical Test Issue Date"
                                onkeydown="return false"
                                onpaste="return false"
                                ondrop="return false"
                                autocomplete="off"
                                x-on:keydown.prevent=""
                                />
                        </div>
                        @error('medicalTestsIssueDate')
                            <span class="text-sm text-red-600">{{ $message }}</span>
                        @enderror

                        {{-- <div class="mt-4">
                            <x-impala-components.form.file-upload
                                :image="$medicalTestFile"
                                wire:model="medicalTestFile"
                                name="medicalTestFile"
                                title="Upload Medical Test"
                                label="Upload Drivers
                                 Licence"  />
                        </div> --}}

                        <div class="mt-4">
                            <x-impala-components.form.file_pond_input
                            title="Upload Medical Test"
                            name="medicalTestFile"
                        />
                        </div>

                        <div class="mt-4">
                            <x-impala-components.form.date-picker
                                wire:model="impalaCeritificationDate"
                                id="impalaCeritificationDate"
                                name="impalaCeritificationDate"
                                label="Impala Certificate Issue Date" 
                                value="Impala Certificate Issue Date"
                                onkeydown="return false"
                                onpaste="return false"
                                ondrop="return false"
                                autocomplete="off"
                                x-on:keydown.prevent=""
                            />
                            @error('impalaCeritificationDate')
                                <span class="text-sm text-red-600">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-span-12 intro-y sm:col-span-6">
                        <div class="mt-4">
                            <x-impala-components.form.date-picker
                                wire:model="policeClearanceIssueDate"
                                id="policeClearanceIssueDate"
                                name="policeClearanceIssueDate"
                                label="Police Clearance Issue Date"
                                value="Police Clearance Issue Date"
                                onkeydown="return false"
                                onpaste="return false"
                                ondrop="return false"
                                autocomplete="off"
                                x-on:keydown.prevent=""
                                />
                        </div>
                        @error('policeClearanceIssueDate')
                            <span class="text-sm text-red-600">{{ $message }}</span>
                        @enderror

                        {{-- <div class="mt-4">
                            <x-impala-components.form.file-upload
                                :image="$policeClearanceFile"
                                wire:model="policeClearanceFile"
                                name="policeClearanceFile"
                                title="Upload Police Clearence"
                                label="Upload Police
                                 Clearance"  />
                        </div> --}}

                        <div class="mt-4">
                            <x-impala-components.form.file_pond_input
                            title="Upload Police Clearence"
                            name="policeClearanceFile"
                        />
                        </div>

                        {{-- <div class="mt-4">
                            <x-impala-components.form.file-upload
                                :image="$proofOfResidenceFile"
                                wire:model="proofOfResidenceFile"
                                name="proofOfResidenceFile"
                                title="Proof Of Residence"
                                label="Upload Of Residence"  />
                        </div> --}}
                        <div class="mt-4">
                            <x-impala-components.form.file_pond_input
                            title="Upload Proof Of Residence"
                            name="proofOfResidenceFile"
                        />
                        </div>
                    </div>
                </div>
        </div>

            <div class="{{ $currentStep == 6 ? '' : 'hidden'  }} ">
                <div class="text-base font-medium">Banking Details</div>
                <div class="grid grid-cols-12 gap-4 mt-5 gap-y-5">
                    <div class="col-span-12 intro-y sm:col-span-4">

                        <div class="mt-4">
                            <x-impala-components.form.select-dropdown
                                wire:model="selectedBankId"
                                id="selectedBankId"
                                name="selectedBankId"
                                placeholder="Bank"
                                label="Select Bank"
                                :selected="$selectedBankId"
                                :options="$banks" />

                        </div>

                        <div class="mt-4">
                            <x-impala-components.form.primary-text-input
                                wire:model="accountNumber"
                                name="acocuntNumber"
                                label="Account Number"
                                placeholder="Bank account number"  />
                        </div>
                    </div>

                    <div class="col-span-12 intro-y sm:col-span-4">
                        <div class="mt-4">
                            <x-impala-components.form.primary-text-input
                                wire:model="ecocashNumber"
                                name="ecocashNumber"
                                label="Ecocash Number"
                                placeholder="Ecocash number"  />
                        </div>

                        <div class="mt-4">
                            <x-impala-components.form.primary-text-input
                                wire:model="innBucksNumber"
                                name="innBucksNumber"
                                label="Innbucks Number"
                                placeholder="Inbucks number"  />
                        </div>

                      
                        <div class="mt-4">
                    {{-- @if (session('driver_created'))
                            <x-base.alert
                            class="flex justify-between mb-2"
                            variant="primary"
                        >

                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                Driver profile created successfully
                                <x-base.alert.dismiss-button
                                    class="text-white"
                                    type="button"
                                    aria-label="Close"
                                >

                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                            </x-base.alert.dismiss-button>
                          </x-base.alert>
                        @endif --}}
                    </div>

                        <div {{ session('driver_created') ? '' : 'hidden' }}>
                            <x-base.alert
                            class="flex justify-between mb-2"
                            variant="primary"
                            hidden
                        >

                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                        {{ session('driver_created')  }}
                                    <x-base.alert.dismiss-button
                                        wire:click="closeAlert"
                                        class="text-white"
                                        type="button"
                                        aria-label="Close"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                    </x-base.alert.dismiss-button>
                        </x-base.alert>
                        </div>
                    </div>
                </div>
            </div>

                <!-- Replace the loading dialog -->
            <div wire:loading wire:target="save" 
                class="fixed inset-0 z-50 flex items-center justify-center transition-opacity duration-300 ease-in-out bg-black bg-opacity-75">
                <div class="bg-white p-6 rounded-lg shadow-xl w-1/5 min-w-[300px] transform scale-100 transition-transform duration-300 flex flex-col items-center">
                    <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mb-2"></div>
                    <p class="text-base font-medium text-gray-700">Creating driver profile...</p>
                </div>
            </div>

            <div class="flex items-center justify-center col-span-12 mt-5 intro-y sm:justify-end">
                <x-base.button
                    wire:click="previousStep"
                    class="w-24"
                    variant="secondary"
                    :disabled="$isSaving"
                >
                    Previous
                </x-base.button>
                <x-base.button
                    wire:click="{{ $currentStep == 6 ? 'save' : 'nextStep' }}"
                    class="w-24 ml-2"
                    variant="primary"
                    :disabled="$isSaving"
                >
                    {{ $currentStep == 6 ? 'Submit' : 'Next' }}
                </x-base.button>
            </div>
        </div>
    </div>
</div>
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.4.1/dist/flowbite.min.js"></script>
    <script>
        document.addEventListener('livewire:initialized', () => {
            console.log('JS comp initialized');
            let component = @this;
            const dateOfBirth = document.getElementById('driverDOB');
            const licenceIssueDate = document.getElementById('licenceIssueDate');
            const defencelicenceExpiryDate = document.getElementById('defencelicenceExpiryDate');
            const medicalTestsIssueDate = document.getElementById('medicalTestsIssueDate');
            const policeClearanceIssueDate = document.getElementById('policeClearanceIssueDate');
            const impalaInductionTrainingDate = document.getElementById('impalaInductionTrainingDate');
            const impalaDriversCodeOfConductDate = document.getElementById('impalaDriversCodeOfConductDate');
            const impalaSafeDrivingDate = document.getElementById('impalaSafeDrivingDate');
            const impalaChaufferDriversCourseDate = document.getElementById('impalaChaufferDriversCourseDate');
            const impalaCMEDVipDrivingCourseDate = document.getElementById('impalaCMEDVipDrivingCourseDate');
            const impalaDriversInHouseTrainingDate = document.getElementById('impalaDriversInHouseTrainingDate');

            dateOfBirth.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('driverDOB', event.target.value);
            });

            licenceIssueDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('licenceIssueDate', event.target.value);
            });

            defencelicenceExpiryDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('defencelicenceExpiryDate', event.target.value);
            });

            medicalTestsIssueDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('medicalTestsIssueDate', event.target.value);
            });

            policeClearanceIssueDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('policeClearanceIssueDate', event.target.value);
            });


            impalaInductionTrainingDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('impalaInductionTrainingDate', event.target.value);
            });

             impalaCeritificationDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('impalaCeritificationDate', event.target.value);
            });

            impalaDriversCodeOfConductDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('impalaDriversCodeOfConductDate', event.target.value);
            });

            impalaSafeDrivingDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('impalaSafeDrivingDate', event.target.value);
            });

            impalaChaufferDriversCourseDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('impalaChaufferDriversCourseDate', event.target.value);
            });

            impalaCMEDVipDrivingCourseDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('impalaCMEDVipDrivingCourseDate', event.target.value);
            });

            impalaDriversInHouseTrainingDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('impalaDriversInHouseTrainingDate', event.target.value);
            });
        });
    </script>
    <script>
    document.addEventListener('livewire:initialized', () => {
        Livewire.on('success', (message) => {
            // Your success alert logic here
            alert(message); // Or use your preferred alert/notification system
        });
        
        Livewire.on('error', (message) => {
            // Your error alert logic here
            alert(message); // Or use your preferred alert/notification system
        });
    });
</script>
    
</div>
@pushOnce('styles')
    @vite('resources/css/vendors/filepond.css')
@endPushOnce

@pushOnce('vendors')
    @vite('resources/js/vendors/filepond.js')
@endPushOnce