<div>
    <div class="mt-4">
        <x-impala-components.form.date-picker
            wire:model="accidentDate2"
            id="accidentDate2"
            name="accidentDate2"
            label="Date of accident"
            value="Choose date of accident"
            />
    </div>

    <div class="mt-4">
        <x-impala-components.form.select-dropdown
            wire:model="selectedAccidentType"
            id="selectedAccidentType"
            name="selectedAccidentType"
            placeholder="Select accident type"
            label="Select accident type"
            :selected="$selectedAccidentType"
            :options="$filteredAccidentTypes" />

    </div>

    <div class="mt-4">
        <x-impala-components.form.textarea
            wire:model="accidentDetails"
            label="Accident comments"
            name="accidentDetails"
            placeholder="Enter accident comments" />
    </div>

    <div class="mt-4">
        <x-impala-components.form.file_pond_input
        title="Upload police or accident report"
        name="accidentPoliceReport"
    />

     </div>

     <div class="mt-4">
        <x-impala-components.form.select-dropdown
            wire:model="selectedStatusId"
            id="selectedStatusId"
            name="selectedStatusId"
            placeholder="Select accident type"
            label="Select accident type"
            :selected="$selectedStatusId"
            :options="$filtteredAccidentStatus" />

    </div>

    <div class="mt-8">
        <x-impala-components.form.primary-button
        wire:click="save"
        name="Save Report" />
    </div>

    <div {{ session('report_saved') ? '' : 'hidden' }} class="w-full pt-4">
        <x-base.alert
        class="flex justify-between mb-2"
        variant="primary"
        hidden
    >

    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
            {{ session('report_saved')  }}
        <x-base.alert.dismiss-button
            class="text-white"
            type="button"
            aria-label="Close"
        >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
        </x-base.alert.dismiss-button>
    </x-base.alert>
</div>

<script src="https://cdn.jsdelivr.net/npm/flowbite@2.4.1/dist/flowbite.min.js"></script>
    <script>
        document.addEventListener('livewire:initialized', () => {
            console.log('JS comp initializedEEEE');
            let component = @this;
            const accidentDate = document.getElementById('accidentDate2');
          
            accidentDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('accidentDate2', event.target.value);
            });

        });
    </script>
@pushOnce('styles')
    @vite('resources/css/vendors/filepond.css')
@endPushOnce

@pushOnce('vendors')
    @vite('resources/js/vendors/filepond.js')
@endPushOnce
