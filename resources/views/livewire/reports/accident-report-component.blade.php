<div class="grid grid-cols-12 gap-6">
    <x-impala-components.overlay.view-report-details>
        @slot('content') 
        <div class="text-base font-medium">Report Details</div>
        <div class="w-full border-t border-gray-200 mt-2"></div>

        <div class="flex pt-4 flex-1 items-center justify-center px-5 lg:justify-start">
            <div class="image-fit relative h-20 w-20 flex-none sm:h-24 sm:w-24 lg:h-32 lg:w-32">
                <img
                    class="rounded-full"
                    src="{{ $driverProfile ?  asset('storage/' . $driverProfile) : asset('driver.png') }}"
                    alt="Midone - Tailwind Admin Dashboard Template"
                />
            </div>
            <div class="ml-5">
                <div class="w-24 truncate text-lg font-medium sm:w-40 sm:whitespace-normal">
                   {{ $editDriverDisplayName }}
                </div>
                <div class="text-slate-500">
                    {{  $driversLicenceNumber  }}    
                </div>
            </div>
        </div>
        <div class="w-full border-t border-gray-200 mt-4"></div>
        <div class="ml-4 mt-4 mr-auto">
            <a
                class="font-medium"
                href=""
            >
                Date of accident
            </a>
            <div class="mr-5 text-slate-500 sm:mr-5">
                {{  $editAccidentDate  }}
            </div>
        </div>
        <div class="ml-4 mt-4 mr-auto">
            <a
                class="font-medium"
                href=""
            >
                Nature of accident
            </a>
            <div class="mr-5 text-slate-500 sm:mr-5">
               {{ $viewAccidentType }}
            </div>
        </div>
        <div class="ml-4 mt-4 mr-auto">
            <a
                class="font-medium"
                href=""
            >
                Status
            </a>
            <div class="mr-5 text-slate-500 sm:mr-5">
                {{ $viewAccidentStatus }}
            </div>
        </div>



        
        @if ($viewReportComments)
        <div class="ml-4 mt-4 mr-auto">
            <a
                class="font-medium"
                href=""
            >
                Comments
            </a>
            <div class="mr-5 text-slate-500 sm:mr-5">
               {{ $viewReportComments  }}
            </div>
        </div>
        @endif
        
        @endslot

        @slot('footer')
        <div class="px-5 py-3 mt-4 text-right border-t border-slate-200/60 dark:border-darkmode-400">
            <button 
             x-data
            x-on:click="$dispatch('close-modal')"
             type="button" 
             class="transition duration-200 border mt-4 shadow-sm inline-flex items-center justify-center py-2 px-3 rounded-md font-medium cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed border-secondary text-slate-500 dark:border-darkmode-100/40 dark:text-slate-300 [&amp;:hover:not(:disabled)]:bg-secondary/20 [&amp;:hover:not(:disabled)]:dark:bg-darkmode-100/10 w-20 mr-1 w-20 mr-1">Close</button>
            
        </div>
        @endslot
        
    </x-impala-components.overlay.view-report-details>
    <div class="col-span-12 lg:col-span-6 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-6">
        <div class="text-base font-medium">Create accident report</div>
        <div class="w-full border-t border-gray-200 mt-2"></div>
        
    
        @livewire('reports.search-driver-component')
        @error('driver_id') <span class="text-danger">{{ $message }}</span> @enderror

        <div class="mt-4">
            <x-impala-components.form.date-picker
                wire:model="accidentDate"
                id="accidentDate"
                name="accidentDate"
                label="Date of accident"
                value="Choose date of accident"
                />
            @error('accidentDate') <span class="text-danger">{{ $message }}</span> @enderror
        </div>

        <div class="mt-4">
            <x-impala-components.form.select-dropdown
                wire:model="selectedAccidentType"
                id="selectedAccidentType"
                name="selectedAccidentType"
                placeholder="Select accident type"
                label="Select accident type"
                :selected="$selectedAccidentType"
                :options="$filteredAccidentTypes" />
            @error('selectedAccidentType') <span class="text-danger">{{ $message }}</span> @enderror

        </div>

        <div class="mt-4">
            <x-impala-components.form.textarea
                wire:model="accidentDetails"
                label="Accident comments"
                name="accidentDetails"
                placeholder="Enter accident comments" />
            @error('accidentDetails') <span class="text-danger">{{ $message }}</span> @enderror
        </div>

        <div class="mt-4">
            <x-impala-components.form.file_pond_input
            title="Upload police or accident report"
            name="accidentPoliceReport"
        />

         </div>

         <div class="mt-4">
            <x-impala-components.form.select-dropdown
                wire:model="selectedStatusId"
                id="selectedStatusId"
                name="selectedStatusId"
                placeholder="Select accident status"
                label="Select accident status"
                :selected="$selectedStatusId"
                :options="$filtteredAccidentStatus" />
            @error('selectedStatusId') <span class="text-danger">{{ $message }}</span> @enderror
        </div>

        <div class="mt-8">
            <x-impala-components.form.primary-button
            wire:click="save"
            name="Save Report" />
        </div>

        <div {{ session('report_saved') ? '' : 'hidden' }} class="w-full pt-4">
            <x-base.alert
            class="flex justify-between mb-2"
            variant="primary"
            hidden
        >

        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                {{ session('report_saved')  }}
            <x-base.alert.dismiss-button
                class="text-white"
                type="button"
                aria-label="Close"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
            </x-base.alert.dismiss-button>
        </x-base.alert>
     </div>
    </div>

    <div class="col-span-12 lg:col-span-6 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-6">
        <div class="text-base font-medium">Reports</div>
        <!-- Delete Confirmation Modal -->
        <div id="delete-modal" x-data="{ show: false }" 
            x-show="show"
            x-on:show-delete-modal.window="show = true"
            x-on:hide-delete-modal.window="show = false"
            class="modal" 
            tabindex="-1" 
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body p-0">
                        <div class="p-5 text-center">
                            <i data-lucide="x-circle" class="w-16 h-16 text-danger mx-auto mt-3"></i>
                            <div class="text-3xl mt-5">Confirm deletion？</div>
                            <div class="text-slate-500 mt-2">Are you sure you want to delete this incident report? This action cannot be undone.</div>
                        </div>
                        <div class="px-5 pb-8 text-center">
                            <button type="button" data-tw-dismiss="modal" class="btn btn-outline-secondary w-24 mr-1">Cancel</button>
                            <button wire:click="deleteReport" wire:loading.attr="disabled" type="button" class="btn btn-danger w-24">
                                <span wire:loading.remove wire:target="deleteReport">delete</span>
                                <span wire:loading wire:target="deleteReport">Deleting...</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div {{ session('report_deleted') ? '' : 'hidden' }} class="w-full pt-4">
            <x-base.alert
            class="flex justify-between mb-2"
            variant="danger"
            hidden
        >

        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                {{ session('report_deleted')  }}
            <x-base.alert.dismiss-button
                class="text-white"
                type="button"
                aria-label="Close"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
            </x-base.alert.dismiss-button>
        </x-base.alert>
     </div>
        <div class="w-full border-t border-gray-200 mt-2"></div>
        <div {{ $editReportId != 0 ? 'hidden' : '' }}>
        <div class="flex flex-wrap items-center justify-center p-5 gap-2 lg:flex-nowrap">
            <div class="mb-4 mr-auto w-auto lg:mb-0 lg:w-auto">
                <div>
                    <x-impala-components.form.date-picker
                        wire:model="filterAccidentDate"
                        id="filterAccidentDate"
                        name="filterAccidentDate"
                        label="Filter by date"
                        value="Choose date"
                        />
                </div>
                
            </div>
            <button wire:click="filterByDate" data-tw-merge class="transition duration-200 border shadow-sm inline-flex items-center
             justify-center py-2 px-3 font-medium cursor-pointer 
             focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 
             dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90
              [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed bg-success border-success bg-opacity-20 border-opacity-5 text-success dark:border-success dark:border-opacity-20 [&amp;:hover:not(:disabled)]:bg-opacity-10 [&amp;:hover:not(:disabled)]:border-opacity-10 rounded-full mb-2 mr-1 w-24 mb-2 mr-1 w-24">Filter</button>
             
              <button wire:click="resetDateFilter" data-tw-merge class="transition duration-200 border shadow-sm inline-flex items-center 
              justify-center py-2 px-3font-medium cursor-pointer 
              focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50
               [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed bg-slate-300 border-secondary bg-opacity-20 text-slate-500 dark:bg-darkmode-100/20 dark:border-darkmode-100/30 dark:text-slate-300 [&amp;:hover:not(:disabled)]:bg-opacity-10 [&amp;:hover:not(:disabled)]:dark:bg-darkmode-100/10 [&amp;:hover:not(:disabled)]:dark:border-darkmode-100/20 rounded-full mb-2 mr-1 w-24 mb-2 mr-1 w-24">Reset</button>
           
        </div>
    </div>
        <div class="p-8"  {{ $editReportId != 0 ? '' : 'hidden' }}>
            <div class="mt-4 text-medium font-bold">
                Modify report for {{ $editDriverDisplayName ?? ''  }}
            </div>
            <div class="mt-4">
                <x-impala-components.form.date-picker
                    wire:model="editAccidentDate"
                    id="editAccidentDate"
                    name="editAccidentDate"
                    label="Date of accident"
                    value="{{ $editAccidentDate ?? 'Choose date of accident' }}"
                    readonly="readonly"
                    onkeydown="return false"
                    onpaste="return false"
                    ondrop="return false"
                    autocomplete="off"
                    x-on:keydown.prevent=""
                    />
            </div>
    
            <div class="mt-4">
                <x-impala-components.form.select-dropdown
                    wire:model="editSelectedAccidentType"
                    id="editSelectedAccidentType"
                    name="editSelectedAccidentType"
                    placeholder="Select accident type"
                    label="Select accident type"
                    :selected="$editSelectedAccidentType"
                    :options="$filteredAccidentTypes" />
            </div>
    
            <div class="mt-4">
                <x-impala-components.form.textarea
                    wire:model="editAccidentDetails"
                    label="Accident comments"
                    name="editAccidentDetails"
                    placeholder="Enter accident comments" />
            </div>
    
            <div class="mt-4">
                <x-impala-components.form.file_pond_input
                title="Upload police or accident report"
                name="editAccidentPoliceReport"
            />
    
             </div>
    
             <div class="mt-4">
                <x-impala-components.form.select-dropdown
                    wire:model="editSelectedStatusId"
                    id="editSelectedStatusId"
                    name="editSelectedStatusId"
                    placeholder="Select accident type"
                    label="Select accident type"
                    :selected="$editSelectedStatusId"
                    :options="$filtteredAccidentStatus" />
            </div>
    
            <div class="mt-8">
                <x-impala-components.form.primary-button
                wire:click="saveModifiedReport"
                name="Save Report" />

                <button wire:click="cancelEdit" data-tw-merge class="transition duration-200 border shadow-sm inline-flex items-center justify-center py-2 px-3 rounded-md font-medium cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed bg-secondary/70 border-secondary/70 text-slate-500 dark:border-darkmode-400 dark:bg-darkmode-400 dark:text-slate-300 [&amp;:hover:not(:disabled)]:bg-slate-100 [&amp;:hover:not(:disabled)]:border-slate-100 [&amp;:hover:not(:disabled)]:dark:border-darkmode-300/80 [&amp;:hover:not(:disabled)]:dark:bg-darkmode-300/80 mb-2 mr-1 w-24 mb-2 mr-1 w-24">Cancel</button>
            </div>
          </div>
        @if ($isDateFilterOn)
        @foreach ($this->filteredReports as  $report)
        <div class="box mt-8" wire:key="{{ $report->id }}" {{ $editReportId != 0 ? 'hidden' : '' }}>
            <div>
            <div class="flex flex-col items-center border-b border-slate-200/60 p-5 dark:border-darkmode-400 lg:flex-row">
                <div class="image-fit h-24 w-24 lg:mr-1 lg:h-12 lg:w-12">
                    <img
                        class="rounded-full"
                        src="{{ $report->driver->profile_photo_file ?  asset('storage/'. $report->driver->profile_photo_file) : asset('driver.png') }}"
                        alt="profile"
                    />
                </div>
                <div class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left">
                    <a
                        class="font-medium"
                        href=""
                    >
                        {{  $report->driver->driver_firstname . '  ' . $report->driver->driver_lastname  }}
                    </a>
                    <div class="mt-0.5 text-medium text-slate-500">
                       Date of accident: {{ convertDateToHumanFormat($report->accident_date) }}
                    </div>
                    <div class="mt-0.5 text-xs font-bold text-slate-500">
                        {{ $report->accidentStatus->status }}
                     </div>
                </div>
                <div class="-ml-2 mt-3 flex lg:ml-0 lg:mt-0 lg:justify-end">
                    <button data-tw-merge class="transition duration-200 border shadow-sm inline-flex items-center 
                        justify-center py-1 px-1 rounded-md font-medium cursor-pointer focus:ring-4 
                        focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none 
                        dark:focus:ring-slate-700 dark:focus:ring-opacity-50 
                        [&amp;:hover:not(:disabled)]:bg-opacity-90 
                        [&amp;:hover:not(:disabled)]:border-opacity-90 
                        [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed bg-danger border-danger text-white dark:border-danger mb-2 mr-1 mb-2 mr-1">
                        Delete
                    </button>
                    <button
                    wire:click="{{ $this->viewReport($report->id) }}"
                    data-tw-merge data-tw-toggle="modal" data-tw-target="#edit-driver"   data-tw-merge class="ml-2 transition duration-200 border shadow-sm inline-flex items-center justify-center py-1 px-1 rounded-md font-medium
                     cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed bg-pending border-pending text-white 
                     dark:border-pending mb-2 mr-1 mb-2 mr-1"
                      
                     >
                     View
                    </button>
                </div>
            </div>
            <div class="flex flex-wrap items-center justify-center p-5 lg:flex-nowrap">
                <div class="mb-4 mr-auto w-auto lg:mb-0 lg:w-auto">
                   @if ($report->accidentType->type == 'Avoidable')
                   <div class="rounded bg-success/20 p-2 font-bold text-success">
                    {{ $report->accidentType->type }}
                </div>
                   @else
                   <div class="rounded bg-pending/20 p-2 font-bold text-pending">
                    {{ $report->accidentType->type }}
                </div>
                   @endif
                    
                </div>
                <x-base.button
                    class="mr-2 px-2 py-1"
                    variant="primary"
                   
                    wire:click="edit({{ $report->id }})"
                >
                    Edit
                </x-base.button>
            </div>
          </div>
        </div>
        @endforeach

            <x-impala-components.overlay.slide-over
                id="edit-driver"
                title="Edit Driver"
            >

            @slot('print_button')
            <button data-tw-merge class="transition duration-200 border shadow-sm inline-flex items-center justify-center py-2 px-3 rounded-md font-medium cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed border-secondary text-slate-500 dark:border-darkmode-100/40 dark:text-slate-300 [&amp;:hover:not(:disabled)]:bg-secondary/20 [&amp;:hover:not(:disabled)]:dark:bg-darkmode-100/10 hidden sm:flex hidden sm:flex"><i data-tw-merge data-lucide="file" class="stroke-1.5 w-5 h-5 w-4 h-4 mr-2 w-4 h-4 mr-2"></i>
                Print Report
            </button>
            @endslot
            @slot('content')
            <div class="flex flex-1 items-center justify-center px-5 lg:justify-start">
                <div class="image-fit relative h-20 w-20 flex-none sm:h-24 sm:w-24 lg:h-32 lg:w-32">
                    <img
                        class="rounded-full"
                        src="{{ $driverProfile ?  asset('storage/' . $driverProfile) : asset('driver.png') }}"
                        alt="Midone - Tailwind Admin Dashboard Template"
                    />
                </div>
                <div class="ml-5">
                    <div class="w-24 truncate text-lg font-medium sm:w-40 sm:whitespace-normal">
                       {{ $editDriverDisplayName }}
                    </div>
                    <div class="text-slate-500">
                        {{  $driversLicenceNumber  }}    
                    </div>
                </div>
            </div>
            <div class="w-full border-t border-gray-200 mt-4"></div>
            <div class="ml-4 mt-4 mr-auto">
                <a
                    class="font-medium"
                    href=""
                >
                    Date of accident
                </a>
                <div class="mr-5 text-slate-500 sm:mr-5">
                   18 Aug 2024
                </div>
            </div>
            <div class="ml-4 mt-4 mr-auto">
                <a
                    class="font-medium"
                    href=""
                >
                    Nature of accident
                </a>
                <div class="mr-5 text-slate-500 sm:mr-5">
                   Avoidable
                </div>
            </div>
            <div class="ml-4 mt-4 mr-auto">
                <a
                    class="font-medium"
                    href=""
                >
                    Status
                </a>
                <div class="mr-5 text-slate-500 sm:mr-5">
                   Pending Investigation 
                </div>
            </div>
            <div class="ml-4 mt-4 mr-auto">
                <a
                    class="font-medium"
                    href=""
                >
                    Comments
                </a>
                <div class="mr-5 text-slate-500 sm:mr-5">
                   fdskjhfs fdshjfdshk 
                </div>
            </div>

            @endslot

            @slot('footer')
            <div class="px-5 py-3 text-right border-t border-slate-200/60 dark:border-darkmode-400"><button data-tw-merge data-tw-dismiss="modal" type="button" class="transition duration-200 border shadow-sm inline-flex items-center justify-center py-2 px-3 rounded-md font-medium cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed border-secondary text-slate-500 dark:border-darkmode-100/40 dark:text-slate-300 [&amp;:hover:not(:disabled)]:bg-secondary/20 [&amp;:hover:not(:disabled)]:dark:bg-darkmode-100/10 w-20 mr-1 w-20 mr-1">Cancel</button>
                <button data-tw-merge type="button" class="transition duration-200 border shadow-sm inline-flex items-center justify-center py-2 px-3 rounded-md font-medium cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed bg-primary border-primary text-white dark:border-primary w-20 w-20">Send</button>
            </div>
            @endslot

         </x-impala-components.overlay.slide-over>
    
        @else
        @foreach ($this->reports as  $report)
        <div class="box mt-8" wire:key="{{ $report->id }}" {{ $editReportId != 0 ? 'hidden' : '' }}>
            <div>
            <div class="flex flex-col items-center border-b border-slate-200/60 p-5 dark:border-darkmode-400 lg:flex-row">
                <div class="image-fit h-24 w-24 lg:mr-1 lg:h-12 lg:w-12">
                    <img
                        class="rounded-full"
                        src="{{ $report->driver->profile_photo_file ?  asset('storage/' . $report->driver->profile_photo_file) : asset('driver.png') }}"
                        alt="profile"
                    />
                </div>
                <div class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left">
                    <a
                        class="font-medium"
                        href=""
                    >
                        {{  $report->driver->driver_firstname . '  ' . $report->driver->driver_lastname  }}
                    </a>
                    <div class="mt-0.5 text-medium text-slate-500">
                       Date of accident: {{ convertDateToHumanFormat($report->accident_date) }}
                    </div>
                    <div class="mt-0.5 text-xs font-bold text-slate-500">
                        {{ $report->accidentStatus->status }}
                     </div>
                </div>
                <div class="-ml-2 mt-3 flex lg:ml-0 lg:mt-0 lg:justify-end">
                    <button 
                        wire:click="confirmDelete({{ $report->id }})"
                        wire:loading.attr="disabled"
                        data-tw-merge class="transition duration-200 border shadow-sm inline-flex items-center 
                        justify-center py-1 px-1 rounded-md font-medium cursor-pointer focus:ring-4 
                        focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none 
                        dark:focus:ring-slate-700 dark:focus:ring-opacity-50 
                        [&amp;:hover:not(:disabled)]:bg-opacity-90 
                        [&amp;:hover:not(:disabled)]:border-opacity-90 
                        [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed bg-danger border-danger text-white dark:border-danger mb-2 mr-1 mb-2 mr-1">
                        <span wire:loading.remove wire:target="confirmDelete">Delete</span>
                        <span wire:loading wire:target="confirmDelete">Deleting...</span>
                    </button>
                    <button
                    x-data
                    x-on:click="$dispatch('open-modal')"
                     wire:click="viewReport({{ $report->id }})"
                         {{-- data-tw-toggle="modal" data-tw-target="#view-driver"  --}}
                         
                        class="ml-2 transition duration-200 border shadow-sm inline-flex items-center justify-center py-1 px-1 rounded-md font-medium
                        cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none 
                        dark:focus:ring-slate-700 dark:focus:ring-opacity-50 
                        [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 
                        disabled:cursor-not-allowed bg-pending border-pending text-white dark:border-pending mb-2 mr-1 mb-2 mr-1"
                     >
                     View
                    </button>
                </div>
            </div>
            <div class="flex flex-wrap items-center justify-center p-5 lg:flex-nowrap">
                <div class="mb-4 mr-auto w-auto lg:mb-0 lg:w-auto">
                   @if ($report->accidentType->type == 'Avoidable')
                   <div class="rounded bg-success/20 p-2 font-bold text-success">
                    {{ $report->accidentType->type }}
                </div>
                   @else
                   <div class="rounded bg-pending/20 p-2 font-bold text-pending">
                    {{ $report->accidentType->type }}
                </div>
                   @endif
                </div>
                <x-base.button
                    class="mr-2 px-2 py-1"
                    variant="primary"
                    wire:click="edit({{ $report->id }})"
                >
                    Edit
                </x-base.button>
            </div>
          </div>
        </div>
       
        
        @endforeach

        


        <!-- BEGIN: Modal Content -->
{{-- <div data-tw-backdrop="" aria-hidden="true" tabindex="-1" id="view-driver" class="modal group bg-black/60 transition-[visibility,opacity] w-screen h-screen fixed left-0 top-0 [&amp;:not(.show)]:duration-[0s,0.2s] [&amp;:not(.show)]:delay-[0.2s,0s] [&amp;:not(.show)]:invisible [&amp;:not(.show)]:opacity-0 [&amp;.show]:visible [&amp;.show]:opacity-100 [&amp;.show]:duration-[0s,0.4s]">
    <div data-tw-merge class="w-[90%] mx-auto bg-white relative rounded-md shadow-md transition-[margin-top,transform] duration-[0.4s,0.3s] -mt-16 group-[.show]:mt-16 group-[.modal-static]:scale-[1.05] dark:bg-darkmode-600 sm:w-[460px]"><a class="absolute right-0 top-0 mr-3 mt-3" data-tw-dismiss="modal" href="#">
            <i data-tw-merge data-lucide="x" class="stroke-1.5 w-5 h-5 h-8 w-8 text-slate-400 h-8 w-8 text-slate-400"></i>


        </a>
        <div class="p-5 text-center">
            <i data-tw-merge data-lucide="check-circle" class="stroke-1.5 w-5 h-5 mx-auto mt-3 h-16 w-16 text-success mx-auto mt-3 h-16 w-16 text-success"></i>


            <div class="mt-5 text-3xl">Modal Example</div>
            <div class="mt-2 text-slate-500">
                {{ $editDriverDisplayName  }}
            </div>
        </div>
        <div class="px-5 pb-8 text-center">
            <button data-tw-merge data-tw-dismiss="modal" type="button" class="transition duration-200 border shadow-sm inline-flex items-center justify-center py-2 px-3 rounded-md font-medium cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed bg-primary border-primary text-white dark:border-primary w-24 w-24">Ok</button>
        </div>
    </div>
</div> --}}
<!-- END: Modal Content -->
    <x-impala-components.overlay.slide-over
        id="view-driver"
        title="Edit Driver"
        >

        @slot('print_button')
        <button data-tw-merge class="transition duration-200 border shadow-sm inline-flex items-center justify-center py-2 px-3 rounded-md font-medium cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed border-secondary text-slate-500 dark:border-darkmode-100/40 dark:text-slate-300 [&amp;:hover:not(:disabled)]:bg-secondary/20 [&amp;:hover:not(:disabled)]:dark:bg-darkmode-100/10 hidden sm:flex hidden sm:flex"><i data-tw-merge data-lucide="file" class="stroke-1.5 w-5 h-5 w-4 h-4 mr-2 w-4 h-4 mr-2"></i>
            Print Report
        </button>
        @endslot
        @slot('content')
        <div class="flex flex-1 items-center justify-center px-5 lg:justify-start">
            <div class="image-fit relative h-20 w-20 flex-none sm:h-24 sm:w-24 lg:h-32 lg:w-32">
                <img
                    class="rounded-full"
                    src="{{ $driverProfile ?  asset('storage/'. $driverProfile) : asset('driver.png') }}"
                    alt="Midone - Tailwind Admin Dashboard Template"
                />
            </div>
            <div class="ml-5">
                <div class="w-24 truncate text-lg font-medium sm:w-40 sm:whitespace-normal">
                   jkljkjkljZZZZZ
                </div>
                <div class="text-slate-500">{{ $fakers[0]['jobs'][0] }}</div>
            </div>
        </div>
        <div class="w-full border-t border-gray-200 mt-4"></div>
        <div class="ml-4 mt-4 mr-auto">
            <a
                class="font-medium"
                href=""
            >
                Date of accident
            </a>
            <div class="mr-5 text-slate-500 sm:mr-5">
               18 Aug 2024
            </div>
        </div>
        <div class="ml-4 mt-4 mr-auto">
            <a
                class="font-medium"
                href=""
            >
                Nature of accident
            </a>
            <div class="mr-5 text-slate-500 sm:mr-5">
               Avoidable
            </div>
        </div>
        <div class="ml-4 mt-4 mr-auto">
            <a
                class="font-medium"
                href=""
            >
                Status
            </a>
            <div class="mr-5 text-slate-500 sm:mr-5">
               Pending Investigation 
            </div>
        </div>
        <div class="ml-4 mt-4 mr-auto">
            <a
                class="font-medium"
                href=""
            >
                Comments
            </a>
            <div class="mr-5 text-slate-500 sm:mr-5">
               fdskjhfs fdshjfdshk 
            </div>
        </div>
        


        @endslot

        @slot('footer')
        <div class="px-5 py-3 text-right border-t border-slate-200/60 dark:border-darkmode-400"><button data-tw-merge data-tw-dismiss="modal" type="button" class="transition duration-200 border shadow-sm inline-flex items-center justify-center py-2 px-3 rounded-md font-medium cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed border-secondary text-slate-500 dark:border-darkmode-100/40 dark:text-slate-300 [&amp;:hover:not(:disabled)]:bg-secondary/20 [&amp;:hover:not(:disabled)]:dark:bg-darkmode-100/10 w-20 mr-1 w-20 mr-1">Cancel</button>
            <button data-tw-merge type="button" class="transition duration-200 border shadow-sm inline-flex items-center justify-center py-2 px-3 rounded-md font-medium cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&amp;:hover:not(:disabled)]:bg-opacity-90 [&amp;:hover:not(:disabled)]:border-opacity-90 [&amp;:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed bg-primary border-primary text-white dark:border-primary w-20 w-20">Send</button>
        </div>
        @endslot

     </x-impala-components.overlay.slide-over> 
        @endif
        @if ($isDateFilterOn)
        {{ $this->filteredReports->links('vendor.livewire.custom-pagination') }} 
        @else 
        {{ $this->reports->links('vendor.livewire.custom-pagination') }} 
        @endif
       

    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/flowbite@2.4.1/dist/flowbite.min.js"></script>
    <script>
        document.addEventListener('livewire:initialized', () => {
            console.log('JS comp initialized');
            let component = @this;
            const accidentDate = document.getElementById('accidentDate');
            const filterAccidentDate = document.getElementById('filterAccidentDate');
            const editAccidentDate = document.getElementById('editAccidentDate');
    
            accidentDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('accidentDate', event.target.value);
            });

            filterAccidentDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('filterAccidentDate', event.target.value);
            });

            editAccidentDate.addEventListener('blur', (event) => {
                console.log(event.target.value);
                component.set('editAccidentDate', event.target.value);
            });
            
        });
    </script>
    
</div>
@pushOnce('styles')
    @vite('resources/css/vendors/filepond.css')
@endPushOnce

@pushOnce('vendors')
    @vite('resources/js/vendors/filepond.js')
@endPushOnce