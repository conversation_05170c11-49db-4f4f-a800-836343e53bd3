<div class="grid grid-cols-12 gap-6">
       
    <div class="col-span-12 2xl:col-span-9">
        <div class="grid grid-cols-12 gap-6">
            <!-- BEGIN: General Report -->
            <div class="col-span-12 mt-8">
                <div class="intro-y flex h-10 items-center">
                    <h2 class="mr-5 truncate text-lg font-medium">Overview</h2>
                    <a
                        class="ml-auto flex items-center text-primary"
                        href=""
                    >
                        <x-base.lucide
                            class="mr-3 h-4 w-4"
                            icon="RefreshCcw"
                        /> Reload Data
                    </a>
                </div>
                <div class="mt-5 grid grid-cols-12 gap-6">
                    <div class="intro-y col-span-12 sm:col-span-6 xl:col-span-3">
                        <div @class([
                            'relative zoom-in',
                            "before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-['']",
                        ])>
                            <div class="box p-5">
                                <div class="flex">
                                    <x-base.lucide
                                        class="h-[28px] w-[28px] text-primary"
                                        icon="car-taxi-front"
                                    />
                                    
                                </div>
                                <div class="mt-6 text-3xl font-medium leading-8">{{ $taxiBookingCount }}</div>
                                <div class="mt-1 text-base text-slate-500">New Cab Bookings</div>
                            </div>
                        </div>
                    </div>
                    <div class="intro-y col-span-12 sm:col-span-6 xl:col-span-3">
                        <div @class([
                            'relative zoom-in',
                            "before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-['']",
                        ])>
                            <div class="box p-5">
                                <div class="flex">
                                    <x-base.lucide
                                        class="h-[28px] w-[28px] text-primary"
                                        icon="car-taxi-front"
                                    />
                                    
                                </div>
                                <div class="mt-6 text-3xl font-medium leading-8">{{ $shuttleBookingCount }}</div>
                                <div class="mt-1 text-base text-slate-500">New Shuttle Bookings</div>
                            </div>
                        </div>
                    </div>
                    <div class="intro-y col-span-12 sm:col-span-6 xl:col-span-3">
                        <div @class([
                            'relative zoom-in',
                            "before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-['']",
                        ])>
                            <div class="box p-5">
                                <div class="flex">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#026100" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-user"><circle cx="12" cy="12" r="10"/><circle cx="12" cy="10" r="3"/><path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662"/></svg>
                                    
                                </div>
                                <div class="mt-6 text-3xl font-medium leading-8">{{ $driverBookingCount }}</div>
                                <div class="mt-1 text-base text-slate-500">
                                    New Driver Bookings
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="intro-y col-span-12 sm:col-span-6 xl:col-span-3">
                        <div @class([
                            'relative zoom-in',
                            "before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-['']",
                        ])>
                            <div class="box p-5">
                                <div class="flex">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#026100" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-car"><path d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2"/><circle cx="7" cy="17" r="2"/><path d="M9 17h6"/><circle cx="17" cy="17" r="2"/></svg>
                                    
                                </div>
                                <div class="mt-6 text-3xl font-medium leading-8">{{ $carRentalBookingCount }}</div>
                                <div class="mt-1 text-base text-slate-500">
                                    New Car Rental Requests
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END: General Report -->
                
            
            <div class="col-span-12 mt-6 xl:col-span-12">
                    
                <div class="intro-y box h-screen lg:mt-5">
                    <div class="flex items-center border-b border-slate-200/60 p-5 dark:border-darkmode-400">
                        <h2 class="mr-auto text-base font-medium">
                            Map Information
                        </h2>
                    </div>
                    <div wire:ignore id="map" class="h-screen" style="width: 100%;"></div>
                </div>
            </div>   
                
          
            
        </div>
    </div>
    <div class="col-span-12 2xl:col-span-3">
        <div class="-mb-10 pb-10 2xl:border-l">
            <div class="grid grid-cols-12 gap-x-6 gap-y-6 2xl:gap-x-0 2xl:pl-6">
                <!-- BEGIN: Transactions -->
                <div class="col-span-12 mt-3 md:col-span-6 xl:col-span-4 2xl:col-span-12 2xl:mt-8">
                    <div class="intro-x flex h-10 items-center">
                        <h2 class="mr-5 truncate text-lg font-medium">Cab Bookings</h2>
                    </div>
                    <div class="mt-5">
                        @foreach ($cabBookings as $cabBooking )
                            <div class="intro-x">
                                <div class="box zoom-in mb-3 flex items-center px-5 py-3">
                                    <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                        <img
                                            src="{{ $cabBooking->client->supabase_image_url }}"
                                            alt="Midone - Tailwind Admin Dashboard Template"
                                        />
                                    </div>
                                    <div class="ml-4 mr-auto">
                                        <div class="font-medium">{{ $cabBooking->client->name }}</div>
                                        <div class="mt-0.5 text-xs text-slate-800">
                                            {{ convertDateToHumanFormat($cabBooking->trip_date)  }}
                                        </div>
                                    </div>
                                    <div class="text-success" >
                                        {{ $cabBooking->tripStatus->status }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                        <a
                            class="intro-x block w-full rounded-md border border-dotted border-slate-400 py-3 text-center text-slate-500 dark:border-darkmode-300"
                            href=""
                        >
                            View More
                        </a>
                    </div>
                </div>
                <!-- END: Transactions -->

                <div class="col-span-12 mt-3 md:col-span-6 xl:col-span-4 2xl:col-span-12 2xl:mt-8">
                    <div class="intro-x flex h-10 items-center">
                        <h2 class="mr-5 truncate text-lg font-medium">Shuttle Bookings</h2>
                    </div>
                    <div class="mt-5">
                        @foreach ($cabBookings as $cabBooking )
                        <div class="intro-x">
                            <div class="box zoom-in mb-3 flex items-center px-5 py-3">
                                <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                    <img
                                        src="{{ $cabBooking->client->supabase_image_url }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                                <div class="ml-4 mr-auto">
                                    <div class="font-medium">{{ $cabBooking->client->name }}</div>
                                    <div class="mt-0.5 text-xs text-slate-800">
                                        {{ convertDateToHumanFormat($cabBooking->trip_date)  }}
                                    </div>
                                </div>
                                <div class="text-success" >
                                    {{ $cabBooking->tripStatus->status }}
                                </div>
                            </div>
                        </div>
                    @endforeach
                        <a
                            class="intro-x block w-full rounded-md border border-dotted border-slate-400 py-3 text-center text-slate-500 dark:border-darkmode-300"
                            href=""
                        >
                            View More
                        </a>
                    </div>
                </div>
                <!-- BEGIN: Recent Activities -->
                <div class="col-span-12 mt-3 md:col-span-6 xl:col-span-4 2xl:col-span-12">
                    <div class="intro-x flex h-10 items-center">
                        <h2 class="mr-5 truncate text-lg font-medium">
                            Recent Activities
                        </h2>
                        <a
                            class="ml-auto truncate text-primary"
                            href=""
                        > Show More </a>
                    </div>
                    <div
                        class="relative mt-5 before:absolute before:ml-5 before:mt-5 before:block before:h-[85%] before:w-px before:bg-slate-200 before:dark:bg-darkmode-400">
                        <div class="intro-x relative mb-3 flex items-center">
                            <div
                                class="before:absolute before:ml-5 before:mt-5 before:block before:h-px before:w-20 before:bg-slate-200 before:dark:bg-darkmode-400">
                                <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                    <img
                                        src="{{ Vite::asset($fakers[9]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                            </div>
                            <div class="box zoom-in ml-4 flex-1 px-5 py-3">
                                <div class="flex items-center">
                                    <div class="font-medium">
                                        {{ $fakers[9]['users'][0]['name'] }}
                                    </div>
                                    <div class="ml-auto text-xs text-slate-500">07:00 PM</div>
                                </div>
                                <div class="mt-1 text-slate-500">Has joined the team</div>
                            </div>
                        </div>
                        <div class="intro-x relative mb-3 flex items-center">
                            <div
                                class="before:absolute before:ml-5 before:mt-5 before:block before:h-px before:w-20 before:bg-slate-200 before:dark:bg-darkmode-400">
                                <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                    <img
                                        src="{{ Vite::asset($fakers[8]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                            </div>
                            <div class="box zoom-in ml-4 flex-1 px-5 py-3">
                                <div class="flex items-center">
                                    <div class="font-medium">
                                        {{ $fakers[8]['users'][0]['name'] }}
                                    </div>
                                    <div class="ml-auto text-xs text-slate-500">07:00 PM</div>
                                </div>
                                <div class="text-slate-500">
                                    <div class="mt-1">Added 3 new photos</div>
                                    <div class="mt-2 flex">
                                        <x-base.tippy
                                            class="image-fit zoom-in mr-1 h-8 w-8"
                                            as="div"
                                            content="{{ $fakers[0]['products'][0]['name'] }}"
                                        >
                                            <img
                                                class="rounded-md border border-white"
                                                src="{{ Vite::asset($fakers[8]['photos'][0]) }}"
                                                alt="Midone - Tailwind Admin Dashboard Template"
                                            />
                                        </x-base.tippy>
                                        <x-base.tippy
                                            class="image-fit zoom-in mr-1 h-8 w-8"
                                            as="div"
                                            content="{{ $fakers[1]['products'][0]['name'] }}"
                                        >
                                            <img
                                                class="rounded-md border border-white"
                                                src="{{ Vite::asset($fakers[8]['photos'][1]) }}"
                                                alt="Midone - Tailwind Admin Dashboard Template"
                                            />
                                        </x-base.tippy>
                                        <x-base.tippy
                                            class="image-fit zoom-in mr-1 h-8 w-8"
                                            as="div"
                                            content="{{ $fakers[2]['products'][0]['name'] }}"
                                        >
                                            <img
                                                class="rounded-md border border-white"
                                                src="{{ Vite::asset($fakers[8]['photos'][2]) }}"
                                                alt="Midone - Tailwind Admin Dashboard Template"
                                            />
                                        </x-base.tippy>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-x my-4 text-center text-xs text-slate-500">
                            12 November
                        </div>
                        <div class="intro-x relative mb-3 flex items-center">
                            <div
                                class="before:absolute before:ml-5 before:mt-5 before:block before:h-px before:w-20 before:bg-slate-200 before:dark:bg-darkmode-400">
                                <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                    <img
                                        src="{{ Vite::asset($fakers[7]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                            </div>
                            <div class="box zoom-in ml-4 flex-1 px-5 py-3">
                                <div class="flex items-center">
                                    <div class="font-medium">
                                        {{ $fakers[7]['users'][0]['name'] }}
                                    </div>
                                    <div class="ml-auto text-xs text-slate-500">07:00 PM</div>
                                </div>
                                <div class="mt-1 text-slate-500">
                                    Has changed
                                    <a
                                        class="text-primary"
                                        href=""
                                    >
                                        {{ $fakers[7]['products'][0]['name'] }}
                                    </a>
                                    price and description
                                </div>
                            </div>
                        </div>
                        <div class="intro-x relative mb-3 flex items-center">
                            <div
                                class="before:absolute before:ml-5 before:mt-5 before:block before:h-px before:w-20 before:bg-slate-200 before:dark:bg-darkmode-400">
                                <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                    <img
                                        src="{{ Vite::asset($fakers[6]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                            </div>
                            <div class="box zoom-in ml-4 flex-1 px-5 py-3">
                                <div class="flex items-center">
                                    <div class="font-medium">
                                        {{ $fakers[6]['users'][0]['name'] }}
                                    </div>
                                    <div class="ml-auto text-xs text-slate-500">07:00 PM</div>
                                </div>
                                <div class="mt-1 text-slate-500">
                                    Has changed
                                    <a
                                        class="text-primary"
                                        href=""
                                    >
                                        {{ $fakers[6]['products'][0]['name'] }}
                                    </a>
                                    description
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END: Recent Activities -->
                
                
        </div>
    </div>

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDgPaOiKNa1w_JLpABm2E14_M4Q-HtcBdY&libraries=places"></script>
    <script>
        document.addEventListener('livewire:initialized', function () {
            // Initialize the map
           
            var mapOptions = {
                zoom: 12,
                center: {lat: -17.819251966390638, lng: 31.049042350284857}
            };
            
            var map = new google.maps.Map(document.getElementById('map'), mapOptions);

            // console.log($cabBookings);
            // @foreach (@this.cabBookings as $cabBooking)
            // var destinationMarker = new google.maps.Marker({
            //     position: {lat: $cabBooking->pick_up_latitude, lng: $cabBooking->pick_up_longitude},
            //     map: map,
            //     title: "Destination"
            // });
            // @endforeach

            // @this.cabBookings.forEach(location => {
            //     new google.maps.Marker({
            //         position: { lat: location.pick_up_latitude, lng: location.pick_up_longitude },
            //         map: map,
            //         title: location.title
            //     });
            // });      
        });


        
    </script>
</div>