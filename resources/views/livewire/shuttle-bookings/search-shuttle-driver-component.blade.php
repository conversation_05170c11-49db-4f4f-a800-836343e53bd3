<div>
    <div class="intro-x relative mr-3 sm:mr-6 mt-8">
        <div class="search relative hidden sm:block">
            <x-base.form-input
                  wire:model.live.debounce.300ms="searchDriver"
                class="w-1/2 rounded-full border-transparent bg-slate-200 pr-8 shadow-none
                 transition-[width] duration-300 ease-in-out focus:w-72 focus:border-transparent dark:bg-darkmode-400"
                type="text"
                placeholder="Search driver"
            />
        </div>
        <div {{ $driverId == 0 ? 'hidden' : '' }}>
            <div  class="mt-2 flex justify-between items-center" hidden  >
                <div class="image-fit h-8 w-8">
                    <img
                        class="rounded-full"
                        src="{{ $driverProfilePic ?  asset('storage/'.$driverProfilePic) : asset('driver.png') }}"
                        alt="Midone Tailwind HTML Admin Template"
                    />
                </div>
                <div class="ml-3">{{ $driverName}}</div>
                <div class="ml-auto w-48 truncate text-right font-bold text-xs text-slate-500">
                    {{ 'Licence No:' . ' ' . $driverLicenceNumber  }}
                </div>
            </div>
        </div>
        <a
            class="relative text-white/70 sm:hidden"
            href=""
        >
            <x-base.lucide
                class="h-5 w-5 dark:text-slate-500"
                icon="Search"
            />
        </a>
        <div class="mt-2" {{ $searchResults->count() > 0 && $searchDriver !== '' ? '' : 'hidden' }}>
         {{-- <x-base.transition
            class="search-result absolute right-0 z-10 mt-[3px] hidden"
            selector=".show"
            enter="transition-all ease-linear duration-150"
            enterFrom="mt-5 invisible opacity-0 translate-y-1"
            enterTo="mt-[3px] visible opacity-100 translate-y-0"
            leave="transition-all ease-linear duration-150"
            leaveFrom="mt-[3px] visible opacity-100 translate-y-0"
            leaveTo="mt-5 invisible opacity-0 translate-y-1"
        > --}}
            <div class="box w-[300px] p-5">
                <div class="mb-2 font-medium">Search results</div>
                <div class="mb-5">
                  
                </div>
                <div class="mb-2 font-medium">Drivers</div>
                <div class="mb-5">
                    @foreach ($searchResults as $searchResult)
                        <div
                            wire:key="{{  $searchResult->id }}"
                            class="mt-2 flex items-center"
                            wire:click="selectDriver({{ $searchResult }})" 
                        >
                            <div class="image-fit h-8 w-8">
                                
                                <img
                                    class="rounded-full"
                                    src="{{ $searchResult->profile_photo_file ?  asset('storage/'.$searchResult->profile_photo_file) : asset('driver.png') }}"
                                    alt="Midone Tailwind HTML Admin Template"
                                />
                            </div>
                            <div class="ml-3">{{ $searchResult->driver_firstname . ' ' . $searchResult->driver_lastname }}</div>
                            <div class="ml-auto w-48 truncate text-right text-xs text-slate-500">
                                {{ 'Licence No:' . $searchResult->drivers_licence  }}
                            </div>
                        </div>
                    @endforeach
                </div>
               
            </div>
        {{-- </x-base.transition> --}}
        </div>
    </div>
</div>
