<div>

    <div class="intro-y mt-8 flex items-center">
        <h2 class="mr-auto text-lg font-medium">Shuttle Trip details</h2>
    </div>
    <div class="grid grid-cols-12 gap-6">
        <!-- BEGIN: Profile Menu -->
        <div class="col-span-12 flex flex-col-reverse lg:col-span-4 lg:block 2xl:col-span-3">
            <div class="intro-y box mt-5">
                <div class="relative flex items-center p-5">
                    <div class="image-fit h-20 w-20">
                        <img
                            class="rounded-full"
                            src="{{ $trip->client->supabase_image_url }}"
                            alt="Midone - Tailwind Admin Dashboard Template"
                        />
                    </div>
                    <div class="ml-4 mr-auto">
                        <div class="text-base font-medium">
                            {{ $trip->client->name }}
                        </div>
                        <div class="text-slate-500">{{ 'Phone: +' . $trip->client->phonenumber }}</div>
                    </div>
                    {{-- <x-base.menu>
                        <x-base.menu.button
                            class="block h-5 w-5"
                            href="#"
                            tag="a"
                        >
                            <x-base.lucide
                                class="h-5 w-5 text-slate-500"
                                icon="MoreHorizontal"
                            />
                        </x-base.menu.button>
                        <x-base.menu.items class="w-56">
                            <x-base.menu.header> Export Options</x-base.menu.header>
                            <x-base.menu.divider />
                            <x-base.menu.item>
                                <x-base.lucide
                                    class="mr-2 h-4 w-4"
                                    icon="Activity"
                                />
                                English
                            </x-base.menu.item>
                            <x-base.menu.item>
                                <x-base.lucide
                                    class="mr-2 h-4 w-4"
                                    icon="Box"
                                />
                                Indonesia
                                <div class="ml-auto rounded-full bg-danger px-1 text-xs text-white">
                                    10
                                </div>
                            </x-base.menu.item>
                            <x-base.menu.item>
                                <x-base.lucide
                                    class="mr-2 h-4 w-4"
                                    icon="Layout"
                                />
                                English
                            </x-base.menu.item>
                            <x-base.menu.item>
                                <x-base.lucide
                                    class="mr-2 h-4 w-4"
                                    icon="Sidebar"
                                />
                                Indonesia
                            </x-base.menu.item>
                            <x-base.menu.divider />
                            <x-base.menu.footer>
                                <x-base.button
                                    class="px-2 py-1"
                                    type="button"
                                    variant="primary"
                                >
                                    Settings
                                </x-base.button>
                                <x-base.button
                                    class="ml-auto px-2 py-1"
                                    type="button"
                                    variant="secondary"
                                >
                                    View Profile
                                </x-base.button>
                            </x-base.menu.footer>
                        </x-base.menu.items>
                    </x-base.menu> --}}
                </div>
                <div class="border-t border-slate-200/60 p-5 dark:border-darkmode-400">
                    <a
                        class="flex items-center font-medium text-primary"
                        href=""
                    >
                        <x-base.lucide
                            class="mr-2 h-4 w-4"
                            icon="Activity"
                        /> Personal
                        Information
                    </a>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                            <x-base.lucide
                                class="mr-2 h-4 w-4"
                                icon="receipt"
                            /> Trip Cost
                        </a>
                        </div>
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                            {{ '$'. $trip->trip_cost . 'USD' }}
                        </a>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                            <x-base.lucide
                                class="mr-2 h-4 w-4"
                                icon="route"
                            /> Distance
                        </a>
                        </div>
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                            {{ $trip->trip_total_distance . 'Km' }}
                        </a>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                            <x-base.lucide
                                class="mr-2 h-4 w-4"
                                icon="calendar-days"
                            /> Pickup date
                        </a>
                        </div>
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                             {{ convertDateToHumanFormat($trip->trip_date)  }}
                        </a>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                            <x-base.lucide
                                class="mr-2 h-4 w-4"
                                icon="clock"
                            /> Pickup time
                        </a>
                        </div>
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                             {{ convertToAmPmFormat($trip->trip_time)}}
                        </a>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                            <x-base.lucide
                                class="mr-2 h-4 w-4"
                                icon="car"
                            /> Vehicle Type
                        </a>
                        </div>
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                        {{ $trip->shuttleCity->classification }}
                        </a>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                            <x-base.lucide
                                class="mr-2 h-4 w-4"
                                icon="circle"
                            /> Trip Status
                        </a>
                        </div>
                        <div>
                            <a
                            class="mt-5 flex items-center"
                            href=""
                        >
                        {{ $trip->tripStatus->status }}
                        </a>
                        </div>
                    </div>
                   
                   
                    
                </div>
                <div class="border-t border-slate-200/60 p-5 dark:border-darkmode-400">
                    @livewire('shuttle-bookings.search-shuttle-driver-component')
                        </div>
                        <div class="flex border-t border-slate-200/60 p-5 dark:border-darkmode-400">
                        
                        <div {{ $driver_id != 0 ? '' : 'hidden' }}>
                            <x-base.button
                            wire:click="assignShuttleDriver" 
                            class="px-2 py-1"
                            type="button"
                            variant="primary"
                        >
                            Assign Driver
                        </x-base.button>
                        </div>
                        
                            <x-base.button
                                class="ml-auto px-2 py-1"
                                type="button"
                                variant="outline-secondary"
                            >
                                Close Trip
                            </x-base.button>
                        </div>
                <div {{ session('shuttle_trip_assigned') ? '' : 'hidden' }} class="w-full pt-4">
                    <x-base.alert
                    class="flex justify-between mb-2"
                    variant="primary"
                    hidden
                >
        
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                        {{ session('shuttle_trip_assigned')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
               </div>

                <div {{ session('shuttle_trip_not_paid') ? '' : 'hidden' }} class="w-full pt-4">
                    <x-base.alert
                    class="flex justify-between mb-2"
                    variant="warning"
                    hidden
                >
        
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                        {{ session('shuttle_trip_not_paid')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
               </div>
                <div {{ session('cab_trip_not_assigned') ? '' : 'hidden' }} class="w-full pt-4">
                    <x-base.alert
                    class="flex justify-between mb-2"
                    variant="danger"
                    hidden
                >
        
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                        {{ session('cab_trip_not_assigned')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
               </div>
            </div>
        </div>
        <!-- END: Profile Menu -->
        <div class="col-span-12 lg:col-span-8 2xl:col-span-9">
            <!-- BEGIN: Display Information -->
            <div class="intro-y box h-screen lg:mt-5">
                <div class="flex items-center border-b border-slate-200/60 p-5 dark:border-darkmode-400">
                    <h2 class="mr-auto text-base font-medium">
                        Map Information
                    </h2>
                </div>
                <div wire:ignore id="map" class="h-screen" style="width: 100%;"></div>
                {{-- <div class="p-5">
                    <div class="flex flex-col xl:flex-row">
                        <div class="mt-6 flex-1 xl:mt-0">
                            <div class="grid grid-cols-12 gap-x-5">
                                <div class="col-span-12 2xl:col-span-6">
                                    <div>
                                        <x-base.form-label for="update-profile-form-1">
                                            Display Name
                                        </x-base.form-label>
                                        <x-base.form-input
                                            id="update-profile-form-1"
                                            type="text"
                                            value="{{ $fakers[0]['users'][0]['name'] }}"
                                            placeholder="Input text"
                                            disabled
                                        />
                                    </div>
                                    <div class="mt-3">
                                        <x-base.form-label for="update-profile-form-2">
                                            Nearest MRT Station
                                        </x-base.form-label>
                                        <x-base.tom-select
                                            class="w-full"
                                            id="update-profile-form-2"
                                        >
                                            <option value="1">Admiralty</option>
                                            <option value="2">Aljunied</option>
                                            <option value="3">Ang Mo Kio</option>
                                            <option value="4">Bartley</option>
                                            <option value="5">Beauty World</option>
                                        </x-base.tom-select>
                                    </div>
                                </div>
                                <div class="col-span-12 2xl:col-span-6">
                                    <div class="mt-3 2xl:mt-0">
                                        <x-base.form-label for="update-profile-form-3">
                                            Postal Code
                                        </x-base.form-label>
                                        <x-base.tom-select
                                            class="w-full"
                                            id="update-profile-form-3"
                                        >
                                            <option value="1">
                                                018906 - 1 STRAITS BOULEVARD SINGA...
                                            </option>
                                            <option value="2">
                                                018910 - 5A MARINA GARDENS DRIVE...
                                            </option>
                                            <option value="3">
                                                018915 - 100A CENTRAL BOULEVARD...
                                            </option>
                                            <option value="4">
                                                018925 - 21 PARK STREET MARINA...
                                            </option>
                                            <option value="5">
                                                018926 - 23 PARK STREET MARINA...
                                            </option>
                                        </x-base.tom-select>
                                    </div>
                                    <div class="mt-3">
                                        <x-base.form-label for="update-profile-form-4">
                                            Phone Number
                                        </x-base.form-label>
                                        <x-base.form-input
                                            id="update-profile-form-4"
                                            type="text"
                                            value="65570828"
                                            placeholder="Input text"
                                        />
                                    </div>
                                </div>
                                <div class="col-span-12">
                                    <div class="mt-3">
                                        <x-base.form-label for="update-profile-form-5">
                                            Address
                                        </x-base.form-label>
                                        <x-base.form-textarea
                                            id="update-profile-form-5"
                                            value="10 Anson Road, International Plaza, #10-11, 079903
                        Singapore, Singapore"
                                            placeholder="Adress"
                                        ></x-base.form-textarea>
                                    </div>
                                </div>
                            </div>
                            <x-base.button
                                class="mt-3 w-20"
                                type="button"
                                variant="primary"
                            >
                                Save
                            </x-base.button>
                        </div>
                        <div class="mx-auto w-52 xl:ml-6 xl:mr-0">
                            <div
                                class="rounded-md border-2 border-dashed border-slate-200/60 p-5 shadow-sm dark:border-darkmode-400">
                                <div class="image-fit zoom-in relative mx-auto h-40 cursor-pointer">
                                    <img
                                        class="rounded-md"
                                        src="{{ Vite::asset($fakers[0]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                    <x-base.tippy
                                        class="absolute right-0 top-0 -mr-2 -mt-2 flex h-5 w-5 items-center justify-center rounded-full bg-danger text-white"
                                        as="div"
                                        content="Remove this profile photo?"
                                    >
                                        <x-base.lucide
                                            class="h-4 w-4"
                                            icon="X"
                                        />
                                    </x-base.tippy>
                                </div>
                                <div class="relative mx-auto mt-5 cursor-pointer">
                                    <x-base.button
                                        class="w-full"
                                        type="button"
                                        variant="primary"
                                    >
                                        Change Photo
                                    </x-base.button>
                                    <x-base.form-input
                                        class="absolute left-0 top-0 h-full w-full opacity-0"
                                        type="file"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div> --}}
            </div>
            <!-- END: Display Information -->
            <!-- BEGIN: Personal Information -->
            {{-- <div class="intro-y box mt-5">
                <div class="flex items-center border-b border-slate-200/60 p-5 dark:border-darkmode-400">
                    <h2 class="mr-auto text-base font-medium">
                        Personal Information
                    </h2>
                </div>
                <div class="p-5">
                    <div class="grid grid-cols-12 gap-x-5">
                        <div class="col-span-12 xl:col-span-6">
                            <div>
                                <x-base.form-label for="update-profile-form-6">Email</x-base.form-label>
                                <x-base.form-input
                                    id="update-profile-form-6"
                                    type="text"
                                    value="{{ $fakers[0]['users'][0]['email'] }}"
                                    placeholder="Input text"
                                    disabled
                                />
                            </div>
                            <div class="mt-3">
                                <x-base.form-label for="update-profile-form-7">Name</x-base.form-label>
                                <x-base.form-input
                                    id="update-profile-form-7"
                                    type="text"
                                    value="{{ $fakers[0]['users'][0]['name'] }}"
                                    placeholder="Input text"
                                    disabled
                                />
                            </div>
                            <div class="mt-3">
                                <x-base.form-label for="update-profile-form-8">
                                    ID Type
                                </x-base.form-label>
                                <x-base.form-select id="update-profile-form-8">
                                    <option>IC</option>
                                    <option>FIN</option>
                                    <option>Passport</option>
                                </x-base.form-select>
                            </div>
                            <div class="mt-3">
                                <x-base.form-label for="update-profile-form-9">
                                    ID Number
                                </x-base.form-label>
                                <x-base.form-input
                                    id="update-profile-form-9"
                                    type="text"
                                    value="357821204950001"
                                    placeholder="Input text"
                                />
                            </div>
                        </div>
                        <div class="col-span-12 xl:col-span-6">
                            <div class="mt-3 xl:mt-0">
                                <x-base.form-label for="update-profile-form-10">
                                    Phone Number
                                </x-base.form-label>
                                <x-base.form-input
                                    id="update-profile-form-10"
                                    type="text"
                                    value="65570828"
                                    placeholder="Input text"
                                />
                            </div>
                            <div class="mt-3">
                                <x-base.form-label for="update-profile-form-11">
                                    Address
                                </x-base.form-label>
                                <x-base.form-input
                                    id="update-profile-form-11"
                                    type="text"
                                    value="10 Anson Road, International Plaza, #10-11, 079903 Singapore, Singapore"
                                    placeholder="Input text"
                                />
                            </div>
                            <div class="mt-3">
                                <x-base.form-label for="update-profile-form-12">
                                    Bank Name
                                </x-base.form-label>
                                <x-base.tom-select
                                    class="w-full"
                                    id="update-profile-form-12"
                                >
                                    <option value="1">SBI - STATE BANK OF INDIA</option>
                                    <option value="2">CITI BANK - CITI BANK</option>
                                </x-base.tom-select>
                            </div>
                            <div class="mt-3">
                                <x-base.form-label for="update-profile-form-13">
                                    Bank Account
                                </x-base.form-label>
                                <x-base.form-input
                                    id="update-profile-form-13"
                                    type="text"
                                    value="DBS Current 011-903573-0"
                                    placeholder="Input text"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-end">
                        <x-base.button
                            class="mr-auto w-20"
                            type="button"
                            variant="primary"
                        >
                            Save
                        </x-base.button>
                        <a
                            class="flex items-center text-danger"
                            href=""
                        >
                            <x-base.lucide
                                class="mr-1 h-4 w-4"
                                icon="Trash"
                            /> Delete
                            Account
                        </a>
                    </div>
                </div>
            </div> --}}
            <!-- END: Personal Information -->
        </div>
    </div>
    
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDgPaOiKNa1w_JLpABm2E14_M4Q-HtcBdY&libraries=places"></script>
    <script>
        document.addEventListener('livewire:initialized', function () {
            // Initialize the map
          
            var mapOptions = {
                zoom: 7,
                center: {lat: @this.originLat, lng: @this.originLng}
            };

            var map = new google.maps.Map(document.getElementById('map'), mapOptions);

           
            // Add origin marker
            var originMarker = new google.maps.Marker({
                position: {lat: @this.originLat, lng: @this.originLng},
                map: map,
                title: "Pickup point"
            });

            // Add destination marker
            var destinationMarker = new google.maps.Marker({
                position: {lat: @this.destinationLat, lng: @this.destinationLng},
                map: map,
                title: "Destination"
            });

            // Directions Service and Renderer
            var directionsService = new google.maps.DirectionsService();
            var directionsRenderer = new google.maps.DirectionsRenderer();

            directionsRenderer.setMap(map);

            // Define the route
            var request = {
                origin: {lat: @this.originLat, lng: @this.originLng},
                destination: {lat: @this.destinationLat, lng: @this.destinationLng},
                travelMode: google.maps.TravelMode.DRIVING
            };

            // Draw the route
            directionsService.route(request, function (result, status) {
                if (status == 'OK') {
                    directionsRenderer.setDirections(result);
                } else {
                    console.error('Directions request failed due to ' + status);
                }
            });
        });
    </script>
    {{-- <script>
        document.addEventListener('livewire:initialized', function () {
            // Initialize the map
            let map;
            let directionsService;
            let directionsRenderer;

            function initMap() {
                // Center of the map
                const center = { lat: 37.7749, lng: -122.4194 }; // Default center

                // Create the map
                map = new google.maps.Map(document.getElementById("map"), {
                    zoom: 12,
                    center: center,
                });

                directionsService = new google.maps.DirectionsService();
                directionsRenderer = new google.maps.DirectionsRenderer();
                directionsRenderer.setMap(map);

                // Add a marker to the center
                const marker = new google.maps.Marker({
                    position: center,
                    map: map,
                    title: "Center",
                });

                // Load polyline based on coordinates
                loadDirections();
            }

            // Function to add directions polyline
            function loadDirections() {
                const waypoints = @json($coordinates); // This should be a list of coordinates

                if (waypoints.length < 2) {
                    console.error("You need at least 2 coordinates to display a route.");
                    return;
                }

                const origin = waypoints[0];
                const destination = waypoints[waypoints.length - 1];
                const routeWaypoints = waypoints.slice(1, waypoints.length - 1).map(coord => {
                    return { location: new google.maps.LatLng(coord.lat, coord.lng), stopover: true };
                });

                const request = {
                    origin: new google.maps.LatLng(origin.lat, origin.lng),
                    destination: new google.maps.LatLng(destination.lat, destination.lng),
                    waypoints: routeWaypoints,
                    travelMode: 'DRIVING'
                };

                directionsService.route(request, function(result, status) {
                    if (status === 'OK') {
                        directionsRenderer.setDirections(result);
                    } else {
                        console.error('Directions request failed due to ' + status);
                    }
                });
            }

            // Load the Google Maps script dynamically
            function loadGoogleMapsScript() {
                const script = document.createElement('script');
                script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyDgPaOiKNa1w_JLpABm2E14_M4Q-HtcBdY&callback=initMap`;
                script.async = true;
                document.head.appendChild(script);
            }

            // Call the function to load the Google Maps API
            loadGoogleMapsScript();
        });
    </script> --}}
</div>
