<div>
    {{-- {{ dd($bookings) }} --}}
    <h2 class="intro-y mt-10 text-lg font-medium"></h2> Sales

    <div class="mt-5 grid grid-cols-12 gap-6">
        <!-- Table (left) -->
        <div class="col-span-12 lg:col-span-8">
            <div class="box p-5 mb-6 bg-white">
                
                <!-- BEGIN: Data List -->
                <div class="intro-y col-span-12">
                    <!-- Loading state for table -->
                    <div wire:loading.flex wire:target="search" class="w-full justify-center items-center py-10">
                        <x-base.loading-icon icon="circles" class="w-10 h-10" />
                        <span class="ml-2 text-slate-500">Loading rentals...</span>
                    </div>
                    <!-- Table content -->
                    <div wire:loading.remove wire:target="search" class="overflow-x-auto scrollbar-hidden md:overflow-x-visible" style="max-width: 100vw;">
                        <x-base.table class="-mt-2 border-separate border-spacing-y-[10px] min-w-full xs:min-w-[500px] sm:min-w-[800px] lg:min-w-full">
                            <x-base.table.thead>
                                <x-base.table.tr>
                                    <x-base.table.th class="whitespace-nowrap border-b-0 text-xs sm:text-sm">
                                        
                                    </x-base.table.th>
                                    <x-base.table.th class="whitespace-nowrap border-b-0 text-xs sm:text-sm">
                                        Name
                                    </x-base.table.th>
                                    <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                        Mobile Number
                                    </x-base.table.th>
                                    <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                        ACTIONS
                                    </x-base.table.th>
                                </x-base.table.tr>
                            </x-base.table.thead>
                            <x-base.table.tbody>
                                @forelse ($sales as $sale)
                                    <x-base.table.tr>
                                        <x-base.table.td></x-base.table.td>
                                        <x-base.table.td>
                                            @if($editId === $sale->id)
                                                <x-base.form-input
                                                    wire:model.defer="editFullname"
                                                    name="editFullname"
                                                    label="Full Name"
                                                    placeholder="Edit full name"
                                                    class="w-40"
                                                />
                                            @else
                                                {{ $sale->fullname }}
                                            @endif
                                        </x-base.table.td>
                                        <x-base.table.td class="text-center">
                                            @if($editId === $sale->id)
                                                <x-base.form-input
                                                    wire:model.defer="editPhone"
                                                    name="editPhone"
                                                    label="Mobile Number"
                                                    placeholder="Edit mobile number"
                                                    class="w-40"
                                                />
                                            @else
                                                {{ $sale->phone }}
                                            @endif
                                        </x-base.table.td>
                                        <x-base.table.td class="text-center">
                                            @if($editId === $sale->id)
                                                <x-base.button
                                                    wire:click="update({{ $sale->id }})"
                                                    class="mr-2 px-2 py-1"
                                                    variant="primary"
                                                >
                                                    Save
                                                </x-base.button>
                                                <x-base.button
                                                    wire:click="cancelEdit"
                                                    class="px-2 py-1"
                                                    variant="cancel"
                                                >
                                                    Cancel
                                                </x-base.button>
                                            @else
                                                <x-base.button
                                                    wire:click="edit({{ $sale->id }})"
                                                    class="mr-2 px-2 py-1"
                                                    variant="Edit"
                                                >
                                                    Edit
                                                </x-base.button>
                                                <x-base.button
                                                    wire:click="delete({{ $sale->id }})"
                                                    class="px-2 py-1"
                                                    variant="outline-danger"
                                                >
                                                    Delete
                                                </x-base.button>
                                            @endif
                                        </x-base.table.td>
                                    </x-base.table.tr>
                                @empty
                                    <x-base.table.tr>
                                        <x-base.table.td colspan="4" class="text-center">
                                            No sales found.
                                        </x-base.table.td>
                                    </x-base.table.tr>
                                @endforelse
                            </x-base.table.tbody>
                        </x-base.table>
                    </div>
                </div>
                <!-- Pagination -->
                <div class="intro-y col-span-12 flex flex-wrap sm:flex-row sm:flex-nowrap items-center">
                    {{-- {{ $sales->links() }} --}}
                </div>
            </div>
        </div>
        <!-- Add Sale Form (right) -->
        <div class="col-span-12 lg:col-span-4">
            <div class="mb-6 box p-5 bg-white">
                <form wire:submit.prevent="saveSale">
                    <div class="grid grid-cols-12 gap-4">
                        <div class="col-span-12">
                            <x-base.form-input
                                wire:model.defer="fullname"
                                name="fullname"
                                label="Full Name"
                                placeholder="Enter full name"
                                required
                            />
                        </div>
                        <div class="col-span-12">
                            <x-base.form-input
                                wire:model.defer="mobile_number"
                                name="mobile_number"
                                label="Mobile Number"
                                placeholder="Enter mobile number"
                                required
                            />
                        </div>
                        <div class="col-span-12 flex items-end">
                            <x-base.button
                                type="submit"
                                variant="primary"
                                class="w-full"
                                wire:loading.attr="disabled"
                            >
                                <span wire:loading.remove wire:target="saveSale">Save</span>
                                <span wire:loading wire:target="saveSale">Saving...</span>
                            </x-base.button>
                        </div>
                    </div>
                    @error('fullname') <div class="text-red-600 text-xs mt-1">{{ $message }}</div> @enderror
                    @error('mobile_number') <div class="text-red-600 text-xs mt-1">{{ $message }}</div> @enderror
                </form>
                @if (session('sale_created'))
                    <div class="mt-2">
                        <x-base.alert variant="primary" class="mb-2">
                            {{ session('sale_created') }}
                        </x-base.alert>
                    </div>
                @endif
            </div>
        </div>
    </div>
    <!-- BEGIN: Delete Confirmation Modal -->
    <x-base.dialog id="delete-confirmation-modal">
        <x-base.dialog.panel>
            <div class="p-5 text-center">
                <x-base.lucide
                    class="mx-auto mt-3 h-16 w-16 text-danger"
                    icon="XCircle"
                />
                <div class="mt-5 text-3xl">Are you sure?</div>
                <div class="mt-2 text-slate-500">
                    Do you really want to delete this driver? <br />
                    This process cannot be undone.
                </div>
            </div>
            <div class="px-5 pb-8 text-center">
                <x-base.button
                    class="mr-1 w-24"
                    data-tw-dismiss="modal"
                    type="button"
                    variant="outline-secondary"
                >
                    Cancel
                </x-base.button>
                <x-base.button
                    wire:click="delete"
                    class="w-24"
                    type="button"
                    variant="danger"
                    {{-- data-tw-dismiss="modal" --}}
                >
                    Delete
                </x-base.button>
            </div>
        </x-base.dialog.panel>
    </x-base.dialog>
</div>
