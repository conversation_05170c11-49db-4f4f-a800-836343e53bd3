<div>
    {{-- {{ dd($bookings) }} --}}
    <h2 class="intro-y mt-10 text-lg font-medium"></h2>Car Rentals
    <div class="mt-5 grid grid-cols-12 gap-6">
        <div class="intro-y col-span-12 mt-2 flex flex-wrap items-center sm:flex-nowrap">
            <x-base.button
                class="mr-2 shadow-md"
                variant="primary"
            >
             Export Report
            </x-base.button>
            <x-base.menu>
                <x-base.menu.button
                    class="!box px-2"
                    as="x-base.button"
                >
                    <span class="flex h-5 w-5 items-center justify-center">
                        <x-base.lucide
                            class="h-4 w-4"
                            icon="Plus"
                        />
                    </span>
                </x-base.menu.button>
                <x-base.menu.items class="w-40">
                    <x-base.menu.item>
                        <x-base.lucide
                            class="mr-2 h-4 w-4"
                            icon="Printer"
                        /> Print
                    </x-base.menu.item>
                    <x-base.menu.item>
                        <x-base.lucide
                            class="mr-2 h-4 w-4"
                            icon="FileText"
                        /> Export to
                        Excel
                    </x-base.menu.item>
                    <x-base.menu.item>
                        <x-base.lucide
                            class="mr-2 h-4 w-4"
                            icon="FileText"
                        /> Export to
                        PDF
                    </x-base.menu.item>
                </x-base.menu.items>
            </x-base.menu>
            <div class="mx-auto hidden text-slate-500 md:block">
                {{-- Showing 1 to 10 of 150 entries --}}
            </div>
            <div class="mt-3 w-full sm:ml-auto sm:mt-0 sm:w-auto md:ml-0">
                <div class="relative w-56 text-slate-500 ml-auto">
                    <x-base.form-input
                        wire:model.live.debounce.300ms="search"
                        class="!box w-56 pr-10"
                        type="text"
                        placeholder="Search client..."
                    />
                    <!-- Loading indicator for search -->
                    <div wire:loading wire:target="search" class="absolute inset-y-0 right-0 flex items-center pr-5">
                        <x-base.loading-icon icon="three-dots" class="w-4 h-4" />
                    </div>
                    <!-- Search icon when not loading -->
                    <div wire:loading.remove wire:target="search" class="absolute inset-y-0 right-0 flex items-center pr-5">
                        <x-base.lucide
                            class="h-4 w-4"
                            icon="Search"
                        />
                    </div>
                </div>
            </div>
        </div>
        <!-- BEGIN: Data List -->
        <div class="intro-y col-span-12">
            <!-- Loading state for table -->
            <div wire:loading.flex wire:target="search" class="w-full justify-center items-center py-10">
                <x-base.loading-icon icon="circles" class="w-10 h-10" />
                <span class="ml-2 text-slate-500">Loading rentals...</span>
            </div>

            <!-- Table content -->
            <div wire:loading.remove wire:target="search" class="overflow-x-auto scrollbar-hidden md:overflow-x-visible" style="max-width: 100vw;">
                <x-base.table class="-mt-2 border-separate border-spacing-y-[10px] min-w-full xs:min-w-[500px] sm:min-w-[800px] lg:min-w-full">
                    <x-base.table.thead>
                        <x-base.table.tr>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-xs sm:text-sm">
                                
                            </x-base.table.th>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-xs sm:text-sm">
                                CUSTOMER
                            </x-base.table.th>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                VEHICLE TYPE
                            </x-base.table.th>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                COST(per day)
                            </x-base.table.th>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                BOOKING DATE
                            </x-base.table.th>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                FROM DATE
                            </x-base.table.th>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                RETURN DATE
                            </x-base.table.th>
                           
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                DAYS
                            </x-base.table.th>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                DEPOSIT
                            </x-base.table.th>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                TOTAL COST
                            </x-base.table.th>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                STATUS
                            </x-base.table.th>
                            <x-base.table.th class="whitespace-nowrap border-b-0 text-center text-xs sm:text-sm">
                                ACTIONS
                            </x-base.table.th>
                        </x-base.table.tr>
                    </x-base.table.thead>
                    <x-base.table.tbody>
                        @forelse ($bookings as $book)
                            <x-base.table.tr class="intro-x" wire:key="{{ $book->id}}">
                                <x-base.table.td
                                    class="box w-20 rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                >
                                    <div class="flex">
                                        <div class="image-fit zoom-in h-10 w-10">
                                            <x-base.tippy
                                                class="rounded-full shadow-[0px_0px_0px_2px_#fff,_1px_1px_5px_rgba(0,0,0,0.32)] dark:shadow-[0px_0px_0px_2px_#3f4865,_1px_1px_5px_rgba(0,0,0,0.32)]"
                                                src="{{ $book->client->supabase_image_url ??  asset('driver.png') }}"
                                                alt="Impala"
                                                as="img"
                                                content=""
                                                
                                            />
                                        </div>
                                    </div>
                                </x-base.table.td>
                                <x-base.table.td
                                    class="box rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                >
                                    <a
                                        class="whitespace-nowrap font-medium"
                                        href=""
                                    >
                                        {{ $book->client->name }}
                                    </a>
                                    <div class="mt-0.5 whitespace-nowrap text-xs text-slate-500">
                                        {{'Phone: +' . $book->client->phonenumber }}
                                    </div>
                                </x-base.table.td>
                                <x-base.table.td
                                    class="box rounded-l-none rounded-r-none border-x-0 text-sm text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                >
                                {{ $book->rentalPrice->carClassification->classification }}
                                </x-base.table.td>
                                <x-base.table.td
                                    class="box rounded-l-none rounded-r-none border-x-0 text-sm text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                >
                                {{ $book->rentalPrice->amount }}
                                </x-base.table.td>
                                <x-base.table.td
                                class="box rounded-l-none rounded-r-none border-x-0 text-sm text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                            >
                            {{ convertDateToHumanFormat($book->booking_date)  }}
                            </x-base.table.td>
                           
                            <x-base.table.td
                            class="box rounded-l-none rounded-r-none border-x-0 text-sm text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                        >
                         {{ convertDateToHumanFormat($book->from_date) }}
                        </x-base.table.td>
                        <x-base.table.td
                        class="box rounded-l-none rounded-r-none border-x-0 text-sm text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                    >
                     {{ convertDateToHumanFormat($book->to_date) }}
                    </x-base.table.td>
                    <x-base.table.td
                    class="box rounded-l-none rounded-r-none border-x-0 text-sm text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                >
                 {{ getDayBetweenDates($book->from_date,$book->to_date)}}
                </x-base.table.td>
                <x-base.table.td
                class="box rounded-l-none rounded-r-none border-x-0 text-sm text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
            >
             {{ $book->rentalPrice->deposit }}
            </x-base.table.td>
            <x-base.table.td
                class="box rounded-l-none rounded-r-none border-x-0 text-sm text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
            >
             {{ $book->total_cost}}
            </x-base.table.td>
            <x-base.table.td
                class="box rounded-l-none rounded-r-none border-x-0 text-sm text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
            >
             {{ $book->rentalStatus->status}}
            </x-base.table.td>
                                
                                <x-base.table.td @class([
                                    'box w-56 rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600',
                                    'before:absolute before:inset-y-0 before:left-0 before:my-auto before:block before:h-8 before:w-px before:bg-slate-200 before:dark:bg-darkmode-400',
                                ])>
                                    <div class="flex items-center justify-center">
                                        <a
                                            class="mr-3 flex items-center text-success"
                                            href="{{ route('view-car-rental-details', ['book' => $book->id]) }}" 
                                        >
                                            <div class="mr-1 h-4 w-4">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-scan-eye"><path d="M3 7V5a2 2 0 0 1 2-2h2"/><path d="M17 3h2a2 2 0 0 1 2 2v2"/><path d="M21 17v2a2 2 0 0 1-2 2h-2"/><path d="M7 21H5a2 2 0 0 1-2-2v-2"/><circle cx="12" cy="12" r="1"/><path d="M18.944 12.33a1 1 0 0 0 0-.66 7.5 7.5 0 0 0-13.888 0 1 1 0 0 0 0 .66 7.5 7.5 0 0 0 13.888 0"/></svg>
                                            </div>
                                            View
                                        </a>
                                        <button
                                            onclick="if(!confirm('Are you sure you want to delete this booking?')) { event.preventDefault(); event.stopImmediatePropagation(); }"
                                            wire:click="delete({{ $book->id }})" 
                                            type="button"
                                            class="flex items-center text-danger"
                                        >
                                            <div class="mr-1 h-4 w-4">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>
                                            </div>
                                            Delete
                                        </button>
                                    </div>
                                </x-base.table.td>
                            </x-base.table.tr>
                        @empty
                            <x-base.table.tr>
                                <x-base.table.td colspan="6" class="text-center">
                                    No rentals found
                                </x-base.table.td>
                            </x-base.table.tr>
                        @endforelse
                    </x-base.table.tbody>
                </x-base.table>
            </div>
        </div>
        <!-- Pagination -->
        <div class="intro-y col-span-12 flex flex-wrap sm:flex-row sm:flex-nowrap items-center">
            {{ $bookings->links() }}
        </div>
    </div>
    <!-- BEGIN: Delete Confirmation Modal -->
    <x-base.dialog id="delete-confirmation-modal">
        <x-base.dialog.panel>
            <div class="p-5 text-center">
                <x-base.lucide
                    class="mx-auto mt-3 h-16 w-16 text-danger"
                    icon="XCircle"
                />
                <div class="mt-5 text-3xl">Are you sure?</div>
                <div class="mt-2 text-slate-500">
                    Do you really want to delete this driver? <br />
                    This process cannot be undone.
                </div>
            </div>
            <div class="px-5 pb-8 text-center">
                <x-base.button
                    class="mr-1 w-24"
                    data-tw-dismiss="modal"
                    type="button"
                    variant="outline-secondary"
                >
                    Cancel
                </x-base.button>
                <x-base.button
                    wire:click="delete"
                    class="w-24"
                    type="button"
                    variant="danger"
                    {{-- data-tw-dismiss="modal" --}}
                >
                    Delete
                </x-base.button>
            </div>
        </x-base.dialog.panel>
    </x-base.dialog>
</div>
