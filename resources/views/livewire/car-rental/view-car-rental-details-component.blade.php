<div>

    <x-base.notification.index>
            fdsjfhdsjkfhdskjfhdksjf
    </x-base.notification.index>


    <div class="flex items-center mt-8 intro-y">
        <h2 class="mr-auto text-lg font-medium">Trip details</h2>
    </div>
    <div wire:poll.5s class="grid grid-cols-12 gap-6">
        <!-- BEGIN: Profile Menu -->
        <div class="flex flex-col-reverse col-span-12 lg:col-span-6 lg:block 2xl:col-span-6">
            <div class="mt-5 intro-y box">
                <div class="relative flex items-center p-5">
                    <div class="w-20 h-20 image-fit">
                        <img
                            class="rounded-full"
                            src="{{ $book->client->supabase_image_url }}"
                            alt="Midone - Tailwind Admin Dashboard Template"
                        />
                    </div>
                    <div class="ml-4 mr-auto">
                        <div class="text-base font-medium">
                            {{ $book->client->name }}
                        </div>
                        <div class="text-slate-500">{{ 'Phone: +' . $book->client->phonenumber }}</div>
                    </div>
                </div>
                <div class="p-5 border-t border-slate-200/60 dark:border-darkmode-400">
                    <a
                        class="flex items-center font-medium text-primary"
                        href=""
                    >
                        <x-base.lucide
                            class="w-4 h-4 mr-2"
                            icon="Activity"
                        /> Car rental
                        information
                    </a>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="receipt"
                            /> Vehicle Type
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            {{  $book->rentalPrice->carClassification->classification }}
                        </a>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="route"
                            /> 
                            Booking Date
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            {{ convertDateToHumanFormat($book->booking_date) }}
                        </a>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="calendar-days"
                            /> Collection date
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                             {{ convertDateToHumanFormat($book->from_date)  }}
                        </a>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="calendar-days"
                            /> 
                            Returning Date
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                        {{ convertDateToHumanFormat($book->to_date)  }}
                        </a>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="clock"
                            /> Days
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            {{ $book->period }}
                        </a>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="clock"
                            /> Daily Rate
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            {{ '$'. $book->rentalPrice->amount . 'USD' }}
                        </a>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="clock"
                            /> Deposit
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            {{ '$'. $book->rentalPrice->deposit . 'USD' }}
                        </a>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="clock"
                            /> {{ 'Hire Cost: ' . 'Daily Rate'. '  ' . '$'. '('. $book->rentalPrice->amount . ')' . ' *  Number of days' . ' ' .'(' .$book->period . ')'  }}
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            {{ '$'. $book->total_cost . 'USD' }}
                        </a>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5 font-bold"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="clock"
                            /> {{ 'Total Cost: ' . '$'. $book->total_cost . 'USD' . ' + Deposit: ' . '$'. $book->rentalPrice->deposit  }} 
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5 font-bold"
                            href=""
                        >
                            {{ '' $book->total_cost + $book->rentalPrice->deposit }}
                        </a>
                        </div>
                    </div>

                    <hr class="mt-8 border-gray-600 dark:border-white">

                    <div class="flex justify-between mt-4">
                        <div>
                            <x-base.button
                            wire:click="bookCar({{ $book->id }})"
                            class="w-24 mt-4"
                            variant="primary"
                        >
                            Allocate
                        </x-base.button>
                        </div>
                        <div>
                            <x-base.button
                    {{-- wire:click="bookCar({{ $book->id }})" --}}
                            class="w-24 mt-4"
                            variant="secondary"
                >
                    Cancel
                </x-base.button>
                            
                        </div>
                    </div>
                    
                    {{-- <div class="{{ $supabaseDriver !== null ? '' : 'hidden' }} mt-5 border-t border-slate-200/60 dark:border-darkmode-400">
                        <a
                        class="flex items-center mt-4 font-medium text-primary"
                        href=""
                    >
                        <x-base.lucide
                                    class="w-4 h-4 mr-2"
                                    icon="Activity"
                                /> Driver
                                Information
                            </a>

                                <div class="relative flex items-center p-5">
                                    <div class="w-20 h-20 image-fit">
                                        <img
                                    class="rounded-full"
                                    @if($supabaseDriver !== null || $assignedDriverId !== null)
                                         src="{{ $supabaseDriver->profile_photo_file ? asset('storage/'.$supabaseDriver->profile_photo_file) : asset('driver.png') }}"
                                    @else
                                         src="{{ asset('driver.png') }}"
                                    @endif

                                    alt="Driver Profile"
                                />
                            </div>
                            <div class="ml-4 mr-auto">
                                <div class="text-base font-medium">
                                    @if($supabaseDriver)
                                        {{ trim($supabaseDriver->driver_firstname . ' ' . $supabaseDriver->driver_lastname) }}
                                    @else
                                        N/A
                                    @endif
                                </div>
                                <div class="text-slate-500">
                                    Phone: +{{ $supabaseDriver->driver_mobile ?? 'N/A' }}
                                </div>
                                <div class="text-slate-500">
                                    Drivers Licence: {{ $supabaseDriver->driversLicence->licence_class ?? 'N/A' }}
                                </div>
                            </div>
                        </div>
                    </div> --}}
                </div>
                {{-- <div class="p-5 border-t border-slate-200/60 dark:border-darkmode-400">
                    @livewire('cab-bookings.search-driver-to_assign-component')
                        </div>
                        <div class="flex p-5 border-t border-slate-200/60 dark:border-darkmode-400">

                        <div {{ $driver_id != 0 ? '' : 'hidden' }}>
                            <x-base.button
                            wire:click="assignDriver"
                            class="px-2 py-1"
                            type="button"
                            variant="primary"
                        >
                            Assign Driver
                        </x-base.button>
                        </div>

                            <x-base.button
                                class="px-2 py-1 ml-auto"
                                type="button"
                                variant="outline-secondary"
                            >
                                Close Trip
                            </x-base.button>
                        </div>
                <div {{ session('cab_trip_assigned') ? '' : 'hidden' }} class="w-full pt-4">
                    <x-base.alert
                    class="flex justify-between mb-2"
                    variant="primary"
                    hidden
                > --}}


        </div>
     </div>        
    </div>

    {{-- <div id="success-notification-content" class="hidden">
        <div class="flex py-5 pl-5 bg-white border rounded-lg shadow-xl pr-14 border-slate-200/60 dark:bg-darkmode-600 dark:text-slate-300 dark:border-darkmode-600">
            <i data-tw-merge data-lucide="check-circle" class="stroke-1.5 w-5 h-5 text-success"></i>
            <div class="ml-4 mr-4">
                <div class="font-medium">Success!</div>
                <div class="mt-1 text-slate-500">
                    Driver has been successfully assigned.
                </div>
            </div>
        </div>
    </div> --}}

    <div id="success-notification-content" class="flex hidden py-5 pl-5 bg-white border rounded-lg shadow-xl pr-14 border-slate-200/60 dark:bg-darkmode-600 dark:text-slate-300 dark:border-darkmode-600">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#47a117" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big"><path d="M21.801 10A10 10 0 1 1 17 3.335"/><path d="m9 11 3 3L22 4"/></svg>
        <div class="ml-4 mr-4">
            <div class="font-medium">Car Hired</div>
            <div class="mt-1 text-slate-500">
                The vehicle has been allocated successfully!
            </div>
        </div>
    </div>
</div>

<script>
     document.addEventListener('livewire:initialized', function () {
        console.log('fhdjskhfkjdshfkjdshfjkds');
        @this.on('carHired', () => {
                Toastify({
                    node: $("#success-notification-content")
                        .clone()
                        .removeClass("hidden")[0],
                    duration: -1,
                    newWindow: true,
                    close: true,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "white",
                    stopOnFocus: true,
                }).showToast();
            });
     });
</script>
