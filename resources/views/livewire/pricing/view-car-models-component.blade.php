<div class="grid grid-cols-12 gap-6">
    <div class="col-span-12 lg:col-span-6 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-6">
        <div class="text-base font-medium">Car classification</div>
            <div class="w-full border-t border-gray-200 mt-2"></div>
            <div class="intro-y col-span-12 md:col-span-6 mt-2">
                <div class="flex pb-1 items-end">
                    <div>
                        <x-impala-components.form.primary-text-input
                        wire:model="classification"
                        name="classification"
                        label="Car class"
                        placeholder="Add car class group"  />
                    </div>
                </div>
                <div class="mt-4">
                    <x-base.button
                        wire:click="addCarClassification"
                        class="px-2 py-1"
                        variant="primary"
                        :disabled="$isAddingCarClassification"
                    >
                        Add Car Classification
                        <span wire:loading wire:target="addCarClassification" class="ml-2">
                            <svg class="animate-spin h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                            </svg>
                        </span>
                    </x-base.button>
                </div>
                <div class="mt-4"></div>
                <div {{ session('car_class_created') ? '' : 'hidden' }} class="w-full">
                    <x-base.alert
                    class="flex justify-between mb-2"
                    variant="primary"
                    hidden
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                        {{ session('car_class_created')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
            </div>
            <div class="w-full border-t border-gray-200 mt-8"></div>
                @foreach ($car_classes as $carClass)
                <div class="box mt-4" wire:key="{{ $carClass->id }}">
                    <div class="col-span-12 intro-y sm:col-span-6">
                    </div>
                    <div class="flex flex-col items-center p-5 lg:flex-row">
                        {{-- <div class="image-fit h-0 w-0 lg:mr-1 lg:h-12 lg:w-12"> --}}
                            {{-- <img
                                class="rounded-full"
                                src="{{ Vite::asset($faker['photos'][0]) }}"
                                alt="Midone - Tailwind Admin Dashboard Template"
                            {{-- /> --}} 
                        {{-- </div> --}}
                        <div class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left" {{ $isEditingId == $carClass->id ? '' : 'hidden' }}>
                            <x-impala-components.form.primary-text-input
                            value="{{ $carClass->classification  }}"
                            wire:model="editClassification"
                            name="editClassification"
                            label="Car class"
                            placeholder="Add car class group"  />
                        </div>
                        <div   {{ $isEditingId == $carClass->id ? 'hidden' : '' }} class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left">
                            <a
                                class="font-medium"
                                href=""
                            >
                                {{ $carClass->classification }}
                            </a>
                            <div class="mt-0.5 text-xs text-slate-500">
                             Created on:  {{ getHumanDateFromCreatedAt($carClass->created_at) }}
                            </div>
                        </div>
                        <div class="mt-4 flex lg:mt-0" >
                            <div {{ $isEditingId == $carClass->id ? 'hidden' : '' }}>
                            <x-base.button
                                wire:click="editCarClass({{ $carClass->id }})"
                                class="mr-2 px-2 py-1"
                                variant="Edit"
                            >
                                Edit
                            </x-base.button>
                            <x-base.button
                                wire:click="deleteCarModel({{ $carClass->id }})"
                                class="px-2 py-1"
                                variant="outline-danger"
                                :disabled="$isDeletingCarClassification == $carClass->id"
                            >
                                <span wire:loading wire:target="deleteCarModel({{ $carClass->id }})" class="mr-2">
                                    <svg class="animate-spin h-4 w-4 text-red-600 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                                    </svg>
                                </span>
                                Delete
                            </x-base.button>
                           </div>

                           <div {{ $isEditingId == $carClass->id ? '' : 'hidden' }}>
                            <x-base.button
                                wire:click="saveClassModification({{ $carClass->id }})"
                                class="mr-2 px-2 py-1"
                                variant="primary"
                            >
                                Save Changes
                            </x-base.button>
                            <x-base.button
                                wire:click="cancelEdit()"
                                class="px-2 py-1"
                                variant="outline-danger"
                            >
                                Cancel 
                            </x-base.button>
                           </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div> 
    </div>


    {{-- <div class="col-span-12 lg:col-span-6 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-6">
        <div class="text-base font-medium">Add Car Model</div>
            <div class="w-full border-t border-gray-200 mt-2"></div>
        
            <div class="col-span-12 intro-y sm:col-span-6">
                <x-impala-components.form.primary-text-input
                    wire:model="name"
                    name="name"
                    label="Car name"
                    placeholder="Enter actual car brand name"  />

            </div>
            <div class="mt-4">
                <x-impala-components.form.select-dropdown
                    wire:model="classification_id"
                    id="classification_id"
                    name="classification_id"
                    placeholder="Select Car class"
                    label="Select car class"
                    :selected="$classification_id"
                    :options="$filteredClasses" />

            </div>
            <div class="mt-4">
                <x-base.button
                wire:click="saveCarModel"
                class="px-2 py-1"
                variant="primary"
            >
                Add new car
            </x-base.button>
            </div>

            <div class="w-full border-t border-gray-200 mt-8"></div>
                @foreach ($carModels as $carModel)
                <div class="box mt-4">
                    <div class="col-span-12 intro-y sm:col-span-6">
                    </div>
                    <div  class="flex flex-col items-center p-5 lg:flex-row"> --}}
                        {{-- <div class="image-fit h-0 w-0 lg:mr-1 lg:h-12 lg:w-12"> --}}
                            {{-- <img
                                class="rounded-full"
                                src="{{ Vite::asset($faker['photos'][0]) }}"
                                alt="Midone - Tailwind Admin Dashboard Template"
                            {{-- /> --}} 
                        {{-- </div> --}}
                        {{-- <div {{ $isEditinCarModelId == $carModel->id ? 'hidden' : '' }}  class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left">
                            <a
                                class="font-medium"
                                href=""
                            >
                            {{  $carModel->name  }}
                            </a>
                            <div class="mt-0.5 text-xs text-slate-500">
                              
                                {{ $carModel->carClassification->classification }}
                               </div>
                            <div class="mt-0.5 text-xs text-slate-500">
                             Created on:  {{ getHumanDateFromCreatedAt($carModel->created_at) }}
                            </div>
                        </div> --}}

                        {{-- <div {{ $isEditinCarModelId == $carModel->id ? '' : 'hidden' }}>
                            <div class="col-span-12 intro-y sm:col-span-6">
                                <x-impala-components.form.primary-text-input
                                    wire:model="carModelName"
                                    name="carModelName"
                                    label="Car name"
                                    placeholder="Enter actual car brand name"  />
                
                            </div>
                            <div class="mt-4">
                                <x-impala-components.form.select-dropdown
                                    wire:model="selectedCarModelId"
                                    id="selectedCarModelId"
                                    name="selectedCarModelId"
                                    placeholder="Select Car class"
                                    label="Select car class"
                                    :selected="$selectedCarModelId"
                                    :options="$filteredClasses" />
                
                            </div>
                             <div class="flex items-start gap-8">
                                <div class="mt-4">
                                    <x-base.button
                                    wire:click="saveCarModelChanges({{ $carModel->id }})"
                                    class="px-2 py-1"
                                    variant="primary"
                                >
                                    Save Changes
                                </x-base.button>
                                </div>
                                <div class="mt-4">
                                    <x-base.button
                                    wire:click="cancelCarModelEdit()"
                                    class="px-2 py-1"
                                    variant="cancel"
                                >
                                    Cancel
                                </x-base.button>
                                </div>
                             </div>
                        </div> --}}



                        {{-- <div  class="mt-4 flex lg:mt-0">
                            <div {{ $isEditinCarModelId == $carModel->id ? 'hidden' : '' }}>
                                <x-base.button
                                wire:click="modifyCarModel({{ $carModel->id }})"
                                class="mr-2 px-2 py-1"
                                variant="Edit"
                            >
                                Edit
                            </x-base.button>
                            <x-base.button
                                class="px-2 py-1"
                                variant="outline-danger"
                            >
                                Delete
                            </x-base.button>
                        </div>
                        </div>
                    </div>
                </div> --}}
                
                {{-- @endforeach
                    {{ $carModels->links('vendor.livewire.custom-pagination') }}  
            </div>
            <div {{ session('car_model_created') ? '' : 'hidden' }} class="w-full pt-4">
                    <x-base.alert
                    class="flex justify-between mb-2"
                    variant="primary"
                    hidden
                >
        
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                        {{ session('car_model_created')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
            </div>
       </div> --}}
</div>



