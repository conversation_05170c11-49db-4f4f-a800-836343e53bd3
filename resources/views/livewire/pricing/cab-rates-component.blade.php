<div class="grid grid-cols-12 gap-6">
    <div class="col-span-12 lg:col-span-6 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-6">
        <div class="text-base font-medium">Add Taxi Rate</div>
            <div class="w-full border-t border-gray-200 mt-2"></div>
        
            
            <div class="mt-4">
                <x-impala-components.form.select-dropdown
                    wire:model="selectedCarClassId"
                    id="selectedCarClassId"
                    name="selectedCarClassId"
                    placeholder="Select class"
                    label="Select class"
                    :selected="$selectedCarClassId"
                    :options="$filteredClasses" />

            </div>

            <div class="col-span-12 intro-y sm:col-span-6">
                <x-impala-components.form.primary-text-input
                    wire:model="amount"
                    name="amount"
                    label="Amount per kilometer"
                    placeholder="Enter car rental rate per kilometer"  />

            </div>

            <div class="mt-4">
                <x-base.button
                wire:click="addRate()"
                class="px-2 py-1"
                variant="primary"
                :disabled="$isAddingCabRate"
            >
                Add new price
                <span wire:loading wire:target="addRate" class="ml-2">
                    <svg class="animate-spin h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                    </svg>
                </span>
            </x-base.button>
            </div>
            <div {{ session('cab_rate_created') ? '' : 'hidden' }} class="w-full pt-4">
                <x-base.alert
                class="flex justify-between mb-2"
                variant="primary"
                hidden
            >
    
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                    {{ session('cab_rate_created')  }}
                <x-base.alert.dismiss-button
                    class="text-white"
                    type="button"
                    aria-label="Close"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                </x-base.alert.dismiss-button>
            </x-base.alert>
        </div>

            <div class="w-full border-t border-gray-200 mt-8"></div>
                @foreach ($carRates as $cabRate)
                <div class="box mt-4">
                    <div class="col-span-12 intro-y sm:col-span-6">
                    </div>
                    <div  class="flex flex-col items-center p-5 lg:flex-row">
                        {{-- <div class="image-fit h-0 w-0 lg:mr-1 lg:h-12 lg:w-12"> --}}
                            {{-- <img
                                class="rounded-full"
                                src="{{ Vite::asset($faker['photos'][0]) }}"
                                alt="Midone - Tailwind Admin Dashboard Template"
                            {{-- /> --}} 
                        {{-- </div> --}}
                        <div {{ $isEditingCabRateId == $cabRate->id ? 'hidden' : '' }}  class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left">
                            <a
                                class="font-medium"
                                href=""
                            >
                            {{  $cabRate->carClassification->classification  }}
                            </a>
                            <div class="mt-0.5 text-medium font-bold text-slate-500">
                              
                                {{'$' . $cabRate->amount . ' '}} USD<span class="font-bold text-small"> (per km)</span>  
                               </div>
                            <div class="mt-0.5 text-xs text-slate-500">
                             Rate modified on:  {{ getHumanDateFromCreatedAt($cabRate->updated_at) }}
                            </div>
                        </div>

                        <div {{ $isEditingCabRateId == $cabRate->id ? '' : 'hidden' }}>
                            
                            <div class="mt-4">
                                <x-impala-components.form.select-dropdown
                                    wire:model="editSelectedCabRateId"
                                    id="editSelectedCabRateId"
                                    name="editSelectedCabRateId"
                                    placeholder="Select cab class"
                                    label="Select cab class"
                                    :selected="$editSelectedCabRateId"
                                    :options="$filteredClasses" />
                
                            </div>

                            <div class="col-span-12 intro-y sm:col-span-6">
                                <x-impala-components.form.primary-text-input
                                    wire:model="editAmount"
                                    name="editAmount"
                                    label="Amount per kilometer"
                                    placeholder="Enter amount per kilometer"  />
                
                            </div>
                             <div class="flex items-start gap-8">
                                <div class="mt-4">
                                    <x-base.button
                                    wire:click="saveChanges({{ $cabRate->id }})"
                                    class="px-2 py-1"
                                    variant="primary"
                                >
                                    Save Changes
                                </x-base.button>
                                </div>
                                <div class="mt-4">
                                    <x-base.button
                                    wire:click="cancelEdit"
                                    class="px-2 py-1"
                                    variant="cancel"
                                >
                                    Cancel
                                </x-base.button>
                                </div>
                             </div>
                        </div>

                        <div  class="mt-4 flex lg:mt-0">
                            <div {{ $isEditingCabRateId == $cabRate->id ? 'hidden' : '' }}>
                                <x-base.button
                                wire:click="edit({{ $cabRate->id }})"
                                class="mr-2 px-2 py-1"
                                variant="Edit"
                            >
                                Edit
                            </x-base.button>
                            <x-base.button
                               wire:click="delete({{ $cabRate->id }})"
                                class="px-2 py-1"
                                variant="outline-danger"
                                :disabled="$isDeletingCabRate ==  $cabRate->id"
                            >
                                <span wire:loading wire:target="delete({{ $cabRate->id }})" class="mr-2">
                                    <svg class="animate-spin h-4 w-4 text-red-600 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                                    </svg>
                                </span>
                                Delete
                            </x-base.button>
                        </div>
                        </div>
                    </div>    
                </div>
                
                @endforeach
                    {{ $carRates->links('vendor.livewire.custom-pagination') }}  
            </div>
            
       </div>
</div>



