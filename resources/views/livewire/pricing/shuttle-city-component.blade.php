<div class="grid grid-cols-12 gap-6">
    <div class="col-span-12 lg:col-span-6 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-6">
        <div class="text-base font-medium">Add city</div>
            <div class="w-full border-t border-gray-200 mt-2"></div>
            <div class="intro-y col-span-12 md:col-span-6 mt-2">
                <div class="flex pb-1 items-end">
                    <div>
                        <x-impala-components.form.primary-text-input
                        wire:model="city"
                        name="city"
                        label="Enter city"
                        placeholder="Enter city"  />
                    </div>
                </div>
                  
                <div class="pb-4 pt-1">
                    <x-base.button
                    wire:click="addCity"
                    class="mr-4 px-2"
                    variant="primary"
                >
                    Add City
                </x-base.button>
                </div>
                {{-- Show error message if duplicate city --}}
                <div {{ session('city_error') ? '' : 'hidden' }} class="w-full pt-2">
                    <x-base.alert
                        class="flex justify-between mb-2"
                        variant="danger"
                        hidden
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><line x1="12" y1="9" x2="12" y2="13"/><line x1="12" y1="17" x2="12.01" y2="17"/></svg>
                        {{ session('city_error')  }}
                        <x-base.alert.dismiss-button
                            class="text-white"
                            type="button"
                            aria-label="Close"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                        </x-base.alert.dismiss-button>
                    </x-base.alert>
                </div>
                {{-- Show success message if city deleted --}}
                <div {{ session('city_deleted') ? '' : 'hidden' }} class="w-full pt-2">
                    <x-base.alert
                        class="flex justify-between mb-2 bg-red-600 text-white"
                        variant="danger"
                        hidden
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                        {{ session('city_deleted')  }}
                        <x-base.alert.dismiss-button
                            class="text-white"
                            type="button"
                            aria-label="Close"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                        </x-base.alert.dismiss-button>
                    </x-base.alert>
                </div>
                <div {{ session('city_saved') ? '' : 'hidden' }} class="w-full">
                    <x-base.alert
                    class="flex justify-between mb-2"
                    variant="primary"
                    hidden
                >
                 
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                        {{ session('city_saved')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
            </div>
            <div class="w-full border-t border-gray-200 mt-8"></div>
                @foreach ($cities as $city)
                <div class="box mt-4" wire:key="{{ $city->id }}">
                    <div class="col-span-12 intro-y sm:col-span-6">
                    </div>
                    <div class="flex flex-col items-center p-5 lg:flex-row">
                        {{-- <div class="image-fit h-0 w-0 lg:mr-1 lg:h-12 lg:w-12"> --}}
                            {{-- <img
                                class="rounded-full"
                                src="{{ Vite::asset($faker['photos'][0]) }}"
                                alt="Midone - Tailwind Admin Dashboard Template"
                            {{-- /> --}} 
                        {{-- </div> --}}
                        <div class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left" {{ $isEditCityId == $city->id ? '' : 'hidden' }}>
                            <x-impala-components.form.primary-text-input
                            value="{{ $city->city_name  }}"
                            wire:model="editCity"
                            name="editCity"
                            label="Add city"
                            placeholder="Add city"  />
                        </div>
                        <div   {{ $isEditCityId == $city->id ? 'hidden' : '' }} class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left">
                            <a
                                class="font-medium"
                                href=""
                            >
                                {{ $city->city_name }}
                            </a>
                            <div class="mt-0.5 text-xs text-slate-500">
                             Created on:  {{ getHumanDateFromCreatedAt($city->updated_at) }}
                            </div>
                        </div>
                        <div class="mt-4 flex lg:mt-0" >
                            <div {{ $isEditCityId == $city->id ? 'hidden' : '' }}>
                            <x-base.button
                                wire:click="modifyCity({{ $city->id }})"
                                class="mr-2 px-2 py-1"
                                variant="Edit"
                            >
                                Edit
                            </x-base.button>
                            <x-base.button
                                wire:click="deleteCity({{ $city->id }})"
                                wire:loading.attr="disabled"
                                class="px-2 py-1"
                                variant="outline-danger"
                            >
                                <span wire:loading.remove wire:target="deleteCity({{ $city->id }})">Delete</span>
                                <span wire:loading wire:target="deleteCity({{ $city->id }})">Deleting...</span>
                            </x-base.button>
                           </div>

                           <div {{ $isEditCityId == $city->id ? '' : 'hidden' }}>
                            <x-base.button
                                wire:click="saveModifiedCityChanges({{ $city->id }})"
                                class="mr-2 px-2 py-1"
                                variant="primary"
                            >
                                Save Changes
                            </x-base.button>
                            <x-base.button
                                wire:click="cancelEditCity"
                                class="px-2 py-1"
                                variant="outline-danger"
                            >
                                Cancel 
                            </x-base.button>
                           </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div> 
    </div>


    <div class="col-span-12 lg:col-span-6 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-6">
        <div class="text-base font-medium">Add shuttle rate</div>
            <div class="w-full border-t border-gray-200 mt-2"></div>
        
            <div class="mt-4">
                <x-impala-components.form.select-dropdown
                    wire:model="selectedCarClass"
                    id="selectedCarClass"
                    name="selectedCarClass"
                    placeholder="Select car class"
                    label="Select car class"
                    :selected="$selectedCarClass"
                    :options="$filteredCarClasses" />

            </div>

            <div class="mt-4">
                <x-impala-components.form.select-dropdown
                    wire:model="selectedCity"
                    id="selectedCity"
                    name="selectedCity"
                    placeholder="Select city"
                    label="Select city"
                    :selected="$selectedCity"
                    :options="$filteredCities" />

            </div>


            <div class="col-span-12 intro-y sm:col-span-6">
                <x-impala-components.form.primary-text-input
                    wire:model="amount"
                    name="amount"
                    label="Amount (per km)"
                    placeholder="Enter price per km"  />

            </div>

            <div class="mt-4">
                <x-base.button
                wire:click="saveShuttleRate"
                wire:loading.attr="disabled"
                class="px-2 py-1"
                variant="primary"
            >
                <span wire:loading.remove wire:target="saveShuttleRate">Add Shuttle Rate</span>
                <span wire:loading wire:target="saveShuttleRate">Adding...</span>
            </x-base.button>
            </div>
            {{-- Show message when shuttle rate has been added successfully --}}
            <div {{ session('shuttle_rate_created') ? '' : 'hidden' }} class="w-full pt-4">
                <x-base.alert
                    class="flex justify-between mb-2"
                    variant="primary"
                    hidden
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                    {{ session('shuttle_rate_created')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
            </div>
            {{-- Show message when shuttle rate has been deleted successfully --}}
            <div {{ session('shuttle_rate_deleted') ? '' : 'hidden' }} class="w-full pt-4">
                <x-base.alert
                    class="flex justify-between mb-2 bg-red-600 text-white"
                    variant="danger"
                    hidden
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2"><path d="M3 6h18"/><path d="M8 6v12a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V6"/><path d="M10 11v6"/><path d="M14 11v6"/><path d="M5 6V4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2"/></svg>
                    {{ session('shuttle_rate_deleted')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
            </div>
            <div class="w-full border-t border-gray-200 mt-8"></div>
            <div class="flex flex-col items-end pt-4">
                <x-impala-components.form.outline-plain-button
                wire:click="resetFilter"
                title="Reset" />
            </div>
            <div class="flex flex-col items-end gap-4  p-2 lg:flex-row">
                <div class="col-span-12 intro-y sm:col-span-6">
                    <x-impala-components.form.select-dropdown
                    wire:model="selectedFilterCity"
                    wire:change="filterByCity"
                    id="selectedFilterCity"
                    name="selectedFilterCity"
                    placeholder="Filter by city"
                    label="Filter by city"
                    :selected="$selectedFilterCity"
                    :options="$filteredCities" />
                   
                </div>
            </div>
              
                @if ($selectedFilterCity == 0)

                @foreach ($this->shuttles as $shuttlePrice)
                <div class="box mt-4">
                    <div class="col-span-12 intro-y sm:col-span-6">
                    </div>
                    <div  class="flex flex-col items-center p-5 lg:flex-row">
                        {{-- <div class="image-fit h-0 w-0 lg:mr-1 lg:h-12 lg:w-12"> --}}
                            {{-- <img
                                class="rounded-full"
                                src="{{ Vite::asset($faker['photos'][0]) }}"
                                alt="Midone - Tailwind Admin Dashboard Template"
                            {{-- /> --}} 
                        {{-- </div> --}}
                        <div {{ $isShuttleId == $shuttlePrice->id ? 'hidden' : '' }}  class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left">
                            <a
                                class="font-medium"
                                href=""
                            >
                            {{  $shuttlePrice->city->city_name  }}
                            </a>
                            <div class="mt-0.5 text-md text-slate-500">
                              
                                {{ $shuttlePrice->carClassification->classification }}
                            </div>
                            
                            <div class="mt-0.5 text-xs font-bold text-slate-500">
                            
                                {{'$'. $shuttlePrice->price_per_km . ' ' . 'USD per km' }}
                            </div>
                            <div class="mt-0.5 text-xs text-slate-500">
                             Modified on:  {{ getHumanDateFromCreatedAt($shuttlePrice->updated_at) }}
                            </div>
                        </div>

                        <div {{ $isShuttleId == $shuttlePrice->id ? '' : 'hidden' }}>
                           
                            <div class="mt-4">
                                <x-impala-components.form.select-dropdown
                                    wire:model="selectedEditCarClass"
                                    id="selectedEditCarClass"
                                    name="selectedEditCarClass"
                                    placeholder="Select car class"
                                    label="Select car class"
                                    :selected="$selectedEditCarClass"
                                    :options="$filteredCarClasses" />
                            </div>

                            <div class="mt-4">
                                <x-impala-components.form.select-dropdown
                                    wire:model="selectedEditCity"
                                    id="selectedEditCity"
                                    name="selectedEditCity"
                                    placeholder="Select city "
                                    label="Select city"
                                    :selected="$selectedEditCity"
                                    :options="$filteredCities" />
                            </div>

                            <div class="col-span-12 intro-y sm:col-span-6">
                                <x-impala-components.form.primary-text-input
                                    wire:model="editAmount"
                                    name="editAmount"
                                    label="Enter amount"
                                    placeholder="Enter amount"  />
                
                            </div>
                             <div class="flex items-start gap-8">
                                <div class="mt-4">
                                    <x-base.button
                                    wire:click="saveModifiedShuttleRateChanges({{ $shuttlePrice->id }})"
                                    class="px-2 py-1"
                                    variant="primary"
                                >
                                    Save Changes
                                </x-base.button>
                                </div>
                                <div class="mt-4">
                                    <x-base.button
                                    wire:click="cancelEditShuttleRate"
                                    class="px-2 py-1"
                                    variant="cancel"
                                >
                                    Cancel
                                </x-base.button>
                                </div>
                             </div>
                        </div>



                        <div  class="mt-4 flex lg:mt-0">
                            <div {{ $isShuttleId == $shuttlePrice->id ? 'hidden' : '' }}>
                                <x-base.button
                                wire:click="modifyShuttleRate({{ $shuttlePrice->id }})"
                                class="mr-2 px-2 py-1"
                                variant="Edit"
                            >
                                Edit
                            </x-base.button>
                            <x-base.button
                                wire:click="deleteShuttleRate({{ $shuttlePrice->id }})"
                                wire:loading.attr="disabled"
                                class="px-2 py-1"
                                variant="outline-danger"
                            >
                                <span wire:loading.remove wire:target="deleteShuttleRate({{ $shuttlePrice->id }})">Delete</span>
                                <span wire:loading wire:target="deleteShuttleRate({{ $shuttlePrice->id }})">Deleting...</span>
                            </x-base.button>
                        </div>
                        </div>
                    </div>
                </div>
                
                @endforeach
              
                 {{ $this->shuttles->links('vendor.livewire.custom-pagination') }}  
               
                @elseif ($selectedFilterCity != 0)
                
                @foreach ($this->filteredShuttles as $shuttlePrice)
                <div class="box mt-4">
                    <div class="col-span-12 intro-y sm:col-span-6">
                    </div>
                    <div  class="flex flex-col items-center p-5 lg:flex-row">
                        {{-- <div class="image-fit h-0 w-0 lg:mr-1 lg:h-12 lg:w-12"> --}}
                            {{-- <img
                                class="rounded-full"
                                src="{{ Vite::asset($faker['photos'][0]) }}"
                                alt="Midone - Tailwind Admin Dashboard Template"
                            {{-- /> --}} 
                        {{-- </div> --}}
                        <div {{ $isShuttleId == $shuttlePrice->id ? 'hidden' : '' }}  class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left">
                            <a
                                class="font-medium"
                                href=""
                            >
                            {{  $shuttlePrice->city->city_name  }}
                            </a>
                            <div class="mt-0.5 text-md text-slate-500">
                              
                                {{ $shuttlePrice->carClassification->classification }}
                            </div>
                            
                            <div class="mt-0.5 text-xs font-bold text-slate-500">
                            
                                {{'$'. $shuttlePrice->price_per_km . ' ' . 'USD per km' }}
                            </div>
                            <div class="mt-0.5 text-xs text-slate-500">
                             Modified on:  {{ getHumanDateFromCreatedAt($shuttlePrice->updated_at) }}
                            </div>
                        </div>

                        <div {{ $isShuttleId == $shuttlePrice->id ? '' : 'hidden' }}>
                           
                            <div class="mt-4">
                                <x-impala-components.form.select-dropdown
                                    wire:model="selectedEditCarClass"
                                    id="selectedEditCarClass"
                                    name="selectedEditCarClass"
                                    placeholder="Select car class"
                                    label="Select car class"
                                    :selected="$selectedEditCarClass"
                                    :options="$filteredCarClasses" />
                            </div>

                            <div class="mt-4">
                                <x-impala-components.form.select-dropdown
                                    wire:model="selectedEditCity"
                                    id="selectedEditCity"
                                    name="selectedEditCity"
                                    placeholder="Select city "
                                    label="Select city"
                                    :selected="$selectedEditCity"
                                    :options="$filteredCities" />
                            </div>

                            <div class="col-span-12 intro-y sm:col-span-6">
                                <x-impala-components.form.primary-text-input
                                    wire:model="editAmount"
                                    name="editAmount"
                                    label="Enter amount"
                                    placeholder="Enter amount"  />
                
                            </div>
                             <div class="flex items-start gap-8">
                                <div class="mt-4">
                                    <x-base.button
                                    wire:click="saveModifiedShuttleRateChanges({{ $shuttlePrice->id }})"
                                    class="px-2 py-1"
                                    variant="primary"
                                >
                                    Save Changes
                                </x-base.button>
                                </div>
                                <div class="mt-4">
                                    <x-base.button
                                    wire:click="cancelEditShuttleRate"
                                    class="px-2 py-1"
                                    variant="cancel"
                                >
                                    Cancel
                                </x-base.button>
                                </div>
                             </div>
                        </div>



                        <div  class="mt-4 flex lg:mt-0">
                            <div {{ $isShuttleId == $shuttlePrice->id ? 'hidden' : '' }}>
                                <x-base.button
                                wire:click="modifyShuttleRate({{ $shuttlePrice->id }})"
                                class="mr-2 px-2 py-1"
                                variant="Edit"
                            >
                                Edit
                            </x-base.button>
                            <x-base.button
                                wire:click="deleteShuttleRate({{ $shuttlePrice->id }})"
                                wire:loading.attr="disabled"
                                class="px-2 py-1"
                                variant="outline-danger"
                            >
                                <span wire:loading.remove wire:target="deleteShuttleRate({{ $shuttlePrice->id }})">Delete</span>
                                <span wire:loading wire:target="deleteShuttleRate({{ $shuttlePrice->id }})">Deleting...</span>
                            </x-base.button>
                        </div>
                        </div>
                    </div>
                </div>
                
                @endforeach
                    {{ $this->filteredShuttles->links('vendor.livewire.custom-pagination') }}  
                @endif
            </div>
       </div>
</div>

{{-- Add this inside your Blade component script section --}}
<script>
    document.addEventListener('livewire:load', function () {
        Livewire.on('refreshCities', () => {
            @this.set('cities', @this.cities.sort((a, b) => b.id - a.id));
        });
    });
</script>



