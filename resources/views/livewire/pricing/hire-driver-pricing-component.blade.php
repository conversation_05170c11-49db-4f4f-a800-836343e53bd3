<div class="grid grid-cols-12 gap-6">
    <div class="col-span-12 lg:col-span-6 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-6">
        <div class="text-base font-medium">Driver hiring rates</div>
            <div class="w-full border-t border-gray-200 mt-2"></div>
            <div class="intro-y col-span-12 md:col-span-6 mt-2">
                <div class="flex pb-1 items-end">
                    <div>
                        <x-impala-components.form.primary-text-input
                        wire:model="hireDriverType"
                        name="hireDriverType"
                        label="Hire driver type"
                        placeholder="Driver hiring type"  />
                    </div>
                </div>
                <div class="pb-4 pt-1">
                    <x-base.button
                    wire:click="addHireDriverType()"
                    class="mr-4 px-2"
                    variant="primary"
                >
                    Add Hiring Type
                </x-base.button>
                </div>
                
                <div {{ session('hire_driver_type_created') ? '' : 'hidden' }} class="w-full">
                    <x-base.alert
                    class="flex justify-between mb-2"
                    variant="primary"
                    hidden
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                        {{ session('hire_driver_type_created')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
                </div>
                {{-- Show message when hire driver type is deleted (same place as add message) --}}
                <div {{ session('hire_driver_type_deleted') ? '' : 'hidden' }} class="w-full">
                    <x-base.alert
                        class="flex justify-between mb-2"
                        variant="danger"
                        hidden
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2"><path d="M3 6h18"/><path d="M8 6v12a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V6"/><path d="M10 11v6"/><path d="M14 11v6"/><path d="M5 6V4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2"/></svg>
                        {{ session('hire_driver_type_deleted')  }}
                        <x-base.alert.dismiss-button
                            class="text-white"
                            type="button"
                            aria-label="Close"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                        </x-base.alert.dismiss-button>
                    </x-base.alert>
                </div>
            <div class="w-full border-t border-gray-200 mt-8"></div>
                @foreach ($hiringTypes as $hiringType)
                <div class="box mt-4" wire:key="{{ $hiringType->id }}">
                    <div class="col-span-12 intro-y sm:col-span-6">
                    </div>
                    <div class="flex flex-col items-center p-5 lg:flex-row">
                        {{-- <div class="image-fit h-0 w-0 lg:mr-1 lg:h-12 lg:w-12"> --}}
                            {{-- <img
                                class="rounded-full"
                                src="{{ Vite::asset($faker['photos'][0]) }}"
                                alt="Midone - Tailwind Admin Dashboard Template"
                            {{-- /> --}} 
                        {{-- </div> --}}
                        <div class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left" {{ $isEditingDriverHireTypeId == $hiringType->id ? '' : 'hidden' }}>
                            <x-impala-components.form.primary-text-input
                            value="{{ $hiringType->hire_type  }}"
                            wire:model="editHireDriverType"
                            name="editHireDriverType"
                            label="Hire driver type"
                            placeholder="Hire driver type"  />
                        </div>
                        <div   {{ $isEditingDriverHireTypeId == $hiringType->id ? 'hidden' : '' }} class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left">
                            <a
                                class="font-medium"
                                href=""
                            >
                                {{ $hiringType->hire_type }}
                            </a>
                            <div class="mt-0.5 text-xs text-slate-500">
                             Created on:  {{ getHumanDateFromCreatedAt($hiringType->updated_at) }}
                            </div>
                        </div>
                        <div class="mt-4 flex lg:mt-0" >
                            <div {{ $isEditingDriverHireTypeId == $hiringType->id ? 'hidden' : '' }}>
                            <x-base.button
                                wire:click="modifyHireDriverType({{ $hiringType->id }})"
                                class="mr-2 px-2 py-1"
                                variant="Edit"
                            >
                                Edit
                            </x-base.button>
                            <x-base.button
                                wire:click="deleteHireDriverType({{ $hiringType->id }})"
                                class="px-2 py-1"
                                variant="outline-danger"
                            >
                                Delete
                            </x-base.button>
                           </div>

                           <div {{ $isEditingDriverHireTypeId == $hiringType->id ? '' : 'hidden' }}>
                            <x-base.button
                                wire:click="saveHireDriverType({{ $hiringType->id }})"
                                class="mr-2 px-2 py-1"
                                variant="primary"
                            >
                                Save Changes
                            </x-base.button>
                            <x-base.button
                                wire:click="cancelEditHireType()"
                                class="px-2 py-1"
                                variant="outline-danger"
                            >
                                Cancel 
                            </x-base.button>
                           </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div> 
    </div>


    <div class="col-span-12 lg:col-span-6 intro-y box mt-5 px-5 pt-5 pb-8 sm:col-span-6">
        <div class="text-base font-medium">Add Hire Driver Rate</div>
            <div class="w-full border-t border-gray-200 mt-2"></div>
        
            <div class="mt-4">
                <x-impala-components.form.select-dropdown
                    wire:model="selectedHireDriverRateId"
                    id="selectedHireDriverRateId"
                    name="selectedHireDriverRateId"
                    placeholder="Select hiring rate"
                    label="Select hiring rate"
                    :selected="$selectedHireDriverRateId"
                    :options="$filteredHiringTypes" />

            </div>

            <div class="col-span-12 intro-y sm:col-span-6">
                <x-impala-components.form.primary-text-input
                    wire:model="amount"
                    name="amount"
                    label="Amount"
                    placeholder="Enter hiring amount"  />

            </div>

            <div class="mt-4">
                <x-base.button
                wire:click="saveHiringRate"
                class="px-2 py-1"
                variant="primary"
            >
                Add new rate
            </x-base.button>
            </div>

            {{-- Show message when new hire driver rate is added just below the Add new rate button --}}
            <div {{ session('driver_hiring_rate_created') ? '' : 'hidden' }} class="w-full pt-4">
                <x-base.alert
                    class="flex justify-between mb-2"
                    variant="primary"
                    hidden
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                    {{ session('driver_hiring_rate_created')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
            </div>

            <div class="w-full border-t border-gray-200 mt-8"></div>
                @foreach ($hireDriverRates as $hireDriverRate)
                <div class="box mt-4">
                    <div class="col-span-12 intro-y sm:col-span-6">
                    </div>
                    <div  class="flex flex-col items-center p-5 lg:flex-row">
                        {{-- <div class="image-fit h-0 w-0 lg:mr-1 lg:h-12 lg:w-12"> --}}
                            {{-- <img
                                class="rounded-full"
                                src="{{ Vite::asset($faker['photos'][0]) }}"
                                alt="Midone - Tailwind Admin Dashboard Template"
                            {{-- /> --}} 
                        {{-- </div> --}}
                        <div {{ $isEditingDriverRateId == $hireDriverRate->id ? 'hidden' : '' }}  class="mt-3 text-center lg:ml-2 lg:mr-auto lg:mt-0 lg:text-left">
                            <a
                                class="font-medium"
                                href=""
                            >
                            {{  $hireDriverRate->hireDriverType->hire_type  }}
                            </a>
                            <div class="mt-0.5 text-xs text-slate-500">
                              
                                {{ $hireDriverRate->amount }}
                               </div>
                            <div class="mt-0.5 text-xs text-slate-500">
                             Modified on:  {{ getHumanDateFromCreatedAt($hireDriverRate->updated_at) }}
                            </div>
                        </div>

                        <div {{ $isEditingDriverRateId == $hireDriverRate->id ? '' : 'hidden' }}>
                           
                            <div class="mt-4">
                                <x-impala-components.form.select-dropdown
                                    wire:model="selectedEditHireDriverRateId"
                                    id="selectedEditHireDriverRateId"
                                    name="selectedEditHireDriverRateId"
                                    placeholder="Select hiring rate"
                                    label="Select hiring rate"
                                    :selected="$selectedEditHireDriverRateId"
                                    :options="$filteredHiringTypes" />
                            </div>

                            <div class="col-span-12 intro-y sm:col-span-6">
                                <x-impala-components.form.primary-text-input
                                    wire:model="editHireAmount"
                                    name="editHireAmount"
                                    label="Enter amount"
                                    placeholder="Enter amount"  />
                
                            </div>
                             <div class="flex items-start gap-8">
                                <div class="mt-4">
                                    <x-base.button
                                    wire:click="saveHireRateChanges({{ $hireDriverRate->id }})"
                                    class="px-2 py-1"
                                    variant="primary"
                                >
                                    Save Changes
                                </x-base.button>
                                </div>
                                <div class="mt-4">
                                    <x-base.button
                                    wire:click="cancelEditHireDriverRate()"
                                    class="px-2 py-1"
                                    variant="cancel"
                                >
                                    Cancel
                                </x-base.button>
                                </div>
                             </div>
                        </div>



                        <div  class="mt-4 flex lg:mt-0">
                            <div {{ $isEditingDriverRateId == $hireDriverRate->id ? 'hidden' : '' }}>
                                <x-base.button
                                wire:click="editHireDriverRate({{ $hireDriverRate->id }})"
                                class="mr-2 px-2 py-1"
                                variant="Edit"
                            >
                                Edit
                            </x-base.button>
                            <x-base.button
                                wire:click="deleteHireDriverRate({{ $hireDriverRate->id }})"
                                class="px-2 py-1"
                                variant="outline-danger"
                                :disabled="$isDeletingHireDriverRate == $hireDriverRate->id"
                            >
                                <span wire:loading wire:target="deleteHireDriverRate({{ $hireDriverRate->id }})" class="mr-2">
                                    <svg class="animate-spin h-4 w-4 text-red-600 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                                    </svg>
                                </span>
                                Delete
                            </x-base.button>
                        </div>
                        </div>
                    </div>
                </div>
                
                @endforeach
                    {{ $hireDriverRates->links('vendor.livewire.custom-pagination') }}  
            </div>
            {{-- Show message when rate is deleted (top right, 10% from top, clickable, high z-index) --}}
            <div 
                {{ session('hire_driver_rate_deleted') ? '' : 'hidden' }} 
                style="position: fixed; top: 10vh; right: 2vw; z-index: 9999; width: auto; max-width: 22rem; cursor: pointer;"
                onclick="this.style.display='none';"
            >
                <x-base.alert
                    class="flex justify-between mb-2 shadow-lg"
                    variant="danger"
                    style="pointer-events: auto;"
                    hidden
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2"><path d="M3 6h18"/><path d="M8 6v12a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V6"/><path d="M10 11v6"/><path d="M14 11v6"/><path d="M5 6V4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2"/></svg>
                    {{ session('hire_driver_rate_deleted')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                        onclick="this.closest('div[style]').style.display='none'; event.stopPropagation();"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
            </div>

            {{-- Show message when hire driver type is deleted (top right, 10% from top, clickable, high z-index) --}}
            <div 
                {{ session('hire_driver_type_deleted') ? '' : 'hidden' }} 
                style="position: fixed; top: 16vh; right: 2vw; z-index: 9999; width: auto; max-width: 22rem; cursor: pointer;"
                onclick="this.style.display='none';"
            >
                <x-base.alert
                    class="flex justify-between mb-2 shadow-lg"
                    variant="danger"
                    style="pointer-events: auto;"
                    hidden
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2"><path d="M3 6h18"/><path d="M8 6v12a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V6"/><path d="M10 11v6"/><path d="M14 11v6"/><path d="M5 6V4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2"/></svg>
                    {{ session('hire_driver_type_deleted')  }}
                    <x-base.alert.dismiss-button
                        class="text-white"
                        type="button"
                        aria-label="Close"
                        onclick="this.closest('div[style]').style.display='none'; event.stopPropagation();"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                    </x-base.alert.dismiss-button>
                </x-base.alert>
            </div>
        </div>
    </div>



