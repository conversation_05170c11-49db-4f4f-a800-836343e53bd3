<div>
    <div class="grid grid-cols-12 gap-6">
        <div class="col-span-12 2xl:col-span-9">
           
            <div class="grid grid-cols-12 gap-6">
                <!-- BEGIN: General Report -->
                <div class="col-span-12 mt-8">
                    <div class="intro-y flex h-10 items-center">
                        <h2 class="mr-5 truncate text-lg font-medium">General Report</h2>
                        <a
                            class="ml-auto flex items-center text-primary"
                            href=""
                        >
                            <x-base.lucide
                                class="mr-3 h-4 w-4"
                                icon="RefreshCcw"
                            /> Reload Data
                        </a>
                    </div>
                    <div class="mt-5 grid grid-cols-12 gap-6">
                        <div class="intro-y col-span-12 sm:col-span-6 xl:col-span-3">
                            <div @class([
                                'relative zoom-in',
                                "before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-['']",
                            ])>
                                <div class="box p-5">
                                    <div class="flex">
                                        <x-base.lucide
                                            class="h-[28px] w-[28px] text-primary"
                                            icon="ShoppingCart"
                                        />
                                        <div class="ml-auto">
                                            <x-base.tippy
                                                class="flex cursor-pointer items-center rounded-full bg-success py-[3px] pl-2 pr-1 text-xs font-medium text-white"
                                                as="div"
                                                content="33% Higher than last month"
                                            >
                                                33%
                                                <x-base.lucide
                                                    class="ml-0.5 h-4 w-4"
                                                    icon="ChevronUp"
                                                />
                                            </x-base.tippy>
                                        </div>
                                    </div>
                                    <div class="mt-6 text-3xl font-medium leading-8">{{ $taxiBookingCount }}</div>
                                    <div class="mt-1 text-base text-slate-500">Cab bookings</div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-y col-span-12 sm:col-span-6 xl:col-span-3">
                            <div @class([
                                'relative zoom-in',
                                "before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-['']",
                            ])>
                                <div class="box p-5">
                                    <div class="flex">
                                        <x-base.lucide
                                            class="h-[28px] w-[28px] text-pending"
                                            icon="CreditCard"
                                        />
                                        <div class="ml-auto">
                                            <x-base.tippy
                                                class="flex cursor-pointer items-center rounded-full bg-danger py-[3px] pl-2 pr-1 text-xs font-medium text-white"
                                                as="div"
                                                content="2% Lower than last month"
                                            >
                                                2%
                                                <x-base.lucide
                                                    class="ml-0.5 h-4 w-4"
                                                    icon="ChevronDown"
                                                />
                                            </x-base.tippy>
                                        </div>
                                    </div>
                                    <div class="mt-6 text-3xl font-medium leading-8">{{ $shuttleBookingCount }}</div>
                                    <div class="mt-1 text-base text-slate-500">Shuttle bookings</div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-y col-span-12 sm:col-span-6 xl:col-span-3">
                            <div @class([
                                'relative zoom-in',
                                "before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-['']",
                            ])>
                                <div class="box p-5">
                                    <div class="flex">
                                        <x-base.lucide
                                            class="h-[28px] w-[28px] text-warning"
                                            icon="Monitor"
                                        />
                                        <div class="ml-auto">
                                            <x-base.tippy
                                                class="flex cursor-pointer items-center rounded-full bg-success py-[3px] pl-2 pr-1 text-xs font-medium text-white"
                                                as="div"
                                                content="12% Higher than last month"
                                            >
                                                12%
                                                <x-base.lucide
                                                    class="ml-0.5 h-4 w-4"
                                                    icon="ChevronUp"
                                                />
                                            </x-base.tippy>
                                        </div>
                                    </div>
                                    <div class="mt-6 text-3xl font-medium leading-8">{{ $carRentalBookingCount }}</div>
                                    <div class="mt-1 text-base text-slate-500">
                                        Car rental requests
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-y col-span-12 sm:col-span-6 xl:col-span-3">
                            <div @class([
                                'relative zoom-in',
                                "before:box before:absolute before:inset-x-3 before:mt-3 before:h-full before:bg-slate-50 before:content-['']",
                            ])>
                                <div class="box p-5">
                                    <div class="flex">
                                        <x-base.lucide
                                            class="h-[28px] w-[28px] text-success"
                                            icon="User"
                                        />
                                        <div class="ml-auto">
                                            <x-base.tippy
                                                class="flex cursor-pointer items-center rounded-full bg-success py-[3px] pl-2 pr-1 text-xs font-medium text-white"
                                                as="div"
                                                content="22% Higher than last month"
                                            >
                                                22%
                                                <x-base.lucide
                                                    class="ml-0.5 h-4 w-4"
                                                    icon="ChevronUp"
                                                />
                                            </x-base.tippy>
                                        </div>
                                    </div>
                                    <div class="mt-6 text-3xl font-medium leading-8">{{ $driverBookingCount }}</div>
                                    <div class="mt-1 text-base text-slate-500">
                                        Driver bookings
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END: General Report -->
                <!-- BEGIN: Sales Report -->
                
                
                
                <!-- BEGIN: Official Store -->
                <div class="col-span-12 mt-6 xl:col-span-8">
                    <div class="intro-y box text-lg font-semibold mt-12 p-5 sm:mt-5">
                        <div>
                            Bookings Locations
                        </div>
                        <div wire:ignore id="map" class="h-screen mt-4" style="width: 100%;"></div>
                    </div>
                </div>
                <!-- END: Official Store -->
                <!-- BEGIN: Weekly Best Sellers -->
                <div class="col-span-12 mt-6 xl:col-span-4">
                    <div class="intro-y flex h-10 items-center">
                        <h2 class="mr-5 truncate text-lg font-medium">
                            Car rental requests
                        </h2>
                    </div>
                    <div class="mt-5">
                        @foreach ($carRentalBookings as $carRental)
                            <div class="intro-y">
                                <div class="box zoom-in mb-3 flex items-center px-4 py-4">
                                    <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-md">
                                        <img
                                            src="{{ $carRental->client->supabase_image_url }}"
                                            alt="Midone - Tailwind Admin Dashboard Template"
                                        />
                                    </div>
                                    <div class="ml-4 mr-auto">
                                        <div class="font-medium">{{ $carRental->client->name }}</div>
                                        <div class="mt-0.5 text-xs text-slate-500">
                                            {{  convertDateToHumanFormat($carRental->booking_date) }}
                                        </div>
                                    </div>
                                    <div
                                        class="cursor-pointer rounded-full bg-success px-2 py-1 text-xs font-small text-white">
                                       {{ $carRental->rentalPrice->carClassification->classification }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                        <a
                            class="intro-y block w-full rounded-md border border-dotted border-slate-400 py-4 text-center text-slate-500 dark:border-darkmode-300"
                            href="{{ route('view_car_rental_bookings') }}"
                        >
                            View More
                        </a>
                    </div>
                </div>
                <!-- END: Weekly Best Sellers -->
                <!-- BEGIN: General Report -->
                <div class="col-span-12 mt-8 grid grid-cols-12 gap-6">
                        
                </div>
                <!-- END: General Report -->
                <!-- BEGIN: Weekly Top Products -->
                {{-- <div class="col-span-12 mt-6">
                    <div class="intro-y block h-10 items-center sm:flex">
                        <h2 class="mr-5 truncate text-lg font-medium">
                            Weekly Top Products
                        </h2>
                        <div class="mt-3 flex items-center sm:ml-auto sm:mt-0">
                            <x-base.button class="!box flex items-center text-slate-600 dark:text-slate-300">
                                <x-base.lucide
                                    class="mr-2 hidden h-4 w-4 sm:block"
                                    icon="FileText"
                                />
                                Export to Excel
                            </x-base.button>
                            <x-base.button class="!box ml-3 flex items-center text-slate-600 dark:text-slate-300">
                                <x-base.lucide
                                    class="mr-2 hidden h-4 w-4 sm:block"
                                    icon="FileText"
                                />
                                Export to PDF
                            </x-base.button>
                        </div>
                    </div>
                    <div class="intro-y mt-8 overflow-auto sm:mt-0 lg:overflow-visible">
                        <x-base.table class="border-separate border-spacing-y-[10px] sm:mt-2">
                            <x-base.table.thead>
                                <x-base.table.tr>
                                    <x-base.table.th class="whitespace-nowrap border-b-0">
                                        IMAGES
                                    </x-base.table.th>
                                    <x-base.table.th class="whitespace-nowrap border-b-0">
                                        PRODUCT NAME
                                    </x-base.table.th>
                                    <x-base.table.th class="whitespace-nowrap border-b-0 text-center">
                                        STOCK
                                    </x-base.table.th>
                                    <x-base.table.th class="whitespace-nowrap border-b-0 text-center">
                                        STATUS
                                    </x-base.table.th>
                                    <x-base.table.th class="whitespace-nowrap border-b-0 text-center">
                                        ACTIONS
                                    </x-base.table.th>
                                </x-base.table.tr>
                            </x-base.table.thead>
                            <x-base.table.tbody>
                                @foreach (array_slice($fakers, 0, 4) as $faker)
                                    <x-base.table.tr class="intro-x">
                                        <x-base.table.td
                                            class="box w-40 rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                        >
                                            <div class="flex">
                                                <div class="image-fit zoom-in h-10 w-10">
                                                    <x-base.tippy
                                                        class="rounded-full shadow-[0px_0px_0px_2px_#fff,_1px_1px_5px_rgba(0,0,0,0.32)] dark:shadow-[0px_0px_0px_2px_#3f4865,_1px_1px_5px_rgba(0,0,0,0.32)]"
                                                        src="{{ Vite::asset($faker['images'][0]) }}"
                                                        alt="Midone - Tailwind Admin Dashboard Template"
                                                        as="img"
                                                        content="{{ 'Uploaded at ' . $faker['dates'][0] }}"
                                                    />
                                                </div>
                                                <div class="image-fit zoom-in -ml-5 h-10 w-10">
                                                    <x-base.tippy
                                                        class="rounded-full shadow-[0px_0px_0px_2px_#fff,_1px_1px_5px_rgba(0,0,0,0.32)] dark:shadow-[0px_0px_0px_2px_#3f4865,_1px_1px_5px_rgba(0,0,0,0.32)]"
                                                        src="{{ Vite::asset($faker['photos'][1]) }}"
                                                        alt="Midone - Tailwind Admin Dashboard Template"
                                                        as="img"
                                                        content="{{ 'Uploaded at ' . $faker['dates'][1] }}"
                                                    />
                                                </div>
                                                <div class="image-fit zoom-in -ml-5 h-10 w-10">
                                                    <x-base.tippy
                                                        class="rounded-full shadow-[0px_0px_0px_2px_#fff,_1px_1px_5px_rgba(0,0,0,0.32)] dark:shadow-[0px_0px_0px_2px_#3f4865,_1px_1px_5px_rgba(0,0,0,0.32)]"
                                                        src="{{ Vite::asset($faker['photos'][2]) }}"
                                                        alt="Midone - Tailwind Admin Dashboard Template"
                                                        as="img"
                                                        content="{{ 'Uploaded at ' . $faker['dates'][2] }}"
                                                    />
                                                </div>
                                            </div>
                                        </x-base.table.td>
                                        <x-base.table.td
                                            class="box rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                        >
                                            <a
                                                class="whitespace-nowrap font-medium"
                                                href=""
                                            >
                                                {{ $faker['products'][0]['name'] }}
                                            </a>
                                            <div class="mt-0.5 whitespace-nowrap text-xs text-slate-500">
                                                {{ $faker['products'][0]['category'] }}
                                            </div>
                                        </x-base.table.td>
                                        <x-base.table.td
                                            class="box rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                        >
                                            {{ $faker['stocks'][0] }}
                                        </x-base.table.td>
                                        <x-base.table.td
                                            class="box w-40 rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                        >
                                            <div @class([
                                                'flex items-center justify-center',
                                                'text-success' => $faker['true_false'][0],
                                                'text-danger' => !$faker['true_false'][0],
                                            ])>
                                                <x-base.lucide
                                                    class="mr-2 h-4 w-4"
                                                    icon="CheckSquare"
                                                />
                                                {{ $faker['true_false'][0] ? 'Active' : 'Inactive' }}
                                            </div>
                                        </x-base.table.td>
                                        <x-base.table.td @class([
                                            'box w-56 rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600',
                                            'before:absolute before:inset-y-0 before:left-0 before:my-auto before:block before:h-8 before:w-px before:bg-slate-200 before:dark:bg-darkmode-400',
                                        ])>
                                            <div class="flex items-center justify-center">
                                                <a
                                                    class="mr-3 flex items-center"
                                                    href=""
                                                >
                                                    <x-base.lucide
                                                        class="mr-1 h-4 w-4"
                                                        icon="CheckSquare"
                                                    />
                                                    Edit
                                                </a>
                                                <a
                                                    class="flex items-center text-danger"
                                                    href=""
                                                >
                                                    <x-base.lucide
                                                        class="mr-1 h-4 w-4"
                                                        icon="Trash"
                                                    />
                                                    Delete
                                                </a>
                                            </div>
                                        </x-base.table.td>
                                    </x-base.table.tr>
                                @endforeach
                            </x-base.table.tbody>
                        </x-base.table>
                    </div>
                    <div class="intro-y mt-3 flex flex-wrap items-center sm:flex-row sm:flex-nowrap">
                        <x-base.pagination class="w-full sm:mr-auto sm:w-auto">
                            <x-base.pagination.link>
                                <x-base.lucide
                                    class="h-4 w-4"
                                    icon="ChevronsLeft"
                                />
                            </x-base.pagination.link>
                            <x-base.pagination.link>
                                <x-base.lucide
                                    class="h-4 w-4"
                                    icon="ChevronLeft"
                                />
                            </x-base.pagination.link>
                            <x-base.pagination.link>...</x-base.pagination.link>
                            <x-base.pagination.link>1</x-base.pagination.link>
                            <x-base.pagination.link active>2</x-base.pagination.link>
                            <x-base.pagination.link>3</x-base.pagination.link>
                            <x-base.pagination.link>...</x-base.pagination.link>
                            <x-base.pagination.link>
                                <x-base.lucide
                                    class="h-4 w-4"
                                    icon="ChevronRight"
                                />
                            </x-base.pagination.link>
                            <x-base.pagination.link>
                                <x-base.lucide
                                    class="h-4 w-4"
                                    icon="ChevronsRight"
                                />
                            </x-base.pagination.link>
                        </x-base.pagination>
                        <x-base.form-select class="!box mt-3 w-20 sm:mt-0">
                            <option>10</option>
                            <option>25</option>
                            <option>35</option>
                            <option>50</option>
                        </x-base.form-select>
                    </div>
                </div> --}}
                <!-- END: Weekly Top Products -->
            </div>
        </div>
        <div class="col-span-12 2xl:col-span-3">
            <div class="-mb-10 pb-10 2xl:border-l">
                <div class="grid grid-cols-12 gap-x-6 gap-y-6 2xl:gap-x-0 2xl:pl-6">
                    <!-- BEGIN: Transactions -->
                    <div class="col-span-12 mt-3 md:col-span-6 xl:col-span-4 2xl:col-span-12 2xl:mt-8">
                        <div class="intro-x flex h-10 items-center">
                            <h2 class="mr-5 truncate text-lg font-medium">Recent cab bookings</h2>
                        </div>
                        <div class="mt-5">
                            @foreach ($cabBookings as $cabBooking )
                            <div class="intro-x">
                                <div class="box zoom-in mb-3 flex items-center px-5 py-3">
                                    <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                        <img
                                            src="{{ $cabBooking->client->supabase_image_url }}"
                                            alt="Midone - Tailwind Admin Dashboard Template"
                                        />
                                    </div>
                                    <div class="ml-4 mr-auto">
                                        <div class="font-medium">{{ $cabBooking->client->name }}</div>
                                        <div class="mt-0.5 text-xs text-slate-800">
                                            {{ convertDateToHumanFormat($cabBooking->trip_date)  }}
                                        </div>
                                    </div>
                                    <div class="text-success" >
                                        {{ $cabBooking->tripStatus->status }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                            <a
                                class="intro-x block w-full rounded-md border border-dotted border-slate-400 py-3 text-center text-slate-500 dark:border-darkmode-300"
                                href="{{ route('view_cab_trips') }}"
                            >
                                View More
                            </a>
                        </div>
                    </div>


                    <div class="col-span-12 mt-3 md:col-span-6 xl:col-span-4 2xl:col-span-12 2xl:mt-8">
                        <div class="intro-x flex h-10 items-center">
                            <h2 class="mr-5 truncate text-lg font-medium">Recent shuttle bookings</h2>
                        </div>
                        <div class="mt-5">
                            @foreach ($shuttleBookings as $shuttleBooking )
                            <div class="intro-x">
                                <div class="box zoom-in mb-3 flex items-center px-5 py-3">
                                    <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                        <img
                                            src="{{ $shuttleBooking->client->supabase_image_url }}"
                                            alt="Midone - Tailwind Admin Dashboard Template"
                                        />
                                    </div>
                                    <div class="ml-4 mr-auto">
                                        <div class="font-medium">{{ $shuttleBooking->client->name }}</div>
                                        <div class="mt-0.5 text-xs text-slate-800">
                                            {{ convertDateToHumanFormat($shuttleBooking->trip_date)  }}
                                        </div>
                                    </div>
                                    <div class="text-success" >
                                        {{ $shuttleBooking->tripStatus->status }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                            <a
                                class="intro-x block w-full rounded-md border border-dotted border-slate-400 py-3 text-center text-slate-500 dark:border-darkmode-300"
                                href="{{ route('view_shuttle_trips') }}"
                            >
                                View More
                            </a>
                        </div>
                    </div>
                    <!-- END: Transactions -->
                    <!-- BEGIN: Recent Activities -->
                    <div class="col-span-12 mt-3 md:col-span-6 xl:col-span-4 2xl:col-span-12">
                      
                        {{-- <div
                            class="relative mt-5 before:absolute before:ml-5 before:mt-5 before:block before:h-[85%] before:w-px before:bg-slate-200 before:dark:bg-darkmode-400">
                            <div class="intro-x relative mb-3 flex items-center">
                                <div
                                    class="before:absolute before:ml-5 before:mt-5 before:block before:h-px before:w-20 before:bg-slate-200 before:dark:bg-darkmode-400">
                                    <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                        <img
                                            src="{{ Vite::asset($fakers[9]['photos'][0]) }}"
                                            alt="Midone - Tailwind Admin Dashboard Template"
                                        />
                                    </div>
                                </div>
                                <div class="box zoom-in ml-4 flex-1 px-5 py-3">
                                    <div class="flex items-center">
                                        <div class="font-medium">
                                            {{ $fakers[9]['users'][0]['name'] }}
                                        </div>
                                        <div class="ml-auto text-xs text-slate-500">07:00 PM</div>
                                    </div>
                                    <div class="mt-1 text-slate-500">Has joined the team</div>
                                </div>
                            </div>
                            <div class="intro-x relative mb-3 flex items-center">
                                <div
                                    class="before:absolute before:ml-5 before:mt-5 before:block before:h-px before:w-20 before:bg-slate-200 before:dark:bg-darkmode-400">
                                    <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                        <img
                                            src="{{ Vite::asset($fakers[8]['photos'][0]) }}"
                                            alt="Midone - Tailwind Admin Dashboard Template"
                                        />
                                    </div>
                                </div>
                                <div class="box zoom-in ml-4 flex-1 px-5 py-3">
                                    <div class="flex items-center">
                                        <div class="font-medium">
                                            {{ $fakers[8]['users'][0]['name'] }}
                                        </div>
                                        <div class="ml-auto text-xs text-slate-500">07:00 PM</div>
                                    </div>
                                    <div class="text-slate-500">
                                        <div class="mt-1">Added 3 new photos</div>
                                        <div class="mt-2 flex">
                                            <x-base.tippy
                                                class="image-fit zoom-in mr-1 h-8 w-8"
                                                as="div"
                                                content="{{ $fakers[0]['products'][0]['name'] }}"
                                            >
                                                <img
                                                    class="rounded-md border border-white"
                                                    src="{{ Vite::asset($fakers[8]['photos'][0]) }}"
                                                    alt="Midone - Tailwind Admin Dashboard Template"
                                                />
                                            </x-base.tippy>
                                            <x-base.tippy
                                                class="image-fit zoom-in mr-1 h-8 w-8"
                                                as="div"
                                                content="{{ $fakers[1]['products'][0]['name'] }}"
                                            >
                                                <img
                                                    class="rounded-md border border-white"
                                                    src="{{ Vite::asset($fakers[8]['photos'][1]) }}"
                                                    alt="Midone - Tailwind Admin Dashboard Template"
                                                />
                                            </x-base.tippy>
                                            <x-base.tippy
                                                class="image-fit zoom-in mr-1 h-8 w-8"
                                                as="div"
                                                content="{{ $fakers[2]['products'][0]['name'] }}"
                                            >
                                                <img
                                                    class="rounded-md border border-white"
                                                    src="{{ Vite::asset($fakers[8]['photos'][2]) }}"
                                                    alt="Midone - Tailwind Admin Dashboard Template"
                                                />
                                            </x-base.tippy>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="intro-x my-4 text-center text-xs text-slate-500">
                                12 November
                            </div>
                            <div class="intro-x relative mb-3 flex items-center">
                                <div
                                    class="before:absolute before:ml-5 before:mt-5 before:block before:h-px before:w-20 before:bg-slate-200 before:dark:bg-darkmode-400">
                                    <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                        <img
                                            src="{{ Vite::asset($fakers[7]['photos'][0]) }}"
                                            alt="Midone - Tailwind Admin Dashboard Template"
                                        />
                                    </div>
                                </div>
                                <div class="box zoom-in ml-4 flex-1 px-5 py-3">
                                    <div class="flex items-center">
                                        <div class="font-medium">
                                            {{ $fakers[7]['users'][0]['name'] }}
                                        </div>
                                        <div class="ml-auto text-xs text-slate-500">07:00 PM</div>
                                    </div>
                                    <div class="mt-1 text-slate-500">
                                        Has changed
                                        <a
                                            class="text-primary"
                                            href=""
                                        >
                                            {{ $fakers[7]['products'][0]['name'] }}
                                        </a>
                                        price and description
                                    </div>
                                </div>
                            </div>
                            <div class="intro-x relative mb-3 flex items-center">
                                <div
                                    class="before:absolute before:ml-5 before:mt-5 before:block before:h-px before:w-20 before:bg-slate-200 before:dark:bg-darkmode-400">
                                    <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-full">
                                        <img
                                            src="{{ Vite::asset($fakers[6]['photos'][0]) }}"
                                            alt="Midone - Tailwind Admin Dashboard Template"
                                        />
                                    </div>
                                </div>
                                <div class="box zoom-in ml-4 flex-1 px-5 py-3">
                                    <div class="flex items-center">
                                        <div class="font-medium">
                                            {{ $fakers[6]['users'][0]['name'] }}
                                        </div>
                                        <div class="ml-auto text-xs text-slate-500">07:00 PM</div>
                                    </div>
                                    <div class="mt-1 text-slate-500">
                                        Has changed
                                        <a
                                            class="text-primary"
                                            href=""
                                        >
                                            {{ $fakers[6]['products'][0]['name'] }}
                                        </a>
                                        description
                                    </div>
                                </div>
                            </div>
                        </div> --}}
                    </div>
                    <!-- END: Recent Activities -->
                    {{-- <!-- BEGIN: Important Notes -->
                    <div
                        class="col-span-12 mt-3 md:col-span-6 xl:col-span-12 xl:col-start-1 xl:row-start-1 2xl:col-start-auto 2xl:row-start-auto">
                        <div class="intro-x flex h-10 items-center">
                            <h2 class="mr-auto truncate text-lg font-medium">
                                Important Notes
                            </h2>
                            <x-base.button
                                class="tiny-slider-navigator mr-2 border-slate-300 px-2 text-slate-600 dark:text-slate-300"
                                data-carousel="important-notes"
                                data-target="prev"
                            >
                                <x-base.lucide
                                    class="h-4 w-4"
                                    icon="ChevronLeft"
                                />
                            </x-base.button>
                            <x-base.button
                                class="tiny-slider-navigator mr-2 border-slate-300 px-2 text-slate-600 dark:text-slate-300"
                                data-carousel="important-notes"
                                data-target="next"
                            >
                                <x-base.lucide
                                    class="h-4 w-4"
                                    icon="ChevronRight"
                                />
                            </x-base.button>
                        </div>
                        <div class="intro-x mt-5">
                            <div class="box zoom-in">
                                <x-base.tiny-slider id="important-notes">
                                    <div class="p-5">
                                        <div class="truncate text-base font-medium">
                                            Lorem Ipsum is simply dummy text
                                        </div>
                                        <div class="mt-1 text-slate-400">20 Hours ago</div>
                                        <div class="mt-1 text-justify text-slate-500">
                                            Lorem Ipsum is simply dummy text of the printing and
                                            typesetting industry. Lorem Ipsum has been the industry's
                                            standard dummy text ever since the 1500s.
                                        </div>
                                        <div class="mt-5 flex font-medium">
                                            <x-base.button
                                                class="px-2 py-1"
                                                type="button"
                                                variant="secondary"
                                            >
                                                View Notes
                                            </x-base.button>
                                            <x-base.button
                                                class="ml-auto px-2 py-1"
                                                type="button"
                                                variant="outline-secondary"
                                            >
                                                Dismiss
                                            </x-base.button>
                                        </div>
                                    </div>
                                    <div class="p-5">
                                        <div class="truncate text-base font-medium">
                                            Lorem Ipsum is simply dummy text
                                        </div>
                                        <div class="mt-1 text-slate-400">20 Hours ago</div>
                                        <div class="mt-1 text-justify text-slate-500">
                                            Lorem Ipsum is simply dummy text of the printing and
                                            typesetting industry. Lorem Ipsum has been the industry's
                                            standard dummy text ever since the 1500s.
                                        </div>
                                        <div class="mt-5 flex font-medium">
                                            <x-base.button
                                                class="px-2 py-1"
                                                type="button"
                                                variant="secondary"
                                            >
                                                View Notes
                                            </x-base.button>
                                            <x-base.button
                                                class="ml-auto px-2 py-1"
                                                type="button"
                                                variant="outline-secondary"
                                            >
                                                Dismiss
                                            </x-base.button>
                                        </div>
                                    </div>
                                    <div class="p-5">
                                        <div class="truncate text-base font-medium">
                                            Lorem Ipsum is simply dummy text
                                        </div>
                                        <div class="mt-1 text-slate-400">20 Hours ago</div>
                                        <div class="mt-1 text-justify text-slate-500">
                                            Lorem Ipsum is simply dummy text of the printing and
                                            typesetting industry. Lorem Ipsum has been the industry's
                                            standard dummy text ever since the 1500s.
                                        </div>
                                        <div class="mt-5 flex font-medium">
                                            <x-base.button
                                                class="px-2 py-1"
                                                type="button"
                                                variant="secondary"
                                            >
                                                View Notes
                                            </x-base.button>
                                            <x-base.button
                                                class="ml-auto px-2 py-1"
                                                type="button"
                                                variant="outline-secondary"
                                            >
                                                Dismiss
                                            </x-base.button>
                                        </div>
                                    </div>
                                </x-base.tiny-slider>
                            </div>
                        </div>
                    </div>
                    <!-- END: Important Notes --> --}}
                    <!-- BEGIN: Schedules -->
                    {{-- <div
                        class="col-span-12 mt-3 md:col-span-6 xl:col-span-4 xl:col-start-1 xl:row-start-2 2xl:col-span-12 2xl:col-start-auto 2xl:row-start-auto">
                        <div class="intro-x flex h-10 items-center">
                            <h2 class="mr-5 truncate text-lg font-medium">Schedules</h2>
                            <a
                                class="ml-auto flex items-center truncate text-primary"
                                href=""
                            >
                                <x-base.lucide
                                    class="mr-1 h-4 w-4"
                                    icon="Plus"
                                /> Add New Schedules
                            </a>
                        </div>
                        <div class="mt-5">
                            <div class="intro-x box">
                                <div class="p-5">
                                    <div class="flex">
                                        <x-base.lucide
                                            class="h-5 w-5 text-slate-500"
                                            icon="ChevronLeft"
                                        />
                                        <div class="mx-auto text-base font-medium">April</div>
                                        <x-base.lucide
                                            class="h-5 w-5 text-slate-500"
                                            icon="ChevronRight"
                                        />
                                    </div>
                                    <div class="mt-5 grid grid-cols-7 gap-4 text-center">
                                        <div class="font-medium">Su</div>
                                        <div class="font-medium">Mo</div>
                                        <div class="font-medium">Tu</div>
                                        <div class="font-medium">We</div>
                                        <div class="font-medium">Th</div>
                                        <div class="font-medium">Fr</div>
                                        <div class="font-medium">Sa</div>
                                        <div class="relative rounded py-0.5 text-slate-500">29</div>
                                        <div class="relative rounded py-0.5 text-slate-500">30</div>
                                        <div class="relative rounded py-0.5 text-slate-500">31</div>
                                        <div class="relative rounded py-0.5">1</div>
                                        <div class="relative rounded py-0.5">2</div>
                                        <div class="relative rounded py-0.5">3</div>
                                        <div class="relative rounded py-0.5">4</div>
                                        <div class="relative rounded py-0.5">5</div>
                                        <div class="relative rounded bg-success/20 py-0.5 dark:bg-success/30">
                                            6
                                        </div>
                                        <div class="relative rounded py-0.5">7</div>
                                        <div class="relative rounded bg-primary py-0.5 text-white">
                                            8
                                        </div>
                                        <div class="relative rounded py-0.5">9</div>
                                        <div class="relative rounded py-0.5">10</div>
                                        <div class="relative rounded py-0.5">11</div>
                                        <div class="relative rounded py-0.5">12</div>
                                        <div class="relative rounded py-0.5">13</div>
                                        <div class="relative rounded py-0.5">14</div>
                                        <div class="relative rounded py-0.5">15</div>
                                        <div class="relative rounded py-0.5">16</div>
                                        <div class="relative rounded py-0.5">17</div>
                                        <div class="relative rounded py-0.5">18</div>
                                        <div class="relative rounded py-0.5">19</div>
                                        <div class="relative rounded py-0.5">20</div>
                                        <div class="relative rounded py-0.5">21</div>
                                        <div class="relative rounded py-0.5">22</div>
                                        <div class="relative rounded bg-pending/20 py-0.5 dark:bg-pending/30">
                                            23
                                        </div>
                                        <div class="relative rounded py-0.5">24</div>
                                        <div class="relative rounded py-0.5">25</div>
                                        <div class="relative rounded py-0.5">26</div>
                                        <div class="relative rounded bg-primary/10 py-0.5 dark:bg-primary/50">
                                            27
                                        </div>
                                        <div class="relative rounded py-0.5">28</div>
                                        <div class="relative rounded py-0.5">29</div>
                                        <div class="relative rounded py-0.5">30</div>
                                        <div class="relative rounded py-0.5 text-slate-500">1</div>
                                        <div class="relative rounded py-0.5 text-slate-500">2</div>
                                        <div class="relative rounded py-0.5 text-slate-500">3</div>
                                        <div class="relative rounded py-0.5 text-slate-500">4</div>
                                        <div class="relative rounded py-0.5 text-slate-500">5</div>
                                        <div class="relative rounded py-0.5 text-slate-500">6</div>
                                        <div class="relative rounded py-0.5 text-slate-500">7</div>
                                        <div class="relative rounded py-0.5 text-slate-500">8</div>
                                        <div class="relative rounded py-0.5 text-slate-500">9</div>
                                    </div>
                                </div>
                                <div class="border-t border-slate-200/60 p-5">
                                    <div class="flex items-center">
                                        <div class="mr-3 h-2 w-2 rounded-full bg-pending"></div>
                                        <span class="truncate">UI/UX Workshop</span>
                                        <span class="font-medium xl:ml-auto">23th</span>
                                    </div>
                                    <div class="mt-4 flex items-center">
                                        <div class="mr-3 h-2 w-2 rounded-full bg-primary"></div>
                                        <span class="truncate"> VueJs Frontend Development </span>
                                        <span class="font-medium xl:ml-auto">10th</span>
                                    </div>
                                    <div class="mt-4 flex items-center">
                                        <div class="mr-3 h-2 w-2 rounded-full bg-warning"></div>
                                        <span class="truncate">Laravel Rest API</span>
                                        <span class="font-medium xl:ml-auto">31th</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> --}}
                    <!-- END: Schedules -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDgPaOiKNa1w_JLpABm2E14_M4Q-HtcBdY&libraries=places"></script>
    <script>
        document.addEventListener('livewire:initialized', function () {
            // Initialize the map
            const mapManager = new MapMarkersManager('map');
    
            // Add markers from cab bookings
            const cabBookingsData = @json($cabMapData);
            
            mapManager.addBookingMarkers(cabBookingsData);
            
            // Fit map to show all markers
            mapManager.fitBoundsToMarkers();
                   
        });


class MapMarkersManager {
    constructor(mapElementId, defaultCenter = { lat: -17.819319426529013, lng: 31.049096122808336 }, defaultZoom = 12) {
        this.markers = [];
        this.infoWindows = [];
        this.activeInfoWindow = null;
        
        // Initialize map
        this.map = new google.maps.Map(document.getElementById(mapElementId), {
            zoom: defaultZoom,
            center: defaultCenter,
            styles: [
                {
                    featureType: "poi",
                    elementType: "labels",
                    stylers: [{ visibility: "off" }]
                }
            ]
        });
    }

    // Create info window content with enhanced styling
    createInfoWindowContent(booking) {
        return `
            <div class="p-3 min-w-[300px]">
                <div class="flex items-center mb-3">
                    <img src="${booking.client?.supabase_image_url || '/path/to/default-avatar.png'}" 
                         class="w-10 h-10 rounded-full mr-3" 
                         alt="${booking.client?.name || 'Client'}"
                    />
                    <div>
                        <h3 class="font-medium text-base">${booking.client?.name || 'N/A'}</h3>
                        <p class="text-xs text-gray-500">${this.formatDate(booking.trip_date)}</p>
                    </div>
                </div>
                <div class="text-sm">
                    <p class="mb-2"><strong>Status:</strong> 
                        <span class="px-2 py-1 rounded-full text-xs ${
                            booking.trip_status?.status === 'completed' ? 'bg-success/20 text-success' :
                            booking.trip_status?.status === 'Pending' ? 'bg-pending/20 text-pending' :
                            'bg-primary/20 text-primary'
                        }">
                            ${booking.trip_status?.status || 'N/A'}
                        </span>
                    </p>
                    <div class="bg-gray-50 rounded-lg p-2 mb-2">
                        <p class="mb-1 flex text-xs items-center">
                            <svg class="w-4 h-4 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="3"></circle>
                                <path d="M12 21C15.5 17.5 19 14.5 19 11a7 7 0 1 0-14 0c0 3.5 3.5 6.5 7 10"></path>
                            </svg>
                            <span>${booking.pickup_address || 'N/A'}</span>
                        </p>
                        <p class="flex text-xs items-center mt-4">
                            <svg class="w-4 h-4 mr-2 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path d="M9 20l-5.447-2.724A1 1 0 0 1 3 16.382V5.618a1 1 0 0 1 .553-.894L9 2m0 18V2m0 18l5.447-2.724A1 1 0 0 0 15 16.382V5.618a1 1 0 0 0-.553-.894L9 2"></path>
                            </svg>
                            <span>${booking.destination_address || 'N/A'}</span>
                        </p>
                         <p class="flex text-xs font-medium items-center mt-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" fill="none" stroke="#278228" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock"><circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/></svg>
                            <span class="ml-4">Pickup Date: ${booking.trip_date || 'N/A'}</span>
                        </p>
                    </div>
                </div>
            </div>
        `;
    }

    // Format date helper
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // Add a single marker with custom image and info window
    addMarker(position, booking) {
        // Create custom marker icon
        const customIcon = {
            url: booking.client?.supabase_image_url || '/path/to/default-marker.png',
            scaledSize: new google.maps.Size(30, 30),
            origin: new google.maps.Point(0, 0),
            anchor: new google.maps.Point(18, 18),
            // Create circular mask for the image
            className: 'map-marker-image',
            labelOrigin: new google.maps.Point(20, 38),
            
        };

        // Create marker with custom icon
        const marker = new google.maps.Marker({
            position: position,
            map: this.map,
            animation: google.maps.Animation.DROP,
            icon: customIcon,
            title: `${booking.client?.name || 'Booking'} #${booking.id}`,
            // Add status indicator dot
            label: {
                text: '●',
                color: this.getStatusColor(booking.trip_status?.status),
                fontSize: '28px'
            }
        });

        // Create info window with enhanced styling
        const infoWindow = new google.maps.InfoWindow({
            content: this.createInfoWindowContent(booking),
            maxWidth: 320,
            pixelOffset: new google.maps.Size(0, -20)
        });

        // Add click listener with enhanced interaction
        marker.addListener('click', () => {
            // Close currently open info window
            if (this.activeInfoWindow) {
                this.activeInfoWindow.close();
            }
            
            // Open new info window
            infoWindow.open(this.map, marker);
            this.activeInfoWindow = infoWindow;

            // Animate marker
            marker.setAnimation(google.maps.Animation.BOUNCE);
            setTimeout(() => marker.setAnimation(null), 750);

            // Pan map to center on clicked marker
            this.map.panTo(marker.getPosition());
        });

        // Add hover effect
        marker.addListener('mouseover', () => {
            marker.setZIndex(google.maps.Marker.MAX_ZINDEX + 1);
            marker.setAnimation(google.maps.Animation.BOUNCE);
        });

        marker.addListener('mouseout', () => {
            marker.setAnimation(null);
        });

        // Store references
        this.markers.push(marker);
        this.infoWindows.push(infoWindow);

        return marker;
    }

    // Get color based on booking status
    getStatusColor(status) {
        switch (status?.toLowerCase()) {
            case 'completed':
                return '#10B981'; // success
            case 'pending':
                return '#F59E0B'; // pending
            case 'cancelled':
                return '#EF4444'; // danger
            default:
                return '#3B82F6'; // primary
        }
    }

    // Add multiple markers from booking data
    addBookingMarkers(bookings) {
        bookings.forEach(booking => {
            if (booking.pick_up_latitude && booking.pick_up_longitude) {
                this.addMarker(
                    {
                        lat: parseFloat(booking.pick_up_latitude),
                        lng: parseFloat(booking.pick_up_longitude)
                    },
                    booking
                );
            }
        });
    }

    // Clear all markers and info windows
    clearMarkers() {
        this.markers.forEach(marker => marker.setMap(null));
        this.infoWindows.forEach(infoWindow => infoWindow.close());
        this.markers = [];
        this.infoWindows = [];
        this.activeInfoWindow = null;
    }

    // Fit map bounds to show all markers
    fitBoundsToMarkers() {
        if (this.markers.length > 0) {
            const bounds = new google.maps.LatLngBounds();
            this.markers.forEach(marker => bounds.extend(marker.getPosition()));
            this.map.fitBounds(bounds);
            
            // If only one marker, zoom out a bit
            if (this.markers.length === 1) {
                this.map.setZoom(15);
            }
        }

    }
}

//  class MapMarkersManager {
//     constructor(mapElementId, defaultCenter = { lat: -17.819319426529013, lng: 31.049096122808336 }, defaultZoom = 12) {
//         this.markers = [];
//         this.infoWindows = [];
//         this.activeInfoWindow = null;
        
//         // Initialize map
//         this.map = new google.maps.Map(document.getElementById(mapElementId), {
//             zoom: defaultZoom,
//             center: defaultCenter,
//             styles: [
//                 {
//                     featureType: "poi",
//                     elementType: "labels",
//                     stylers: [{ visibility: "off" }]
//                 }
//             ]
//         });
//     }

//     // Create info window content
//     createInfoWindowContent(booking) {
//         return `
//             <div class="p-3 min-w-[200px]">
//                 <h3 class="font-medium text-base mb-2">Booking Details</h3>
//                 <div class="text-sm">
//                      <p class="mb-1"><strong>Pick-up:</strong> ${booking.pickup_address || 'N/A'}</p>
//                     <p class="mb-1"><strong>Client:</strong> ${booking.client?.name || 'N/A'}</p>
//                     <p class="mb-1"><strong>Date:</strong> ${this.formatDate(booking.trip_date)}</p>
//                     <p class="mb-1"><strong>Status:</strong> 
//                         <span class="px-2 py-1 rounded-full text-xs ${
//                             booking.trip_status?.status === 'completed' ? 'bg-success/20 text-success' :
//                             booking.trip_status?.status === 'Pending' ? 'bg-pending/20 text-pending' :
//                             'bg-primary/20 text-primary'
//                         }">
//                             ${booking.trip_status?.status || 'N/A'}
//                         </span>
//                     </p>
//                     <p class="mb-1"><strong>Pick-up:</strong> ${booking.pickup_address || 'N/A'}</p>
//                     <p><strong>Drop-off:</strong> ${booking.destination_address || 'N/A'}</p>
//                 </div>
//             </div>
//         `;
//     }

//     // Format date helper
//     formatDate(dateString) {
//         if (!dateString) return 'N/A';
//         return new Date(dateString).toLocaleDateString('en-US', {
//             year: 'numeric',
//             month: 'short',
//             day: 'numeric',
//             hour: '2-digit',
//             minute: '2-digit'
//         });
//     }

//     // Add a single marker with info window
//     addMarker(position, booking) {
//         // Create marker
//         const marker = new google.maps.Marker({
//             position: position,
//             map: this.map,
//             animation: google.maps.Animation.DROP,
//             title: `Booking #${booking.id}`,
//             icon: {
//                 path: google.maps.SymbolPath.CIRCLE,
//                 scale: 8,
//                 fillColor: this.getStatusColor(booking.trip_status?.status),
//                 fillOpacity: 1,
//                 strokeWeight: 2,
//                 strokeColor: '#ffffff'
//             }
//         });

//         // Create info window
//         const infoWindow = new google.maps.InfoWindow({
//             content: this.createInfoWindowContent(booking),
//             maxWidth: 300
//         });

//         // Add click listener
//         marker.addListener('click', () => {
//             // Close currently open info window
//             if (this.activeInfoWindow) {
//                 this.activeInfoWindow.close();
//             }
            
//             // Open new info window
//             infoWindow.open(this.map, marker);
//             this.activeInfoWindow = infoWindow;

//             // Animate marker
//             marker.setAnimation(google.maps.Animation.BOUNCE);
//             setTimeout(() => marker.setAnimation(null), 750);
//         });

//         // Store references
//         this.markers.push(marker);
//         this.infoWindows.push(infoWindow);

//         return marker;
//     }

//     // Get color based on booking status
//     getStatusColor(status) {
//         switch (status?.toUpperCase()) {
//             case 'Completed':
//                 return '#10B981'; // success
//             case 'Pending':
//                 return '#F59E0B'; // pending
//             case 'Cancelled':
//                 return '#EF4444'; // danger
//             default:
//                 return '#3B82F6'; // primary
//         }
//     }

//     // Add multiple markers from booking data
//     addBookingMarkers(bookings) {
//         bookings.forEach(booking => {
            
//             if (booking.pick_up_latitude && booking.pick_up_longitude) {
//                 this.addMarker(
//                     {
//                         lat: parseFloat(booking.pick_up_latitude),
//                         lng: parseFloat(booking.pick_up_longitude)
//                     },
//                     booking
//                 );
//             }
//         });
//     }

//     // Clear all markers and info windows
//     clearMarkers() {
//         this.markers.forEach(marker => marker.setMap(null));
//         this.infoWindows.forEach(infoWindow => infoWindow.close());
//         this.markers = [];
//         this.infoWindows = [];
//         this.activeInfoWindow = null;
//     }

//     // Fit map bounds to show all markers
//     fitBoundsToMarkers() {
//         if (this.markers.length > 0) {
//             const bounds = new google.maps.LatLngBounds();
//             this.markers.forEach(marker => bounds.extend(marker.getPosition()));
//             this.map.fitBounds(bounds);
            
//             // If only one marker, zoom out a bit
//             if (this.markers.length === 1) {
//                 this.map.setZoom(15);
//             }
//         }
//     }
// }
    </script>
</div>
