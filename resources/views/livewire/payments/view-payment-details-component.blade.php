<div>
    <div class="intro-y mt-8 flex items-center">
        <h2 class="mr-auto text-lg font-medium">Payment Details</h2>
    </div>
    
    <div class="grid grid-cols-12 gap-6">
        <!-- BEGIN: Profile Menu -->
        <div class="col-span-12 flex flex-col-reverse lg:col-span-4 lg:block 2xl:col-span-3">
            <div class="intro-y box mt-5">
                <div class="relative flex items-center p-5">
                    <div class="image-fit h-20 w-20">
                        <img
                            class="rounded-full"
                            src="{{ $payment->client->supabase_image_url }}"
                            alt="Client Profile Image"
                        />
                    </div>
                    <div class="ml-4 mr-auto">
                        <div class="text-base font-medium">
                            {{ $payment->client->name }}
                        </div>
                        <div class="text-slate-500">Client</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END: Profile Menu -->
        
        <div class="col-span-12 lg:col-span-8 2xl:col-span-9">
            <!-- BEGIN: Payment Information -->
            <div class="intro-y box mt-5">
                <div class="flex items-center border-b border-slate-200/60 p-5 dark:border-darkmode-400">
                    <h2 class="mr-auto text-base font-medium">
                        Payment Information
                    </h2>
                </div>
                <div class="p-5">
                    <div class="flex flex-col gap-4">
                        <div class="flex items-center">
                            <x-base.lucide
                                class="mr-2 h-4 w-4 text-slate-500"
                                icon="CreditCard"
                            />
                            <div class="truncate text-slate-500">Reference Number:</div>
                            <div class="ml-auto font-medium">{{ $payment->payment_method_reference }}</div>
                        </div>
                        <div class="flex items-center">
                            <x-base.lucide
                                class="mr-2 h-4 w-4 text-slate-500"
                                icon="Calendar"
                            />
                            <div class="truncate text-slate-500">Payment Date:</div>
                            <div class="ml-auto font-medium">{{ convertIsoDateFormat($payment->updated_at) }}</div>
                        </div>
                        <div class="flex items-center">
                            <x-base.lucide
                                class="mr-2 h-4 w-4 text-slate-500"
                                icon="Wallet"
                            />
                            <div class="truncate text-slate-500">Payment Type:</div>
                            <div class="ml-auto font-medium">{{ $payment->reference }}</div>
                        </div>
                        <div class="flex items-center">
                            <x-base.lucide
                                class="mr-2 h-4 w-4 text-slate-500"
                                icon="Route"
                            />
                            <div class="truncate text-slate-500">Trip Distance:</div>
                            <div class="ml-auto font-medium">
                                @if($payment->trip)
                                    {{ $payment->trip->trip_total_distance }} Km
                                @else
                                   --
                                @endif
                            </div>
                        </div>
                        <div class="flex items-center">
                            <x-base.lucide
                                class="mr-2 h-4 w-4 text-slate-500"
                                icon="CheckCircle"
                            />
                            <div class="truncate text-slate-500">Status:</div>
                            <div class="ml-auto">
                                
                                    {{ $payment->status }}
                              
                            </div>
                        </div>
                        <div class="flex items-center">
                            <x-base.lucide
                                class="mr-2 h-4 w-4 text-slate-500"
                                icon="DollarSign"
                            />
                            <div class="truncate text-slate-500">Amount Paid:</div>
                            <div class="ml-auto font-medium">${{ $payment->amount_paid }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END: Payment Information -->
        </div>
    </div>
</div>