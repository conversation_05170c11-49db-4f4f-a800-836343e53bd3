<div>
    <div class="grid grid-cols-12 gap-6">
        <div class="col-span-12 2xl:col-span-9">
            <div class="grid grid-cols-12 gap-6">
                <!-- BEGIN: General Report -->
              
                
                <!-- BEGIN: General Report -->
                
                <!-- END: General Report -->
                <!-- BEGIN: Weekly Top Products -->
                <div class="col-span-12 mt-6">
                    <div class="intro-y block h-10 items-center sm:flex">
                        <h2 class="mr-5 truncate text-lg font-medium">
                            System users
                        </h2>
                        {{-- <div class="mt-3 flex items-center sm:ml-auto sm:mt-0">
                            <x-base.button class="!box flex items-center text-slate-600 dark:text-slate-300">
                                <x-base.lucide
                                    class="mr-2 hidden h-4 w-4 sm:block"
                                    icon="FileText"
                                />
                                Export to Excel
                            </x-base.button>
                            <x-base.button class="!box ml-3 flex items-center text-slate-600 dark:text-slate-300">
                                <x-base.lucide
                                    class="mr-2 hidden h-4 w-4 sm:block"
                                    icon="FileText"
                                />
                                Export to PDF
                            </x-base.button>
                        </div> --}}
                    </div>
                    <div class="intro-y col-span-12 mt-2 flex flex-wrap items-center sm:flex-nowrap">
                        {{-- <x-base.button
                            class="mr-2 shadow-md"
                            variant="primary"
                        >
                          Add New User
                        </x-base.button> --}}
                        {{-- <x-base.menu>
                            <x-base.menu.button
                                class="!box px-2"
                                as="x-base.button"
                            >
                                <span class="flex h-5 w-5 items-center justify-center">
                                    <x-base.lucide
                                        class="h-4 w-4"
                                        icon="Plus"
                                    />
                                </span>
                            </x-base.menu.button>
                            <x-base.menu.items class="w-40">
                                <x-base.menu.item>
                                    <x-base.lucide
                                        class="mr-2 h-4 w-4"
                                        icon="Printer"
                                    /> Print
                                </x-base.menu.item>
                                <x-base.menu.item>
                                    <x-base.lucide
                                        class="mr-2 h-4 w-4"
                                        icon="FileText"
                                    /> Export to
                                    Excel
                                </x-base.menu.item>
                                <x-base.menu.item>
                                    <x-base.lucide
                                        class="mr-2 h-4 w-4"
                                        icon="FileText"
                                    /> Export to
                                    PDF
                                </x-base.menu.item>
                            </x-base.menu.items>
                        </x-base.menu> --}}
                        {{-- <div class="mx-auto hidden text-slate-500 md:block">
                           .
                        </div> --}}
                        <div class="mt-3 w-full sm:ml-auto sm:mt-0 sm:w-auto md:ml-0">
                            <div class="relative w-56 text-slate-500">
                                <x-base.form-input
                                    wire:model.live.debounce.300ms="search"
                                    class="!box w-56 pr-10"
                                    type="text"
                                    placeholder="Search..."
                                />
                                <x-base.lucide
                                    class="absolute inset-y-0 right-0 my-auto mr-3 h-4 w-4"
                                    icon="Search"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="intro-y mt-8 overflow-auto sm:mt-0 lg:overflow-visible">
                        <x-base.table class="border-separate border-spacing-y-[10px] sm:mt-2">
                            <x-base.table.thead>
                                <x-base.table.tr>
                                    <x-base.table.th class="whitespace-nowrap border-b-0">
                                        
                                    </x-base.table.th>
                                    <x-base.table.th class="whitespace-nowrap border-b-0">
                                        USER
                                    </x-base.table.th>
                                   
                                    {{-- <x-base.table.th class="whitespace-nowrap border-b-0 text-center">
                                        STATUS
                                    </x-base.table.th> --}}
                                    <x-base.table.th class="whitespace-nowrap border-b-0 text-center">
                                        ACTIONS
                                    </x-base.table.th>
                                </x-base.table.tr>
                            </x-base.table.thead>
                            <x-base.table.tbody>
                                @foreach ($users as $user)
                                    <x-base.table.tr class="intro-x">
                                        <x-base.table.td
                                            class="box w-40 rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                        >
                                            <div class="flex">
                                                <div class="image-fit zoom-in h-10 w-10">
                                                    <x-base.tippy
                                                        class="rounded-full shadow-[0px_0px_0px_2px_#fff,_1px_1px_5px_rgba(0,0,0,0.32)] dark:shadow-[0px_0px_0px_2px_#3f4865,_1px_1px_5px_rgba(0,0,0,0.32)]"
                                                        src="{{ asset('storage/security.png') }}"
                                                        alt="Midone - Tailwind Admin Dashboard Template"
                                                        as="img"  
                                                    />
                                                </div>
                                            </div>
                                        </x-base.table.td>
                                        <x-base.table.td
                                            class="box rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                        >
                                            <a
                                                class="whitespace-nowrap font-medium"
                                                href=""
                                            >
                                                {{ $user->name }}
                                            </a>
                                            <div class="mt-0.5 whitespace-nowrap text-xs text-slate-500">
                                              User
                                            </div>
                                        </x-base.table.td>
                                       
                                        {{-- <x-base.table.td
                                            class="box w-40 rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                        >
                                            <div class="flex items-center justify-center text-success">
                                                <x-base.lucide
                                                    class="mr-2 h-4 w-4"
                                                    icon="CheckSquare"
                                                />
                                              Active
                                            </div>
                                        </x-base.table.td> --}}
                                        <x-base.table.td @class([
                                            'box w-56 rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600',
                                            'before:absolute before:inset-y-0 before:left-0 before:my-auto before:block before:h-8 before:w-px before:bg-slate-200 before:dark:bg-darkmode-400',
                                        ])>
                                            <div class="flex items-center justify-center">
                                                <button
                                    onclick="confirm('Are you sure you want to delete the user profile of  {{ $user->name }} ?') ? '' : event.stopImmediatePropagation() "
                                    wire:click="delete({{ $user->id }})" 
                                        type="button"
                                        class="flex items-center text-danger"
                                        
                                    >
                                    
                                        <div class="mr-1 h-4 w-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>
                                        </div>
                                        
                                        Delete
                                    </button>
                                            </div>
                                        </x-base.table.td>
                                    </x-base.table.tr>
                                @endforeach
                            </x-base.table.tbody>
                        </x-base.table>

                        <div class="intro-y col-span-12 flex gap-4 justify-start  sm:flex-row sm:flex-nowrap">
                            {{ $users->links('vendor.livewire.custom-pagination') }}  
                        </div>
                        <div {{ session('user_deleted') ? '' : 'hidden' }}>
                            <x-base.alert
                            class="flex justify-between mb-2"
                            variant="primary"
                            hidden
                        >
                
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                        {{ session('user_deleted')  }}
                                   
                        </x-base.alert>
                        </div>
                    </div>
                    
                </div>
                <!-- END: Weekly Top Products -->
            </div>
        </div>
        <div class="col-span-12 2xl:col-span-3">
            <div class="-mb-10 pb-10 2xl:border-l">
                <div class="grid grid-cols-12 gap-x-6 gap-y-6 2xl:gap-x-0 2xl:pl-6">
                    <!-- BEGIN: Transactions -->
                    <div class="col-span-12 mt-3 md:col-span-6 xl:col-span-4 2xl:col-span-12 2xl:mt-8">
                        <div class="mt-5">
                            
                            <!-- BEGIN: Vertical Form -->
            <x-base.preview-component class="intro-y box">
                <div
                    class="flex flex-col items-center border-b border-slate-200/60 p-5 dark:border-darkmode-400 sm:flex-row">
                    <h2 class="mr-auto text-base font-medium">
                        Create User
                    </h2>
                </div>
                <div class="p-5">
                    <x-base.preview>
                        <div>
                            <x-impala-components.form.primary-text-input
                            wire:model="name"
                            name="name"
                            label="name"
                            placeholder="User fullname"  />
                        </div>
                        
                        <div class="mt-4">
                            <x-impala-components.form.primary-text-input
                            wire:model="email"
                            name="email"
                            label="Email"
                            placeholder="User email"  />
                        </div>
                        
                        <div class="mt-4">
                            <x-impala-components.form.primary-text-input
                                type="password"
                                wire:model="password"
                                name="password"
                                label="Password"
                                placeholder="Password"  />
                        </div>
    
                        <div class="mt-4">
                            <x-impala-components.form.primary-text-input
                                type="password"
                                wire:model="password_confirmation"
                                name="password_confirmation"
                                label="Confirm password"
                                placeholder="Confirm password"  />
                        </div>
                    </div>
                        <x-base.button
                            wire:click="register"
                            class="mt-5 w-auto"
                            variant="primary"
                        >
                            Create User
                        </x-base.button>
                    </x-base.preview>
                    
                </div>
                <div class="mt-4" {{ session('success') ? '' : 'hidden' }}>
                    <x-base.alert
                    class="flex justify-between mb-2"
                    variant="primary"
                    hidden
                >
        
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                {{ session('success')  }}
                           
                </x-base.alert>
            </x-base.preview-component>
                        </div>
                    </div>
                    <!-- END: Transactions -->
                </div>
            </div>
        </div>
    </div>
    <!-- BEGIN: Delete Confirmation Modal -->
    <x-base.dialog id="delete-confirmation-modal">
        <x-base.dialog.panel>
            <div class="p-5 text-center">
                <x-base.lucide
                    class="mx-auto mt-3 h-16 w-16 text-danger"
                    icon="XCircle"
                />
                <div class="mt-5 text-3xl">Are you sure?</div>
                <div class="mt-2 text-slate-500">
                    Do you really want to delete this user? <br />
                    This process cannot be undone.
                </div>
            </div>
            <div class="px-5 pb-8 text-center">
                <x-base.button
                    class="mr-1 w-24"
                    data-tw-dismiss="modal"
                    type="button"
                    variant="outline-secondary"
                >
                    Cancel
                </x-base.button>
                <x-base.button
                    wire:click="delete"
                    class="w-24"
                    type="button"
                    variant="danger"
                    {{-- data-tw-dismiss="modal" --}}
                >
                    Delete
                </x-base.button>
            </div>
        </x-base.dialog.panel>
    </x-base.dialog>
</div>
