<div>

    <x-base.notification.index>
            fdsjfhdsjkfhdskjfhdksjf
    </x-base.notification.index>


    <div class="flex items-center mt-8 intro-y">
        <h2 class="mr-auto text-lg font-medium">Trip details</h2>
    </div>
    <div wire:poll.5s class="grid grid-cols-12 gap-6">
        <!-- BEGIN: Profile Menu -->
        <div class="flex flex-col-reverse col-span-12 lg:col-span-4 lg:block 2xl:col-span-3">
            <div class="mt-5 intro-y box">
                <div class="relative flex items-center p-5">
                    <div class="w-20 h-20 image-fit">
                        <img
                            class="rounded-full"
                            src="{{ $book->client->supabase_image_url }}"
                            alt="Midone - Tailwind Admin Dashboard Template"
                        />
                    </div>
                    <div class="ml-4 mr-auto">
                        <div class="text-base font-medium">
                            {{ $book->client->name }}
                        </div>
                        <div class="text-slate-500">{{ 'Phone: +' . $book->client->phonenumber }}</div>
                    </div>
                </div>
                <div class="p-5 border-t border-slate-200/60 dark:border-darkmode-400">
                    <a
                        class="flex items-center font-medium text-primary"
                        href=""
                    >
                        <x-base.lucide
                            class="w-4 h-4 mr-2"
                            icon="Activity"
                        /> Client
                        Information
                    </a>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="receipt"
                            /> Hire type
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            {{  $book->hireDriverRate->hireDriverType->hire_type }}
                        </a>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="route"
                            /> Cost
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            {{'$'. $book->hireDriverRate->amount . ' USD' }}
                        </a>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="calendar-days"
                            /> Hire date
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                             {{ convertDateToHumanFormat($book->date_of_hire)  }}
                        </a>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="calendar-days"
                            /> Required drivers licence
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                             {{ $book->licence->licence_class  }}
                        </a>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="clock"
                            /> Hire Status
                        </a>
                        </div>
                        <div>
                            <a
                            class="flex items-center mt-5"
                            href=""
                        >
                             {{-- {{ $book->hireDriverStatus->status}} --}}
                             {{ $this->assignementStatus($book->hireDriverStatus->status) }}
                        </a>
                        </div>
                    </div>
                    <div class="{{ $supabaseDriver !== null ? '' : 'hidden' }} mt-5 border-t border-slate-200/60 dark:border-darkmode-400">
                        <a
                        class="flex items-center mt-4 font-medium text-primary"
                        href=""
                    >
                        <x-base.lucide
                            class="w-4 h-4 mr-2"
                            icon="Activity"
                        /> Driver
                        Information
                    </a>

                        <div class="relative flex items-center p-5">
                            <div class="w-20 h-20 image-fit">
                                <img
                                    class="rounded-full"
                                    @if($supabaseDriver !== null || $assignedDriverId !== null)
                                         src="{{ $supabaseDriver->profile_photo_file ? asset('storage/'.$supabaseDriver->profile_photo_file) : asset('driver.png') }}"
                                    @else
                                         src="{{ asset('driver.png') }}"
                                    @endif

                                    alt="Driver Profile"
                                />
                            </div>
                            <div class="ml-4 mr-auto">
                                <div class="text-base font-medium">
                                    @if($supabaseDriver)
                                        {{ trim($supabaseDriver->driver_firstname . ' ' . $supabaseDriver->driver_lastname) }}
                                    @else
                                        N/A
                                    @endif
                                </div>
                                <div class="text-slate-500">
                                    Phone: +{{ $supabaseDriver->driver_mobile ?? 'N/A' }}
                                </div>
                                <div class="text-slate-500">
                                    Drivers Licence: {{ $supabaseDriver->driversLicence->licence_class ?? 'N/A' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- <div class="p-5 border-t border-slate-200/60 dark:border-darkmode-400">
                    @livewire('cab-bookings.search-driver-to_assign-component')
                        </div>
                        <div class="flex p-5 border-t border-slate-200/60 dark:border-darkmode-400">

                        <div {{ $driver_id != 0 ? '' : 'hidden' }}>
                            <x-base.button
                            wire:click="assignDriver"
                            class="px-2 py-1"
                            type="button"
                            variant="primary"
                        >
                            Assign Driver
                        </x-base.button>
                        </div>

                            <x-base.button
                                class="px-2 py-1 ml-auto"
                                type="button"
                                variant="outline-secondary"
                            >
                                Close Trip
                            </x-base.button>
                        </div>
                <div {{ session('cab_trip_assigned') ? '' : 'hidden' }} class="w-full pt-4">
                    <x-base.alert
                    class="flex justify-between mb-2"
                    variant="primary"
                    hidden
                > --}}


        </div>


    </div>
        <!-- END: Profile Menu -->
        <div class="col-span-12 lg:col-span-8 2xl:col-span-9">
            <!-- BEGIN: Display Information -->
            <div class="intro-y box lg:mt-5">
                <div class="flex items-center p-5 border-b border-slate-200/60 dark:border-darkmode-400">
                    <h2 class="mr-auto text-base font-medium">
                        Available Drivers
                    </h2>
                    <div class="w-full mt-3 sm:ml-auto sm:mt-0 sm:w-auto md:ml-0">
                        <div class="relative w-56 text-slate-500">
                            <x-base.form-input
                                {{-- wire:model.live.debounce.300ms="search" --}}
                                class="!box w-56 pr-10"
                                type="text"
                                placeholder="Search..."
                            />
                            <x-base.lucide
                                class="absolute inset-y-0 right-0 w-4 h-4 my-auto mr-3"
                                icon="Search"
                            />
                        </div>
                    </div>
                </div>
               <div class="p-8 mt-4">
                <x-base.table class="-mt-2 border-separate border-spacing-y-[10px]">
                    <x-base.table.thead>
                        <x-base.table.tr>
                            <x-base.table.th class="border-b-0 whitespace-nowrap">
                            </x-base.table.th>
                            <x-base.table.th class="border-b-0 whitespace-nowrap">
                                DRIVER NAME
                            </x-base.table.th>
                            <x-base.table.th class="text-center border-b-0 whitespace-nowrap">
                                PHONENUMBER
                            </x-base.table.th>
                            <x-base.table.th class="text-center border-b-0 whitespace-nowrap">
                                AVAILABILiTY
                            </x-base.table.th>
                            <x-base.table.th class="text-center border-b-0 whitespace-nowrap">
                                ACTIONS
                            </x-base.table.th>
                        </x-base.table.tr>
                    </x-base.table.thead>
                    <x-base.table.tbody>
                        @foreach ($drivers as $driver)
                            <x-base.table.tr class="intro-x" wire:key="{{ $driver->id}}">
                                <x-base.table.td
                                    class="box w-40 rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                >
                                    <div class="flex">
                                        <div class="w-10 h-10 image-fit zoom-in">
                                            <x-base.tippy
                                                class="rounded-full shadow-[0px_0px_0px_2px_#fff,_1px_1px_5px_rgba(0,0,0,0.32)] dark:shadow-[0px_0px_0px_2px_#3f4865,_1px_1px_5px_rgba(0,0,0,0.32)]"
                                                src="{{ $driver->profile_photo_file ?  asset('storage/'.$driver->profile_photo_file) : asset('driver.png') }}"
                                                alt="Impala"
                                                as="img"
                                                content=""

                                            />
                                        </div>
                                    </div>
                                </x-base.table.td>
                                <x-base.table.td
                                    class="box rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                >
                                    <a
                                        class="font-medium whitespace-nowrap"
                                        href=""
                                    >
                                    {{ $driver->driver_firstname . ' ' . $driver->driver_lastname }}
                                    </a>
                                    <div class="mt-0.5 whitespace-nowrap text-xs text-slate-500">
                                        {{$driver->driversLicence->licence_class }}
                                    </div>
                                </x-base.table.td>
                                <x-base.table.td
                                    class="box rounded-l-none rounded-r-none border-x-0 text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                                >
                                {{ $driver->driver_mobile }}
                                </x-base.table.td>
                                <x-base.table.td
                                class="box rounded-l-none rounded-r-none border-x-0 text-center shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600"
                            >
                           Available
                            </x-base.table.td>

                                <x-base.table.td @class([
                                    'box w-56 rounded-l-none rounded-r-none border-x-0 shadow-[5px_3px_5px_#00000005] first:rounded-l-[0.6rem] first:border-l last:rounded-r-[0.6rem] last:border-r dark:bg-darkmode-600',
                                    'before:absolute before:inset-y-0 before:left-0 before:my-auto before:block before:h-8 before:w-px before:bg-slate-200 before:dark:bg-darkmode-400',
                                ])>
                                    <div class="flex items-center justify-center">
                                        <button wire:click="assignDriver('{{ $driver->id }}', '{{ $book->id }}')" type="button"
                                        class="flex items-center mr-3 text-success"
                                        {{-- href="{{ route('view-trip', ['trip' => $trip->id]) }}"  --}}>
                                    <div class="w-4 h-4 mr-1">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-scan-eye"><path d="M3 7V5a2 2 0 0 1 2-2h2"/><path d="M17 3h2a2 2 0 0 1 2 2v2"/><path d="M21 17v2a2 2 0 0 1-2 2h-2"/><path d="M7 21H5a2 2 0 0 1-2-2v-2"/><circle cx="12" cy="12" r="1"/><path d="M18.944 12.33a1 1 0 0 0 0-.66 7.5 7.5 0 0 0-13.888 0 1 1 0 0 0 0 .66 7.5 7.5 0 0 0 13.888 0"/></svg>
                                    </div>
                                        Assign
                                </button>
                                    </div>
                                </x-base.table.td>
                            </x-base.table.tr>
                        @endforeach
                    </x-base.table.tbody>
                </x-base.table>
               </div>
               <div class="flex justify-start col-span-12 gap-4 intro-y sm:flex-row sm:flex-nowrap">
                {{ $drivers->links('vendor.livewire.custom-pagination') }}
        </div>


            </div>

        </div>

    </div>

    {{-- <div id="success-notification-content" class="hidden">
        <div class="flex py-5 pl-5 bg-white border rounded-lg shadow-xl pr-14 border-slate-200/60 dark:bg-darkmode-600 dark:text-slate-300 dark:border-darkmode-600">
            <i data-tw-merge data-lucide="check-circle" class="stroke-1.5 w-5 h-5 text-success"></i>
            <div class="ml-4 mr-4">
                <div class="font-medium">Success!</div>
                <div class="mt-1 text-slate-500">
                    Driver has been successfully assigned.
                </div>
            </div>
        </div>
    </div> --}}

    <div id="success-notification-content" class="flex hidden py-5 pl-5 bg-white border rounded-lg shadow-xl pr-14 border-slate-200/60 dark:bg-darkmode-600 dark:text-slate-300 dark:border-darkmode-600">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#47a117" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big"><path d="M21.801 10A10 10 0 1 1 17 3.335"/><path d="m9 11 3 3L22 4"/></svg>
        <div class="ml-4 mr-4">
            <div class="font-medium">Driver assigned</div>
            <div class="mt-1 text-slate-500">
                The booking was successfully assigned to the driver
            </div>
        </div>
    </div>
</div>

<script>
     document.addEventListener('livewire:initialized', function () {
        console.log('fhdjskhfkjdshfkjdshfjkds');
        @this.on('showNotification', () => {
                Toastify({
                    node: $("#success-notification-content")
                        .clone()
                        .removeClass("hidden")[0],
                    duration: -1,
                    newWindow: true,
                    close: true,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "white",
                    stopOnFocus: true,
                }).showToast();
            });
     });
</script>
