@props(['message', 'icon' => 'alert-triangle'])
<div role="alert" class="relative flex items-center px-5 py-4 mb-2 text-white border rounded-md alert bg-primary border-primary dark:border-primary"><i data-tw-merge data-lucide="{{  $icon }}" class="stroke-1.5 w-5 h-5 mr-2 h-6 w-6 mr-2 h-6 w-6"></i>
   {{  $message }}
    <button data-tw-merge data-tw-dismiss="alert" type="button" aria-label="Close" type="button" aria-label="Close" class="absolute right-0 px-3 py-2 my-auto mr-2 text-white text-slate-800"><i data-tw-merge data-lucide="x" class="stroke-1.5 w-5 h-5 h-4 w-4 h-4 w-4"></i></button>
</div>
