@props(['name', 'placeholder', 'label'])
@aware([
    'error',
])
<div class="mt-3">
    <label data-tw-merge for="{{ $name }}" class="inline-block mb-2 group-[.form-inline]:mb-2
    group-[.form-inline]:sm:mb-0 group-[.form-inline]:sm:mr-5 group-[.form-inline]:sm:text-right">
      {{ $label ?? '' }}
    </label>
    <input  {{ $attributes }} data-tw-merge id="{{ $name }}" type="text" placeholder="{{ $placeholder ?? '' }}"
           class="disabled:bg-slate-100 disabled:cursor-not-allowed
            dark:disabled:bg-darkmode-800/50 dark:disabled:border-transparent
              [&amp;[readonly]]:bg-slate-100 [&amp;[readonly]]:cursor-not-allowed [&amp;[readonly]]:dark:bg-darkmode-800/50 [&amp;[readonly]]:dark:border-transparent transition duration-200 ease-in-out w-full text-sm border-slate-200 shadow-sm rounded-md placeholder:text-slate-400/90 focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus:border-primary focus:border-opacity-40 dark:bg-darkmode-800 dark:border-transparent dark:focus:ring-slate-700 dark:focus:ring-opacity-50 dark:placeholder:text-slate-500/80 group-[.form-inline]:flex-1 group-[.input-group]:rounded-none group-[.input-group]:[&amp;:not(:first-child)]:border-l-transparent group-[.input-group]:first:rounded-l group-[.input-group]:last:rounded-r group-[.input-group]:z-10
               {{ $errors->has($name) ? 'border-danger border-danger' : ''  }}  " />
    <x-impala-components.form.primary-text-input.error name="{{ $name }}" />
</div>
