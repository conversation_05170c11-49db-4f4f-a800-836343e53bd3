@props(['name', 'label', 'value' => '', 'checked' => false])
<div data-tw-merge class="flex items-center mt-2 mt-2"><input  {{ $attributes }} data-tw-merge type="checkbox" class="transition-all
duration-100 ease-in-out shadow-sm border-slate-200 cursor-pointer rounded focus:ring-4 focus:ring-offset-0
focus:ring-primary focus:ring-opacity-20 dark:bg-darkmode-800 dark:border-transparent dark:focus:ring-slate-700
dark:focus:ring-opacity-50 [&amp;[type=&#039;radio&#039;]]:checked:bg-primary [&amp;[type=&#039;radio&#039;]]:checked:border-primary [&amp;[type=&#039;radio&#039;]]:checked:border-opacity-10 [&amp;[type=&#039;checkbox&#039;]]:checked:bg-primary [&amp;[type=&#039;checkbox&#039;]]:checked:border-primary [&amp;[type=&#039;checkbox&#039;]]:checked:border-opacity-10 [&amp;:disabled:not(:checked)]:bg-slate-100 [&amp;:disabled:not(:checked)]:cursor-not-allowed [&amp;:disabled:not(:checked)]:dark:bg-darkmode-800/50 [&amp;:disabled:checked]:opacity-70 [&amp;:disabled:checked]:cursor-not-allowed [&amp;:disabled:checked]:dark:bg-darkmode-800/50"
                                                              id="{{ $name ?? '' }}" value=" {{ $value ?? '' }}" {{ $checked ? 'checked' : '' }}  />
    <label data-tw-merge for="checkbox-switch-1" class="cursor-pointer ml-2">
       <span class="text-base">{{ $label }}</span>
    </label>
</div>
