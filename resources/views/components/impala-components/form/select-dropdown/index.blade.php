@props(['formSelectSize' => null, 'name', 'options', 'selected', 'placeholder', 'label'])

<div class="mt-3">
    <label data-tw-merge for="{{ $name }}" class="inline-block mb-2 group-[.form-inline]:mb-2
    group-[.form-inline]:sm:mb-0 group-[.form-inline]:sm:mr-5 group-[.form-inline]:sm:text-right">
        {{ $label ?? '' }}
    </label>
<select name="{{ $name }}"
    data-tw-merge
    {{ $attributes->class(
            merge([
                'disabled:bg-slate-100 disabled:cursor-not-allowed disabled:dark:bg-darkmode-800/50',
                '[&[readonly]]:bg-slate-100 [&[readonly]]:cursor-not-allowed [&[readonly]]:dark:bg-darkmode-800/50',
                'transition duration-200 ease-in-out w-full text-sm border-slate-200 shadow-sm rounded-md py-2 px-3 pr-8 focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus:border-primary focus:border-opacity-40 dark:bg-darkmode-800 dark:border-transparent dark:focus:ring-slate-700 dark:focus:ring-opacity-50',
                'group-[.form-inline]:flex-1',
                $formSelectSize == 'sm' ? 'text-xs py-1.5 pl-2 pr-8' : null,
                $formSelectSize == 'lg' ? 'text-lg py-1.5 pl-4 pr-8' : null,
                $attributes->whereStartsWith('class')->first(),
            ]),
        )->merge($attributes->whereDoesntStartWith('class')->getAttributes()) }} >
    <option selected>{{ $placeholder ?? '' }}</option>
    @foreach($options as $key => $value)
        <option value="{{ $key }}" {{ $selected == $key ? 'selected' : '' }}>{{ $value }}</option>
    @endforeach
</select>
<x-impala-components.form.primary-text-input.error name="{{ $name }}" />
</div>


{{--<label for="countries" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">--}}
{{--    {{ $placeholder }}--}}
{{--</label>--}}
{{--<select {{ $attributes->merge(['class' => 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg--}}
{{--         focus:ring-green-500 focus:border-green-500 block w-full p-2.5'])}} name="$name">--}}
{{--    <option selected>{{ $placeholder }}</option>--}}
{{--    @foreach($options as $key => $value)--}}
{{--        <option value="{{ $key }}" {{ $selected == $key ? 'selected' : '' }}>{{ $value }}</option>--}}
{{--    @endforeach--}}
{{--</select>--}}
{{--<x-form.error name="{{ $name }}" />--}}
