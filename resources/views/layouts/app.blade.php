<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ config('app.name') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link rel="stylesheet" href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Livewire Styles -->
    @livewireStyles

    <style>
        [x-cloak] { 
            display: none !important; 
        }
    </style>
</head>

<body class="font-sans antialiased">
    <div class="flex h-screen bg-gray-100 dark:bg-darkmode-600">
        <!-- BEGIN: Side Menu -->
        @include('layouts.sidebar')
        <!-- END: Side Menu -->

        <!-- BEGIN: Content -->
        <div class="flex flex-col flex-1 overflow-hidden">
            <!-- BEGIN: Top Bar -->
            @include('layouts.topbar')
            <!-- END: Top Bar -->

            <!-- BEGIN: Main Content -->
            <div class="flex flex-col flex-1 overflow-hidden">
                <!-- BEGIN: Breadcrumb -->
                <div class="intro-y flex items-center h-10 breadcrumb">
                    <a href="" class="text-gray-700 dark:text-gray-300">Dashboard</a>
                    <i data-lucide="chevron-right" class="text-xs h-4 w-4 mx-2"></i>
                    <a href="" class="text-gray-700 dark:text-gray-300">Apps</a>
                    <i data-lucide="chevron-right" class="text-xs h-4 w-4 mx-2"></i>
                    <a href="" class="text-gray-700 dark:text-gray-300">Email</a>
                </div>
                <!-- END: Breadcrumb -->

                <!-- BEGIN: Content -->
                <div class="flex flex-col flex-1 p-5">
                    {{ $slot }}
                </div>
                <!-- END: Content -->
            </div>
            <!-- END: Main Content -->
        </div>
        <!-- END: Content -->
    </div>

    <!-- Scripts -->
    @vite(['resources/js/vendors/filepond.js'])
    @livewireScripts

    <script>
        document.addEventListener('livewire:load', function () {
            Livewire.hook('message.processed', (message, component) => {
                // Code to run after each Livewire component update
                // For example, re-initialize any JavaScript plugins or libraries
                console.log('Livewire component updated:', component);
            });
        });
    </script>
</body>

</html>