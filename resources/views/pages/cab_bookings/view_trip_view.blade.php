@extends('../themes/' . $activeTheme . '/' . $activeLayout)
<link href="https://cdn.jsdelivr.net/npm/flowbite@2.4.1/dist/flowbite.min.css" rel="stylesheet" />
<style>
    /* Style for circular marker image with a border */
    .custom-marker {
        width: 50px;
        height: 50px;
        border-radius: 50%; /* Makes the image circular */
        overflow: hidden;
        border: 3px solid black; /* Border around the image */
        background-size: cover;
        background-position: center;
    }
</style>
@section('subhead')
    <title>View Trip</title>
@endsection
@filepondScripts
@section('subcontent')
   @livewire('cab-bookings.view_trip_component', ['trip' => $tripInfor])
@endsection

