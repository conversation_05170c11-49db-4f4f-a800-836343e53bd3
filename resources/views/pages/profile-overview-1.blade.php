@extends('../themes/' . $activeTheme . '/' . $activeLayout)

@section('subhead')
    <title>Profile - Midone - Tailwind Admin Dashboard Template</title>
@endsection

@section('subcontent')
    <div class="flex items-center mt-8 intro-y">
        <h2 class="mr-auto text-lg font-medium">Profile Layout</h2>
    </div>
    <x-base.tab.group>
        <!-- BEGIN: Profile Info -->
        <div class="px-5 pt-5 mt-5 intro-y box">
            <div class="flex flex-col pb-5 -mx-5 border-b border-slate-200/60 dark:border-darkmode-400 lg:flex-row">
                <div class="flex items-center justify-center flex-1 px-5 lg:justify-start">
                    <div class="relative flex-none w-20 h-20 image-fit sm:h-24 sm:w-24 lg:h-32 lg:w-32">
                        <img
                            class="rounded-full"
                            src="{{ Vite::asset($fakers[0]['photos'][0]) }}"
                            alt="Midone - Tailwind Admin Dashboard Template"
                        />

                        <div
                            class="absolute bottom-0 right-0 flex items-center justify-center p-2 mb-1 mr-1 rounded-full bg-primary">
                            <x-base.lucide
                                class="w-4 h-4 text-white"
                                icon="Camera"
                            />
                        </div>
                    </div>
                    <div class="ml-5">
                        <div class="w-24 text-lg font-medium truncate sm:w-40 sm:whitespace-normal">
                            {{ $fakers[0]['users'][0]['name'] }}
                        </div>
                        <div class="text-slate-500">{{ $fakers[0]['jobs'][0] }}</div>
                    </div>
                </div>
                <div
                    class="flex-1 px-5 pt-5 mt-6 border-t border-l border-r border-slate-200/60 dark:border-darkmode-400 lg:mt-0 lg:border-t-0 lg:pt-0">
                    <div class="font-medium text-center lg:mt-3 lg:text-left">
                        Contact Details
                    </div>
                    <div class="flex flex-col items-center justify-center mt-4 lg:items-start">
                        <div class="flex items-center truncate sm:whitespace-normal">
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="Mail"
                            />
                            {{ $fakers[0]['users'][0]['email'] }}
                        </div>
                        <div class="flex items-center mt-3 truncate sm:whitespace-normal">
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="Instagram"
                            /> Instagram
                            {{ $fakers[0]['users'][0]['name'] }}
                        </div>
                        <div class="flex items-center mt-3 truncate sm:whitespace-normal">
                            <x-base.lucide
                                class="w-4 h-4 mr-2"
                                icon="Twitter"
                            /> Twitter
                            {{ $fakers[0]['users'][0]['name'] }}
                        </div>
                    </div>
                </div>
                <div
                    class="flex-1 px-5 pt-5 mt-6 border-t border-slate-200/60 dark:border-darkmode-400 lg:mt-0 lg:border-0 lg:pt-0">
                    <div class="font-medium text-center lg:mt-5 lg:text-left">
                        Sales Growth
                    </div>
                    <div class="flex items-center justify-center mt-2 lg:justify-start">
                        <div class="flex w-20 mr-2">
                            USP:
                            <span class="ml-3 font-medium text-success">+23%</span>
                        </div>
                        <div class="w-3/4">
                            <x-simple-line-chart-1
                                class="-mr-5"
                                height="h-[55px]"
                            />
                        </div>
                    </div>
                    <div class="flex items-center justify-center lg:justify-start">
                        <div class="flex w-20 mr-2">
                            STP: <span class="ml-3 font-medium text-danger">-2%</span>
                        </div>
                        <div class="w-3/4">
                            <x-simple-line-chart-2
                                class="-mr-5"
                                height="h-[55px]"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <x-base.tab.list
                class="flex-col justify-center text-center sm:flex-row lg:justify-start"
                variant="link-tabs"
            >
                <x-base.tab
                    id="dashboard-tab"
                    :fullWidth="false"
                    selected
                >
                    <x-base.tab.button class="py-4 cursor-pointer">Dashboard</x-base.tab.button>
                </x-base.tab>
                <x-base.tab
                    id="account-and-profile-tab"
                    :fullWidth="false"
                >
                    <x-base.tab.button class="py-4 cursor-pointer">
                        Account & Profile
                    </x-base.tab.button>
                </x-base.tab>
                <x-base.tab
                    id="activities-tab"
                    :fullWidth="false"
                >
                    <x-base.tab.button class="py-4 cursor-pointer">
                        Activities
                    </x-base.tab.button>
                </x-base.tab>
                <x-base.tab
                    id="tasks-tab"
                    :fullWidth="false"
                >
                    <x-base.tab.button class="py-4 cursor-pointer">Tasks</x-base.tab.button>
                </x-base.tab>
            </x-base.tab.list>
        </div>
        <!-- END: Profile Info -->
        <x-base.tab.panels class="mt-5 intro-y">
            <x-base.tab.panel
                id="dashboard"
                selected
            >
                <div class="grid grid-cols-12 gap-6">
                    <!-- BEGIN: Top Categories -->
                    <div class="col-span-12 intro-y box lg:col-span-6">
                        <div class="flex items-center p-5 border-b border-slate-200/60 dark:border-darkmode-400">
                            <h2 class="mr-auto text-base font-medium">
                                Top Categories
                            </h2>
                            <x-base.menu class="ml-auto">
                                <x-base.menu.button
                                    class="block w-5 h-5"
                                    href="#"
                                    tag="a"
                                >
                                    <x-base.lucide
                                        class="w-5 h-5 text-slate-500"
                                        icon="MoreHorizontal"
                                    />
                                </x-base.menu.button>
                                <x-base.menu.items class="w-40">
                                    <x-base.menu.item>
                                        <x-base.lucide
                                            class="w-4 h-4 mr-2"
                                            icon="Plus"
                                        /> Add
                                        Category
                                    </x-base.menu.item>
                                    <x-base.menu.item>
                                        <x-base.lucide
                                            class="w-4 h-4 mr-2"
                                            icon="Settings"
                                        />
                                        Settings
                                    </x-base.menu.item>
                                </x-base.menu.items>
                            </x-base.menu>
                        </div>
                        <div class="p-5">
                            <div class="flex flex-col sm:flex-row">
                                <div class="mr-auto">
                                    <a
                                        class="font-medium"
                                        href=""
                                    >
                                        Wordpress Template
                                    </a>
                                    <div class="mt-1 text-slate-500">
                                        HTML, PHP, Mysql
                                    </div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 mt-5 mr-auto -ml-2 sm:ml-0 sm:mr-5">
                                        <x-simple-line-chart height="h-[30px]" />
                                    </div>
                                    <div class="text-center">
                                        <div class="font-medium">6.5k</div>
                                        <div class="mt-1.5 rounded bg-success/20 px-2 text-success">
                                            +150
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col mt-5 sm:flex-row">
                                <div class="mr-auto">
                                    <a
                                        class="font-medium"
                                        href=""
                                    >
                                        Bootstrap HTML Template
                                    </a>
                                    <div class="mt-1 text-slate-500">
                                        HTML, PHP, Mysql
                                    </div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 mt-5 mr-auto -ml-2 sm:ml-0 sm:mr-5">
                                        <x-simple-line-chart height="h-[30px]" />
                                    </div>
                                    <div class="text-center">
                                        <div class="font-medium">2.5k</div>
                                        <div class="mt-1.5 rounded bg-pending/10 px-2 text-pending">
                                            +150
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col mt-5 sm:flex-row">
                                <div class="mr-auto">
                                    <a
                                        class="font-medium"
                                        href=""
                                    >
                                        Tailwind HTML Template
                                    </a>
                                    <div class="mt-1 text-slate-500">
                                        HTML, PHP, Mysql
                                    </div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 mt-5 mr-auto -ml-2 sm:ml-0 sm:mr-5">
                                        <x-simple-line-chart height="h-[30px]" />
                                    </div>
                                    <div class="text-center">
                                        <div class="font-medium">3.4k</div>
                                        <div class="mt-1.5 rounded bg-primary/10 px-2 text-primary">
                                            +150
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: Top Categories -->
                    <!-- BEGIN: Work In Progress -->
                    <x-base.tab.group class="col-span-12 intro-y box lg:col-span-6">
                        <div
                            class="flex items-center px-5 py-5 border-b border-slate-200/60 dark:border-darkmode-400 sm:py-0">
                            <h2 class="mr-auto text-base font-medium">
                                Work In Progress
                            </h2>
                            <x-base.menu class="ml-auto sm:hidden">
                                <x-base.menu.button
                                    class="block w-5 h-5"
                                    href="#"
                                    tag="a"
                                >
                                    <x-base.lucide
                                        class="w-5 h-5 text-slate-500"
                                        icon="MoreHorizontal"
                                    />
                                </x-base.menu.button>
                                <x-base.menu.items class="w-40">
                                    <x-base.menu.item
                                        class="w-full"
                                        id="work-in-progress-mobile-new-tab"
                                        target="work-in-progress-new"
                                        as="x-base.tab.button"
                                        unstyled
                                        selected
                                    >
                                        New
                                    </x-base.menu.item>
                                    <x-base.menu.item
                                        class="w-full"
                                        id="work-in-progress-mobile-last-week-tab"
                                        target="work-in-progress-last-week"
                                        as="x-base.tab.button"
                                        unstyled
                                        :selected="false"
                                    >
                                        Last Week
                                    </x-base.menu.item>
                                </x-base.menu.items>
                            </x-base.menu>
                            <x-base.tab.list
                                class="hidden w-auto ml-auto sm:flex"
                                variant="link-tabs"
                            >
                                <x-base.tab
                                    id="work-in-progress-new-tab"
                                    :fullWidth="false"
                                    selected
                                >
                                    <x-base.tab.button class="py-5 cursor-pointer">
                                        New
                                    </x-base.tab.button>
                                </x-base.tab>
                                <x-base.tab
                                    id="work-in-progress-last-week-tab"
                                    :fullWidth="false"
                                    :selected="false"
                                >
                                    <x-base.tab.button class="py-5 cursor-pointer">
                                        Last Week
                                    </x-base.tab.button>
                                </x-base.tab>
                            </x-base.tab.list>
                        </div>
                        <div class="p-5">
                            <x-base.tab.panels>
                                <x-base.tab.panel
                                    id="work-in-progress-new"
                                    selected
                                >
                                    <div>
                                        <div class="flex">
                                            <div class="mr-auto">Pending Tasks</div>
                                            <div>20%</div>
                                        </div>
                                        <x-base.progress class="h-1 mt-2">
                                            <x-base.progress.bar
                                                class="w-1/2 bg-primary"
                                                role="progressbar"
                                                aria-valuenow="0"
                                                aria-valuemin="0"
                                                aria-valuemax="100"
                                            ></x-base.progress.bar>
                                        </x-base.progress>
                                    </div>
                                    <div class="mt-5">
                                        <div class="flex">
                                            <div class="mr-auto">Completed Tasks</div>
                                            <div>2 / 20</div>
                                        </div>
                                        <x-base.progress class="h-1 mt-2">
                                            <x-base.progress.bar
                                                class="w-1/4 bg-primary"
                                                role="progressbar"
                                                aria-valuenow="0"
                                                aria-valuemin="0"
                                                aria-valuemax="100"
                                            ></x-base.progress.bar>
                                        </x-base.progress>
                                    </div>
                                    <div class="mt-5">
                                        <div class="flex">
                                            <div class="mr-auto">Tasks In Progress</div>
                                            <div>42</div>
                                        </div>
                                        <x-base.progress class="h-1 mt-2">
                                            <x-base.progress.bar
                                                class="w-3/4 bg-primary"
                                                role="progressbar"
                                                aria-valuenow="0"
                                                aria-valuemin="0"
                                                aria-valuemax="100"
                                            ></x-base.progress.bar>
                                        </x-base.progress>
                                    </div>
                                    <x-base.button
                                        class="block w-40 mx-auto mt-5"
                                        href=""
                                        as="a"
                                        variant="secondary"
                                    >
                                        View More Details
                                    </x-base.button>
                                </x-base.tab.panel>
                            </x-base.tab.panels>
                        </div>
                    </x-base.tab.group>
                    <!-- END: Work In Progress -->
                    <!-- BEGIN: Daily Sales -->
                    <div class="col-span-12 intro-y box lg:col-span-6">
                        <div
                            class="flex items-center px-5 py-5 border-b border-slate-200/60 dark:border-darkmode-400 sm:py-3">
                            <h2 class="mr-auto text-base font-medium">Daily Sales</h2>
                            <x-base.menu class="ml-auto sm:hidden">
                                <x-base.menu.button
                                    class="block w-5 h-5"
                                    href="#"
                                    tag="a"
                                >
                                    <x-base.lucide
                                        class="w-5 h-5 text-slate-500"
                                        icon="MoreHorizontal"
                                    />
                                </x-base.menu.button>
                                <x-base.menu.items class="w-40">
                                    <x-base.menu.item>
                                        <x-base.lucide
                                            class="w-4 h-4 mr-2"
                                            icon="File"
                                        /> Download
                                        Excel
                                    </x-base.menu.item>
                                </x-base.menu.items>
                            </x-base.menu>
                            <x-base.button
                                class="hidden sm:flex"
                                variant="outline-secondary"
                            >
                                <x-base.lucide
                                    class="w-4 h-4 mr-2"
                                    icon="File"
                                /> Download
                                Excel
                            </x-base.button>
                        </div>
                        <div class="p-5">
                            <div class="relative flex items-center">
                                <div class="flex-none w-12 h-12 image-fit">
                                    <img
                                        class="rounded-full"
                                        src="{{ Vite::asset($fakers[0]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                                <div class="ml-4 mr-auto">
                                    <a
                                        class="font-medium"
                                        href=""
                                    >
                                        {{ $fakers[0]['users'][0]['name'] }}
                                    </a>
                                    <div class="mr-5 text-slate-500 sm:mr-5">
                                        Bootstrap 4 HTML Admin Template
                                    </div>
                                </div>
                                <div class="font-medium text-slate-600 dark:text-slate-500">
                                    +$19
                                </div>
                            </div>
                            <div class="relative flex items-center mt-5">
                                <div class="flex-none w-12 h-12 image-fit">
                                    <img
                                        class="rounded-full"
                                        src="{{ Vite::asset($fakers[1]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                                <div class="ml-4 mr-auto">
                                    <a
                                        class="font-medium"
                                        href=""
                                    >
                                        {{ $fakers[1]['users'][0]['name'] }}
                                    </a>
                                    <div class="mr-5 text-slate-500 sm:mr-5">
                                        Tailwind Admin Dashboard Template
                                    </div>
                                </div>
                                <div class="font-medium text-slate-600 dark:text-slate-500">
                                    +$25
                                </div>
                            </div>
                            <div class="relative flex items-center mt-5">
                                <div class="flex-none w-12 h-12 image-fit">
                                    <img
                                        class="rounded-full"
                                        src="{{ Vite::asset($fakers[2]['photos'][0]) }}"
                                        alt="Midone - Tailwind Admin Dashboard Template"
                                    />
                                </div>
                                <div class="ml-4 mr-auto">
                                    <a
                                        class="font-medium"
                                        href=""
                                    >
                                        {{ $fakers[2]['users'][0]['name'] }}
                                    </a>
                                    <div class="mr-5 text-slate-500 sm:mr-5">
                                        Vuejs HTML Admin Template
                                    </div>
                                </div>
                                <div class="font-medium text-slate-600 dark:text-slate-500">
                                    +$21
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: Daily Sales -->
                    <!-- BEGIN: Latest Tasks -->
                    <x-base.tab.group class="col-span-12 intro-y box lg:col-span-6">
                        <div
                            class="flex items-center px-5 py-5 border-b border-slate-200/60 dark:border-darkmode-400 sm:py-0">
                            <h2 class="mr-auto text-base font-medium">
                                Latest Tasks
                            </h2>
                            <x-base.menu class="ml-auto sm:hidden">
                                <x-base.menu.button
                                    class="block w-5 h-5"
                                    href="#"
                                    tag="a"
                                >
                                    <x-base.lucide
                                        class="w-5 h-5 text-slate-500"
                                        icon="MoreHorizontal"
                                    />
                                </x-base.menu.button>
                                <x-base.menu.items class="w-40">
                                    <x-base.menu.item
                                        class="w-full"
                                        id="latest-tasks-mobile-new-tab"
                                        target="latest-tasks-new"
                                        as="x-base.tab.button"
                                        unstyled
                                        selected
                                    >
                                        New
                                    </x-base.menu.item>
                                    <x-base.menu.item
                                        class="w-full"
                                        id="latest-tasks-mobile-last-week-tab"
                                        target="latest-tasks-last-week"
                                        as="x-base.tab.button"
                                        unstyled
                                        :selected="false"
                                    >
                                        Last Week
                                    </x-base.menu.item>
                                </x-base.menu.items>
                            </x-base.menu>
                            <x-base.tab.list
                                class="hidden w-auto ml-auto sm:flex"
                                variant="link-tabs"
                            >
                                <x-base.tab
                                    id="latest-tasks-new-tab"
                                    :fullWidth="false"
                                    selected
                                >
                                    <x-base.tab.button class="py-5 cursor-pointer">
                                        New
                                    </x-base.tab.button>
                                </x-base.tab>
                                <x-base.tab
                                    id="latest-tasks-last-week-tab"
                                    :fullWidth="false"
                                    :selected="false"
                                >
                                    <x-base.tab.button class="py-5 cursor-pointer">
                                        Last Week
                                    </x-base.tab.button>
                                </x-base.tab>
                            </x-base.tab.list>
                        </div>
                        <div class="p-5">
                            <x-base.tab.panels>
                                <x-base.tab.panel
                                    id="latest-tasks-new"
                                    selected
                                >
                                    <div class="flex items-center">
                                        <div class="pl-4 border-l-2 border-primary dark:border-primary">
                                            <a
                                                class="font-medium"
                                                href=""
                                            >
                                                Create New Campaign
                                            </a>
                                            <div class="text-slate-500">10:00 AM</div>
                                        </div>
                                        <x-base.form-switch class="ml-auto">
                                            <x-base.form-switch.input type="checkbox" />
                                        </x-base.form-switch>
                                    </div>
                                    <div class="flex items-center mt-5">
                                        <div class="pl-4 border-l-2 border-primary dark:border-primary">
                                            <a
                                                class="font-medium"
                                                href=""
                                            >
                                                Meeting With Client
                                            </a>
                                            <div class="text-slate-500">02:00 PM</div>
                                        </div>
                                        <x-base.form-switch class="ml-auto">
                                            <x-base.form-switch.input type="checkbox" />
                                        </x-base.form-switch>
                                    </div>
                                    <div class="flex items-center mt-5">
                                        <div class="pl-4 border-l-2 border-primary dark:border-primary">
                                            <a
                                                class="font-medium"
                                                href=""
                                            >
                                                Create New Repository
                                            </a>
                                            <div class="text-slate-500">04:00 PM</div>
                                        </div>
                                        <x-base.form-switch class="ml-auto">
                                            <x-base.form-switch.input type="checkbox" />
                                        </x-base.form-switch>
                                    </div>
                                </x-base.tab.panel>
                            </x-base.tab.panels>
                        </div>
                    </x-base.tab.group>
                    <!-- END: Latest Tasks -->
                    <!-- BEGIN: General Statistic -->
                    <div class="col-span-12 intro-y box">
                        <div
                            class="flex items-center px-5 py-5 border-b border-slate-200/60 dark:border-darkmode-400 sm:py-3">
                            <h2 class="mr-auto text-base font-medium">
                                General Statistics
                            </h2>
                            <x-base.menu class="ml-auto sm:hidden">
                                <x-base.menu.button
                                    class="block w-5 h-5"
                                    href="#"
                                >
                                    <x-base.lucide
                                        class="w-5 h-5 text-slate-500"
                                        icon="MoreHorizontal"
                                    />
                                </x-base.menu.button>
                                <x-base.menu.items class="w-40">
                                    <x-base.menu.item>
                                        <x-base.lucide
                                            class="w-4 h-4 mr-2"
                                            icon="File"
                                        /> Download
                                        XML
                                    </x-base.menu.item>
                                </x-base.menu.items>
                            </x-base.menu>
                            <x-base.button
                                class="hidden sm:flex"
                                variant="outline-secondary"
                            >
                                <x-base.lucide
                                    class="w-4 h-4 mr-2"
                                    icon="File"
                                /> Download XML
                            </x-base.button>
                        </div>
                        <div class="grid grid-cols-1 gap-6 p-5 2xl:grid-cols-7">
                            <div class="2xl:col-span-2">
                                <div class="grid grid-cols-2 gap-6">
                                    <div class="col-span-2 p-5 box dark:bg-darkmode-500 sm:col-span-1 2xl:col-span-2">
                                        <div class="font-medium">Net Worth</div>
                                        <div class="flex items-center mt-1 sm:mt-0">
                                            <div class="flex w-20 mr-4">
                                                USP:
                                                <span class="ml-3 font-medium text-success">
                                                    +23%
                                                </span>
                                            </div>
                                            <div class="w-5/6 overflow-auto">
                                                <x-simple-line-chart height="h-[51px]" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-span-2 p-5 box dark:bg-darkmode-500 sm:col-span-1 2xl:col-span-2">
                                        <div class="font-medium">Sales</div>
                                        <div class="flex items-center mt-1 sm:mt-0">
                                            <div class="flex w-20 mr-4">
                                                USP:
                                                <span class="ml-3 font-medium text-danger">
                                                    -5%
                                                </span>
                                            </div>
                                            <div class="w-5/6 overflow-auto">
                                                <x-simple-line-chart height="h-[51px]" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-span-2 p-5 box dark:bg-darkmode-500 sm:col-span-1 2xl:col-span-2">
                                        <div class="font-medium">Profit</div>
                                        <div class="flex items-center mt-1 sm:mt-0">
                                            <div class="flex w-20 mr-4">
                                                USP:
                                                <span class="ml-3 font-medium text-danger">
                                                    -10%
                                                </span>
                                            </div>
                                            <div class="w-5/6 overflow-auto">
                                                <x-simple-line-chart height="h-[51px]" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-span-2 p-5 box dark:bg-darkmode-500 sm:col-span-1 2xl:col-span-2">
                                        <div class="font-medium">Products</div>
                                        <div class="flex items-center mt-1 sm:mt-0">
                                            <div class="flex w-20 mr-4">
                                                USP:
                                                <span class="ml-3 font-medium text-success">
                                                    +55%
                                                </span>
                                            </div>
                                            <div class="w-5/6 overflow-auto">
                                                <x-simple-line-chart height="h-[51px]" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="w-full 2xl:col-span-5">
                                <div class="flex justify-center mt-8">
                                    <div class="flex items-center mr-5">
                                        <div class="w-2 h-2 mr-3 rounded-full bg-primary"></div>
                                        <span>Product Profit</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 mr-3 rounded-full bg-slate-300"></div>
                                        <span>Author Sales</span>
                                    </div>
                                </div>
                                <div class="mt-8">
                                    <x-stacked-bar-chart-1 height="h-[420px]" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: General Statistic -->
                </div>
            </x-base.tab.panel>
        </x-base.tab.panels>
    </x-base.tab.group>
@endsection
