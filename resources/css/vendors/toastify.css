@import "toastify-js/src/toastify.css";

.toastify {
    background: none;
    box-shadow: none;
    padding: 0;
    color: theme("colors.slate.700");
    max-width: calc(100% - 30px);
    @media screen(md) {
        max-width: none;
    }
    .toast-close {
        color: transparent;
        position: absolute;
        opacity: 1;
        top: 0;
        bottom: 0;
        right: 0;
        padding-right: theme("spacing.3");
        display: flex;
        align-items: center;
        &:before {
            content: "×";
            display: block;
            color: theme("colors.slate.500");
            font-size: theme("fontSize.3xl");
            margin-top: calc(theme("spacing.[1.5]") * -1);
            font-weight: theme("fontWeight.light");
        }
    }
}
