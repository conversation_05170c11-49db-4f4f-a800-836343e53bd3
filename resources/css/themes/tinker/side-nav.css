@import "@left4code/tw-starter/dist/css/mixins/_media.css";

.tinker {
    .side-nav {
        &.side-nav--simple {
            .side-menu {
                .side-menu__title {
                    display: none;
                    .side-menu__sub-icon {
                        display: none;
                    }
                }
            }
        }
        .side-nav__divider {
            width: 100%;
            height: 1px;
            background-color: theme("colors.white" / 8%);
            z-index: 10;
            position: relative;
        }
        .side-menu {
            height: 50px;
            display: flex;
            align-items: center;
            padding-left: theme("spacing.5");
            color: white;
            margin-bottom: theme("spacing.1");
            position: relative;
            border-radius: theme("borderRadius.lg");
            .side-menu__title {
                display: none;
                align-items: center;
                width: 100%;
                margin-left: theme("spacing.3");
                @media screen(xl) {
                    display: flex;
                }
                .side-menu__sub-icon {
                    transition-property: theme("transitionProperty.DEFAULT");
                    transition-timing-function: theme(
                        "transitionTimingFunction.in"
                    );
                    transition-duration: theme("transitionDuration.100");
                    margin-left: auto;
                    margin-right: theme("spacing.5");
                    display: none;
                    @media screen(xl) {
                        display: block;
                    }
                    svg {
                        width: theme("spacing.4");
                        height: theme("spacing.4");
                    }
                }
            }
        }
        & > ul {
            @for $i from 1 through 50 {
                > li:nth-child($i) {
                    &.side-nav__divider {
                        opacity: 0;
                        animation: 0.4s
                            intro-divider-animation
                            ease-in-out
                            0.33333s;
                        animation-fill-mode: forwards;
                        animation-delay: calc($i * 0.1s);
                    }
                    & > a {
                        &:not(.side-menu--active) {
                            opacity: 0;
                            transform: translateX(50px);
                            animation: 0.4s
                                intro-menu-animation
                                ease-in-out
                                0.33333s;
                            animation-fill-mode: forwards;
                            animation-delay: calc($i * 0.1s);
                        }
                    }
                }
            }
            ul {
                @for $i from 1 through 50 {
                    li:nth-child($i) {
                        & > a {
                            opacity: 0;
                            transform: translateX(50px);
                            animation: 0.2s
                                intro-submenu-animation
                                ease-in-out
                                0.33333s;
                            animation-fill-mode: forwards;
                            animation-delay: calc($i * 0.1s);
                        }
                    }
                }
            }
            & > li {
                /* First level */
                & > .side-menu {
                    &.side-menu--active {
                        background-color: theme("colors.theme.1");
                        z-index: 10;
                        &:before {
                            content: "";
                            display: block;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background-color: theme("colors.white" / 8%);
                            border-radius: theme("borderRadius.lg");
                            position: absolute;
                            border-bottom: 3px solid theme("colors.black" / 10%);
                        }
                        .side-menu__icon {
                            z-index: 10;
                        }
                        .side-menu__title {
                            font-weight: theme("fontWeight.medium");
                            z-index: 10;
                        }
                    }
                    &:hover {
                        &:not(.side-menu--active):not(.side-menu--open) {
                            background-color: theme("colors.theme.1" / 60%);
                            &:before {
                                content: "";
                                display: block;
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                background-color: theme("colors.white" / 4%);
                                border-radius: theme("borderRadius.xl");
                                position: absolute;
                                z-index: -1;
                            }
                        }
                    }
                }
                & > ul {
                    background-color: theme("colors.white" / 4%);
                    border-radius: theme("borderRadius.xl");
                    position: relative;
                    &:before {
                        content: "";
                        display: block;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background-color: theme("colors.theme.1" / 60%);
                        border-radius: theme("borderRadius.xl");
                        position: absolute;
                        z-index: -1;
                    }
                    &:not(.side-menu__sub-open) {
                        display: none;
                    }
                    & > li {
                        /* Second level */
                        & > .side-menu {
                            &.side-menu--active {
                                .side-menu__title {
                                    font-weight: theme("fontWeight.medium");
                                }
                            }
                            &:not(.side-menu--active) {
                                color: theme("colors.white" / 70%);
                            }
                            &:hover {
                            }
                            .side-menu__icon {
                            }
                        }
                        & > ul {
                            background-color: theme("colors.white" / 4%);
                            border-radius: theme("borderRadius.xl");
                            position: relative;
                            &:before {
                                content: "";
                                display: block;
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                background-color: theme("colors.theme.1" / 60%);
                                border-radius: theme("borderRadius.xl");
                                position: absolute;
                                z-index: -1;
                            }
                            &:not(.side-menu__sub-open) {
                                display: none;
                            }
                            & > li {
                                /* Third level */
                                & > .side-menu {
                                    &.side-menu--active {
                                        .side-menu__title {
                                            font-weight: theme(
                                                "fontWeight.medium"
                                            );
                                        }
                                    }
                                    &:not(.side-menu--active) {
                                        color: theme("colors.white" / 70%);
                                    }
                                    &:hover {
                                    }
                                    .side-menu__icon {
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@keyframes intro-divider-animation {
    100% {
        opacity: 1;
    }
}

@keyframes intro-menu-animation {
    100% {
        opacity: 1;
        transform: translateX(0px);
    }
}

@keyframes intro-submenu-animation {
    100% {
        opacity: 1;
        transform: translateX(0px);
    }
}

.dark {
    .tinker {
        .side-nav {
            .side-nav__divider {
                background-color: theme("colors.white" / 7%);
            }
            & > ul {
                & > li {
                    /* First level */
                    & > .side-menu {
                        &.side-menu--active {
                            background-color: transparent;
                            &:before {
                                border-color: theme("colors.black" / 10%);
                                background-color: theme("colors.darkmode.700");
                            }
                            .side-menu__icon {
                                color: theme("colors.slate.300");
                            }
                            .side-menu__title {
                                color: theme("colors.slate.300");
                            }
                        }
                        &:not(.side-menu--active) {
                            .side-menu__icon {
                                color: theme("colors.slate.400");
                            }
                            .side-menu__title {
                                color: theme("colors.slate.400");
                            }
                        }
                        &:hover {
                            &:not(.side-menu--active):not(.side-menu--open) {
                                &:before {
                                    background-color: theme(
                                        "colors.darkmode.700"
                                    );
                                }
                            }
                        }
                    }
                    & > ul {
                        background-color: transparent;
                        &:before {
                            background-color: theme(
                                "colors.darkmode.900" / 30%
                            );
                        }
                        & > li {
                            /* Second level */
                            & > .side-menu {
                                &.side-menu--active {
                                    color: theme("colors.slate.300");
                                }
                                &:not(.side-menu--active) {
                                    color: theme("colors.slate.400");
                                }
                                &:hover {
                                }
                                .side-menu__icon {
                                }
                            }
                            & > ul {
                                background-color: transparent;
                                &:before {
                                    background-color: theme(
                                        "colors.darkmode.900" / 30%
                                    );
                                }
                                & > li {
                                    /* Third level */
                                    & > .side-menu {
                                        &.side-menu--active {
                                            color: theme("colors.slate.300");
                                        }
                                        &:not(.side-menu--active) {
                                            color: theme("colors.slate.400");
                                        }
                                        &:hover {
                                        }
                                        .side-menu__icon {
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
