<?php

namespace Database\Seeders;

use App\Models\AccidentType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AccidentTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $accidentTypes = collect([
            ["type" => "Avoidable"],
            ["type" => "Unavoidable"],
        ]);

        $accidentTypes->each(function($accidentType) {
            AccidentType::create([
                'type' => $accidentType['type'],
            ]);
        });
    }
}
