<?php

namespace Database\Seeders;

use App\Models\TripStatus;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TripStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tripStatuses = collect([
            // ["status" => "Pending"],
            ["status" => "Assigned"],
            ["status" => "Driver In Transit"],
            ["status" => "Trip Started"],
            ["status" => "Trip Ended"],
            ["status" => "Trip Cancelled"],
        ]);

        $tripStatuses->each(function($tripStatus) {
            TripStatus::create([
                'status' => $tripStatus['status'],
            ]);
        });
    }
}
