<?php

namespace Database\Seeders;

use App\Models\HireDriverStatus;
use App\Models\SupabaseModels\SupabaseHireDriverStatus;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class HireDriverStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statuses = collect([
            ["status" => "Pending"],
            ["status" => "Assigned"],
            ["status" => "In Transit"],
            ["status" => "Completed"],
        ]);

        $statuses->each(function($status) {
            HireDriverStatus::create([
                'status' => $status['status'],
            ]);
            SupabaseHireDriverStatus::create([
                'status' => $status['status'],
            ]);
        });

    }
}
