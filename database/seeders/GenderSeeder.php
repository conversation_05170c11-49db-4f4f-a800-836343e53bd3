<?php

namespace Database\Seeders;

use App\Models\Gender;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class GenderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $genders = collect([
            ["gender" => "Male"],
            ["gender" => "Female"],
        ]);

        $genders->each(function($gender) {
            Gender::create([
                'gender' => $gender['gender'],
            ]);
        });
    }
}
