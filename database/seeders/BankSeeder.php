<?php

namespace Database\Seeders;

use App\Models\Bank;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $banks = collect([
            ["bank" => "CABS"],
            ["bank" => "FBC"],
            ["bank" => "NBS"],
            ["bank" => "Stanbic"],
        ]);

        $banks->each(function($bank) {
            Bank::create([
                'bank' => $bank['bank'],
            ]);
        });
    }
}
