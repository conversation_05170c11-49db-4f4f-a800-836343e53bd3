<?php

namespace Database\Seeders;

use App\Models\DriversLicence;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DriversLicenceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $licenceClasses = collect([
            ["licence_class" => "Class 4"],
            ["licence_class" => "Class 2"],
            ["licence_class" => "Class 1"],

        ]);

        $licenceClasses->each(function($licenceClass) {
            DriversLicence::create([
                'licence_class' => $licenceClass['licence_class'],
            ]);
        });
    }
}
