<?php

namespace Database\Seeders;

use App\Models\RentalStatus;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RentalStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rentalStatuses = collect([
            // ["status" => "Pending"],
            ["status" => "Pending"],
            ["status" => "Initiated"],
            ["status" => "Hired"],
            ["status" => "Returned"],
            ["status" => "Overdue"],
        ]);

        $rentalStatuses->each(function($rentalStatus) {
            RentalStatus::create([
                'status' => $rentalStatus['status'],
            ]);
        });
    }
}
