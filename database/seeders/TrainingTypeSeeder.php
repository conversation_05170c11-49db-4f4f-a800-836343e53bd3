<?php

namespace Database\Seeders;

use App\Models\TrainingType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TrainingTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $trainingTypes = collect([
            ["name" => "Impala Drivers Induction training"],
            ["name" => "Impala drivers Code of conduct training"],
            ["name" => "Impala Safe driving training"],
            ["name" => "Impala Chauffeur drivers grooming and etiquette training"],
            ["name" => "CMED VIP driving course"],
            ["name" => "Impala drivers inhouse retest yearly"],
        ]);

        $trainingTypes->each(function($trainingType) {
            TrainingType::create([
                'name' => $trainingType['name'],
            ]);
        });
    }
}







