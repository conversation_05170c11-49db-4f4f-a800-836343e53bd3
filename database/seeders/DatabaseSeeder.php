<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\AccidentType;
use App\Models\DriversLicence;
use App\Models\RentalStatus;
use App\Models\TrainingType;
use App\Models\TripStatus;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);

        $this->call(BankSeeder::class);
        $this->call(DriversLicenceSeeder::class);
        $this->call(GenderSeeder::class);
        $this->call(TrainingTypeSeeder::class);
        $this->call(AccidentTypeSeeder::class);
        $this->call(TripStatusSeeder::class);
        $this->call(HireDriverStatusSeeder::class);
        $this->call(RentalStatusSeeder::class);
    }
}
