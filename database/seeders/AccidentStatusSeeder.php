<?php

namespace Database\Seeders;

use App\Models\AccidentStatus;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AccidentStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $accidentStatuses = collect([
            ["status" => "Pending Investigation"],
            ["status" => "Investigation in progress"],
            ["status" => "Awaiting judgement"],
            ["status" => "Case closed"],
        ]);

        $accidentStatuses->each(function($accidentStatus) {
            AccidentStatus::create([
                'status' => $accidentStatus['status'],
            ]);
        });
    }
}
