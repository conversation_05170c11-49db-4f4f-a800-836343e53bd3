<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('driver_auths', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('token')->nullable();
            $table->text('supabase_id')->nullable();
            $table->text('one_signal_id')->nullable();
            $table->integer('status')->default(0);
            $table->string('code')->nullable();
            $table->string('phonenumber')->nullable();
            $table->foreignId('driver_id')
            ->references('id')
            ->on('drivers');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('driver_auths');
    }
};
