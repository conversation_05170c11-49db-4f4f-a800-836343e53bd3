<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       
            Schema::create('driver_bookings', function (Blueprint $table) {
                $table->id();
                $table->foreignId('hire_driver_type_id')
                ->references('id')
                ->on('hire_driver_types');
                $table->foreignId('client_id')
                ->nullable()
                ->references('id')
                ->on('clients')
                ->cascadeOnDelete();
                $table->foreignId('driver_id')
                ->nullable()
                ->references('id')
                ->on('drivers');
                $table->foreignId('trip_status_id')
                ->references('id')
                ->on('trip_statuses');
                $table->text('comments')->default('');
                $table->string('phonenumber');
                $table->date('date_of_hire');
                $table->foreignId('drivers_licence_id')
                ->nullable()
                ->references('id')
                ->on('drivers_licences')
                ->cascadeOnDelete();
                $table->timestamps();
            });
      
    }



    

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('driver_bookings', function (Blueprint $table) {
            //
        });
    }
};
