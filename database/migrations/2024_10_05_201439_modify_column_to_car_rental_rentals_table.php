<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('car_rentals', function (Blueprint $table) {
            $table->unsignedBigInteger('rental_status_id')->nullable();  // or not nullable based on your needs

            $table->foreign('rental_status_id')->references('id')->on('rental_statuses') // Replace 'another_table' with the referenced table
                  ->onDelete('cascade'); 
            $table->dropConstrainedForeignId('driver_id');
            $table->string('phonenumber')->nullable();
            $table->decimal('total_cost', 8, 2)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('car_rentals', function (Blueprint $table) {
            //
        });
    }
};
