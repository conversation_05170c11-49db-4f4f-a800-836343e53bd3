<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('border_driver_hirings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')
                ->nullable()
                ->references('id')
                ->on('clients')
                ->cascadeOnDelete();
            $table->foreignId('driver_id')
            ->nullable()
            ->references('id')
            ->on('drivers');
            $table->date('date_of_hire');
            $table->foreignId('hire_driver_type_id')
                ->references('id')
                ->on('hire_driver_types');
            $table->foreignId('drivers_licence_id')
            ->nullable()
            ->references('id')
            ->on('drivers_licences')
            ->cascadeOnDelete();
            $table->string('car_description');
            $table->text('comments');
            $table->string('phonenumber');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('border_driver_hirings');
    }
};
