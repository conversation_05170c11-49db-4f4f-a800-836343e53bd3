<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shuttle_trips', function (Blueprint $table) {
            $table->unsignedBigInteger('shuttle_booking_id')->nullable();  // or not nullable based on your needs

            $table->foreign('shuttle_booking_id')->references('id')->on('shuttle_bookings') // Replace 'another_table' with the referenced table
                  ->onDelete('cascade'); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shuttle_trips', function (Blueprint $table) {
            //
        });
    }
};
