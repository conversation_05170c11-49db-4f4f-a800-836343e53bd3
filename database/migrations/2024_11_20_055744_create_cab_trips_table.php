<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cab_trips', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')
            ->references('id')
            ->on('clients')
            ->cascadeOnDelete();
            $table->foreignId('driver_id')
            ->references('id')
            ->on('drivers')
            ->cascadeOnDelete();
            $table->decimal('driver_start_lat', 10, 8);
            $table->decimal('driver_start_lon', 10, 8);
            $table->decimal('pickup_lat', 10, 8);
            $table->decimal('pickup_lon', 10, 8);
            $table->decimal('destination_lat', 10, 8);
            $table->decimal('destination_lon', 10, 8);
            $table->decimal('fare', 8, 2);
            $table->foreignId('trip_status_id')
            ->references('id')
            ->on('trip_statuses');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cab_trips');
    }
};
