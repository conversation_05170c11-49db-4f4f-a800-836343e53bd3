<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cab_driver_locations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('taxi_booking_id')
            ->references('id')
            ->on('taxi_bookings');
            $table->foreignId('cab_trip_id')
            ->references('id')
            ->on('cab_trips');
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 10, 8);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cab_driver_locations');
    }
};
