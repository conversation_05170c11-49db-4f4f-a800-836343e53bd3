<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('car_rentals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')
            ->references('id')
            ->on('clients')
            ->cascadeOnDelete();
            $table->foreignId('rental_price_id')
            ->references('id')
            ->on('rental_prices')
            ->cascadeOnDelete();
            $table->foreignId('driver_id')
            ->nullable()
            ->references('id')
            ->on('drivers')
            ->cascadeOnDelete();
            $table->string('drivers_licence');
            $table->date('booking_date');
            $table->date('from_date');
            $table->date('to_date');
            $table->text('comments');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('car_rentals');
    }
};
