<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shuttle_trackings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shuttle_booking_id')
            ->references('id')
            ->on('shuttle_bookings')
            ->cascadeOnDelete();
            $table->decimal('lat', 10, 8);
            $table->decimal('lon', 10, 8);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shuttle_trackings');
    }
};