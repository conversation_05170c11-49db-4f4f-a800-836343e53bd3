<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('driver_bookings', function (Blueprint $table) {
            $table->unsignedBigInteger('hire_driver_status_id')->nullable();  // or not nullable based on your needs

            $table->foreign('hire_driver_status_id')->references('id')->on('hire_driver_statuses') // Replace 'another_table' with the referenced table
                  ->onDelete('cascade'); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('driver_bookings', function (Blueprint $table) {
            //
        });
    }
};
