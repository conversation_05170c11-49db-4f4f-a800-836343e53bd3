<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('driver_bookings', function (Blueprint $table) {
            $table->unsignedBigInteger('hire_driver_rate_id')->nullable();  // or not nullable based on your needs

            // Add the foreign key constraint
            $table->foreign('hire_driver_rate_id')->references('id')->on('hire_driver_rates') // Replace 'another_table' with the referenced table
                  ->onDelete('cascade'); // or
            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('driver_bookings', function (Blueprint $table) {
            //
        });
    }
};
