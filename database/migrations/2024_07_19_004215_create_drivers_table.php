<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drivers', function (Blueprint $table) {
            $table->id();
            $table->string('driver_firstname');
            $table->string('driver_lastname');
            $table->string('driver_mobile');
            $table->string('driver_second_mobile');
            $table->string('driver_email');
            $table->string('driver_address');
            $table->tinyInteger('driving_experience');
            $table->string('drivers_licence');
            $table->string('account_number');
            $table->string('national_id');
            $table->string('passport_number')->nullable();
            $table->string('ecocash_number')->nullable();
            $table->string('innbucks_number')->nullable();
            $table->unsignedBigInteger('gender_id');
            $table->unsignedBigInteger('drivers_licence_id');
            $table->unsignedBigInteger('bank_id');
            $table->date('driver_dob');
            $table->date('licence_issue_date');
            $table->date('defence_licence_expiry_date');
            $table->text('licence_file_path');
            $table->text('defence_licence_file_path');
            $table->text('medical_test_file_path');
            $table->text('police_clearance_file_path');
            $table->text('proof_of_residence_file_path');
            $table->text('national_id_file_path');
            $table->text('passport_file_path')->nullable();
            $table->text('first_aid_certificate_file_path')->nullable();
            $table->text('idl_licence_path')->nullable();
            $table->timestamps();

            $table->foreign('gender_id')->unsigned()->references('id')->on('genders');
            $table->foreign('drivers_licence_id')->unsigned()->references('id')->on('drivers_licences');
            $table->foreign('bank_id')->unsigned()->references('id')->on('banks');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            Schema::dropIfExists('drivers');
        });
    }
};
