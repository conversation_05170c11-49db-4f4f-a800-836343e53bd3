<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->morphs('payable');
            $table->foreignId('client_id')
            ->references('id')
            ->on('clients')
            ->cascadeOnDelete();
            $table->text('reference')->nullable();
            $table->decimal('amount_invoiced',8,2)->nullable();
            $table->decimal('amount_paid',8,2)->nullable();
            $table->string('payment_method') ->nullable();
            $table->string('payment_method_reference') ->nullable();
            $table->string('status')->default('pending');
            $table->string('currency') ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
