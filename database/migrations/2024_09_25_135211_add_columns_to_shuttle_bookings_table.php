<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shuttle_bookings', function (Blueprint $table) {
            $table->decimal('trip_cost', 8, 2);
            $table->decimal('trip_total_distance', 8, 2);
            $table->addColumn('text','trip_route_points');
            $table->addColumn('text','trip_encoded_points');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shuttle_bookings', function (Blueprint $table) {
            
        });
    }
};
