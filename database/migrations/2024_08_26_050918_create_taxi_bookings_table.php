<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('taxi_bookings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('client_id')
            ->references('id')
            ->on('clients')
            ->cascadeOnDelete();
            $table->foreignId('driver_id')
            ->nullable()
            ->references('id')
            ->on('drivers')
            ->cascadeOnDelete();
            $table->decimal('pick_up_latitude', 10, 8);
            $table->decimal('pick_up_longitude', 10, 8);
            $table->decimal('drop_off_latitude', 10, 8);
            $table->decimal('drop_off_longitude', 10, 8);
            $table->date('trip_date');
            $table->date('trip_time');
            $table->foreignId('trip_status_id')
            ->references('id')
            ->on('trip_statuses')
            ->cascadeOnDelete();
            $table->foreignId('cab_rate_id')
            ->references('id')
            ->on('cab_rates')
            ->cascadeOnDelete();
            $table->timestamps();
        });
    }

    

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('taxi_bookings');
    }
};
