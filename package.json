{"private": true, "scripts": {"dev": "vite", "build": "vite build --mode development"}, "dependencies": {"@ckeditor/ckeditor5-build-balloon": "^35.1.0", "@ckeditor/ckeditor5-build-balloon-block": "^35.1.0", "@ckeditor/ckeditor5-build-classic": "^35.1.0", "@ckeditor/ckeditor5-build-decoupled-document": "^35.1.0", "@ckeditor/ckeditor5-build-inline": "^35.1.0", "@fullcalendar/core": "^6.1.7", "@fullcalendar/daygrid": "^6.1.7", "@fullcalendar/interaction": "^6.1.7", "@fullcalendar/list": "^6.1.7", "@fullcalendar/timegrid": "^6.1.7", "@left4code/tw-starter": "^4.0.1", "@popperjs/core": "^2.11.0", "alpinejs": "^3.14.1", "chart.js": "^4.4.0", "dayjs": "^1.11.5", "dropzone": "^6.0.0-beta.2", "filepond": "^4.31.1", "filepond-plugin-file-validate-type": "^1.2.9", "filepond-plugin-image-preview": "^4.6.12", "flat": "^5.0.2", "highlight.js": "^11.6.0", "js-beautify": "^1.14.6", "leaflet": "^1.9.3", "leaflet.markercluster": "^1.5.3", "litepicker": "^2.0.12", "lucide": "^0.290.0", "pristinejs": "^1.0.0", "simplebar": "^5.3.9", "tabulator-tables": "^5.3.4", "tailwind-merge": "^1.8.1", "tiny-slider": "^2.9.4", "tippy.js": "^6.3.7", "toastify-js": "^1.12.0", "tom-select": "^2.2.0", "xlsx": "^0.18.5", "zoom-vanilla.js": "^2.0.6"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "autoprefixer": "^10.4.16", "axios": "^1.1.2", "laravel-vite-plugin": "^0.8.0", "lodash": "^4.17.19", "postcss": "^8.4.31", "postcss-advanced-variables": "^3.0.1", "postcss-import": "^15.1.0", "tailwindcss": "^3.3.5", "vite": "^4.0.0"}}