<?php

// @formatter:off
// phpcs:ignoreFile
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON> <<EMAIL>>
 */


namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $driver_id
 * @property int $accident_status_id
 * @property int $accident_type_id
 * @property string|null $details
 * @property string|null $report_file
 * @property string $accident_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\AccidentStatus $accidentStatus
 * @property-read \App\Models\AccidentType $accidentType
 * @property-read \App\Models\Driver $driver
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport query()
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport whereAccidentDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport whereAccidentStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport whereAccidentTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport whereReportFile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentReport whereUpdatedAt($value)
 */
	class AccidentReport extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AccidentReport> $accidentReports
 * @property-read int|null $accident_reports_count
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentStatus whereUpdatedAt($value)
 */
	class AccidentStatus extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AccidentReport> $accidentReports
 * @property-read int|null $accident_reports_count
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentType query()
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentType whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccidentType whereUpdatedAt($value)
 */
	class AccidentType extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $bank
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Bank newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Bank newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Bank query()
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereBank($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereUpdatedAt($value)
 */
	class Bank extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $classification_id
 * @property string $amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\CarClassification|null $carClassification
 * @method static \Illuminate\Database\Eloquent\Builder|BookingPrice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BookingPrice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BookingPrice query()
 * @method static \Illuminate\Database\Eloquent\Builder|BookingPrice whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingPrice whereClassificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingPrice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingPrice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookingPrice whereUpdatedAt($value)
 */
	class BookingPrice extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int|null $client_id
 * @property int|null $driver_id
 * @property string $date_of_hire
 * @property int $hire_driver_type_id
 * @property int|null $drivers_licence_id
 * @property string $car_description
 * @property string $comments
 * @property string $phonenumber
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $hire_driver_rate_id
 * @property int|null $hire_driver_status_id
 * @property-read \App\Models\Client|null $client
 * @property-read \App\Models\Driver|null $driver
 * @property-read \App\Models\DriversLicence|null $driversLicence
 * @property-read \App\Models\HireDriverStatus|null $hireDriverStatus
 * @property-read \App\Models\HireDriverType $hireType
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring query()
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereCarDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereDateOfHire($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereDriversLicenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereHireDriverRateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereHireDriverStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereHireDriverTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring wherePhonenumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BorderDriverHiring whereUpdatedAt($value)
 */
	class BorderDriverHiring extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $taxi_booking_id
 * @property int $cab_trip_id
 * @property string $latitude
 * @property string $longitude
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\TaxiBooking $booking
 * @property-read \App\Models\CabTrip $trip
 * @method static \Illuminate\Database\Eloquent\Builder|CabDriverLocation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CabDriverLocation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CabDriverLocation query()
 * @method static \Illuminate\Database\Eloquent\Builder|CabDriverLocation whereCabTripId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabDriverLocation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabDriverLocation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabDriverLocation whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabDriverLocation whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabDriverLocation whereTaxiBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabDriverLocation whereUpdatedAt($value)
 */
	class CabDriverLocation extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $car_classification_id
 * @property string $amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\CarClassification $carClassification
 * @method static \Illuminate\Database\Eloquent\Builder|CabRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CabRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CabRate query()
 * @method static \Illuminate\Database\Eloquent\Builder|CabRate whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabRate whereCarClassificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabRate whereUpdatedAt($value)
 */
	class CabRate extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int $driver_id
 * @property string $driver_start_lat
 * @property string $driver_start_lon
 * @property string $pickup_lat
 * @property string $pickup_lon
 * @property string $destination_lat
 * @property string $destination_lon
 * @property string $fare
 * @property int $trip_status_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $taxi_booking_id
 * @property-read \App\Models\TaxiBooking $booking
 * @property-read \App\Models\Client $client
 * @property-read \App\Models\Driver $driver
 * @property-read \App\Models\TripStatus $status
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip query()
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereDestinationLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereDestinationLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereDriverStartLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereDriverStartLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereFare($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip wherePickupLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip wherePickupLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereTaxiBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereTripStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CabTrip whereUpdatedAt($value)
 */
	class CabTrip extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $classification
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\BookingPrice|null $bookingPrice
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\CarModel> $carModels
 * @property-read int|null $car_models_count
 * @property-read \App\Models\RentalPrice|null $rentalPrice
 * @method static \Illuminate\Database\Eloquent\Builder|CarClassification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CarClassification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CarClassification query()
 * @method static \Illuminate\Database\Eloquent\Builder|CarClassification whereClassification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarClassification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarClassification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarClassification whereUpdatedAt($value)
 */
	class CarClassification extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property int $car_classification_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\CarClassification $carClassification
 * @property-read \App\Models\ShuttleCity|null $shuttleCity
 * @method static \Illuminate\Database\Eloquent\Builder|CarModel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CarModel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CarModel query()
 * @method static \Illuminate\Database\Eloquent\Builder|CarModel whereCarClassificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarModel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarModel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarModel whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarModel whereUpdatedAt($value)
 */
	class CarModel extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int $rental_price_id
 * @property string $drivers_licence
 * @property string $booking_date
 * @property string $from_date
 * @property string $to_date
 * @property string $comments
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $rental_status_id
 * @property string|null $phonenumber
 * @property string|null $total_cost
 * @property-read \App\Models\Client $client
 * @property-read \App\Models\DriversLicence|null $driverLicence
 * @property-read \App\Models\RentalPrice $rentalPrice
 * @property-read \App\Models\RentalStatus|null $rentalStatus
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental query()
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereBookingDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereDriversLicence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereFromDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental wherePhonenumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereRentalPriceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereRentalStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereToDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereTotalCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CarRental whereUpdatedAt($value)
 */
	class CarRental extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $city_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|City newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|City newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|City query()
 * @method static \Illuminate\Database\Eloquent\Builder|City whereCityName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|City whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|City whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|City whereUpdatedAt($value)
 */
	class City extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string|null $name
 * @property string|null $phonenumber
 * @property string|null $token
 * @property string $supabase_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $supabase_image_url
 * @property string|null $one_signal_id
 * @method static \Illuminate\Database\Eloquent\Builder|Client newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Client newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Client query()
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereOneSignalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client wherePhonenumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereSupabaseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereSupabaseImageUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereUpdatedAt($value)
 */
	class Client extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $driver_firstname
 * @property string $driver_lastname
 * @property string $driver_mobile
 * @property string $driver_second_mobile
 * @property string $driver_email
 * @property string $driver_address
 * @property int $driving_experience
 * @property string $drivers_licence
 * @property string $account_number
 * @property string $national_id
 * @property string|null $passport_number
 * @property string|null $ecocash_number
 * @property string|null $innbucks_number
 * @property int $gender_id
 * @property int $drivers_licence_id
 * @property int $bank_id
 * @property string $driver_dob
 * @property string $licence_issue_date
 * @property string $defence_licence_expiry_date
 * @property string $licence_file_path
 * @property string $defence_licence_file_path
 * @property string $medical_test_file_path
 * @property string $police_clearance_file_path
 * @property string $proof_of_residence_file_path
 * @property string $national_id_file_path
 * @property string|null $passport_file_path
 * @property string|null $first_aid_certificate_file_path
 * @property string|null $idl_licence_path
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $medical_tests_issue_date
 * @property string $profile_photo_file
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AccidentReport> $accidentReports
 * @property-read int|null $accident_reports_count
 * @property-read \App\Models\Bank $bank
 * @property-read \App\Models\DriversLicence $driversLicence
 * @property-read \App\Models\Gender $gender
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InternalComplain> $internalComplains
 * @property-read int|null $internal_complains_count
 * @property-read \App\Models\NextOfKin|null $nextOfKin
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Rating> $ratings
 * @property-read int|null $ratings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Training> $trainings
 * @property-read int|null $trainings_count
 * @method static \Illuminate\Database\Eloquent\Builder|Driver newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Driver newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Driver query()
 * @method static \Illuminate\Database\Eloquent\Builder|Driver search($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereAccountNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereBankId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDefenceLicenceExpiryDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDefenceLicenceFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDriverAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDriverDob($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDriverEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDriverFirstname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDriverLastname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDriverMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDriverSecondMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDriversLicence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDriversLicenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereDrivingExperience($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereEcocashNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereFirstAidCertificateFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereGenderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereIdlLicencePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereInnbucksNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereLicenceFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereLicenceIssueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereMedicalTestFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereMedicalTestsIssueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereNationalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereNationalIdFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver wherePassportFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver wherePassportNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver wherePoliceClearanceFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereProfilePhotoFile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereProofOfResidenceFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Driver whereUpdatedAt($value)
 */
	class Driver extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string|null $token
 * @property string|null $supabase_id
 * @property string|null $one_signal_id
 * @property int $status
 * @property string|null $code
 * @property string|null $phonenumber
 * @property int $driver_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Driver $driver
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth query()
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth whereOneSignalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth wherePhonenumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth whereSupabaseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverAuth whereUpdatedAt($value)
 */
	class DriverAuth extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int|null $client_id
 * @property int|null $driver_id
 * @property string $comments
 * @property string $phonenumber
 * @property string $date_of_hire
 * @property int|null $drivers_licence_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $hire_driver_rate_id
 * @property int|null $hire_driver_status_id
 * @property-read \App\Models\Client|null $client
 * @property-read \App\Models\Driver|null $driver
 * @property-read \App\Models\HireDriverStatus|null $hireDriverStatus
 * @property-read \App\Models\HireDriverType|null $hireDriverType
 * @property-read \App\Models\DriversLicence|null $licence
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking query()
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking whereComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking whereDateOfHire($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking whereDriversLicenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking whereHireDriverRateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking whereHireDriverStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking wherePhonenumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriverBooking whereUpdatedAt($value)
 */
	class DriverBooking extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $licence_class
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|DriversLicence newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DriversLicence newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DriversLicence query()
 * @method static \Illuminate\Database\Eloquent\Builder|DriversLicence whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriversLicence whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriversLicence whereLicenceClass($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DriversLicence whereUpdatedAt($value)
 */
	class DriversLicence extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $gender
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Gender newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Gender newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Gender query()
 * @method static \Illuminate\Database\Eloquent\Builder|Gender whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Gender whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Gender whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Gender whereUpdatedAt($value)
 */
	class Gender extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $hire_driver_type_id
 * @property string $amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HireDriverType $hireDriverType
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverRate query()
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverRate whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverRate whereHireDriverTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverRate whereUpdatedAt($value)
 */
	class HireDriverRate extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverStatus whereUpdatedAt($value)
 */
	class HireDriverStatus extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $hire_type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HireDriverRate|null $hireDriverRate
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverType query()
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverType whereHireType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HireDriverType whereUpdatedAt($value)
 */
	class HireDriverType extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $certificate
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ImpalaCertification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ImpalaCertification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ImpalaCertification query()
 * @method static \Illuminate\Database\Eloquent\Builder|ImpalaCertification whereCertificate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ImpalaCertification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ImpalaCertification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ImpalaCertification whereUpdatedAt($value)
 */
	class ImpalaCertification extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property int $driver_id
 * @property string $details
 * @property string $date_of_report
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Driver $driver
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|InternalComplain newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InternalComplain newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InternalComplain query()
 * @method static \Illuminate\Database\Eloquent\Builder|InternalComplain whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InternalComplain whereDateOfReport($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InternalComplain whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InternalComplain whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InternalComplain whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InternalComplain whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InternalComplain whereUserId($value)
 */
	class InternalComplain extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int|null $driver_id
 * @property string $fullname
 * @property string $mobile_number
 * @property string|null $address
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Driver|null $driver
 * @method static \Illuminate\Database\Eloquent\Builder|NextOfKin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NextOfKin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NextOfKin query()
 * @method static \Illuminate\Database\Eloquent\Builder|NextOfKin whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NextOfKin whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NextOfKin whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NextOfKin whereFullname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NextOfKin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NextOfKin whereMobileNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NextOfKin whereUpdatedAt($value)
 */
	class NextOfKin extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $payable_type
 * @property int $payable_id
 * @property int $client_id
 * @property string|null $reference
 * @property string|null $amount_invoiced
 * @property string|null $amount_paid
 * @property string|null $payment_method
 * @property string|null $payment_method_reference
 * @property string $status
 * @property string|null $currency
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $payable
 * @method static \Illuminate\Database\Eloquent\Builder|Payment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Payment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Payment query()
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereAmountInvoiced($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereAmountPaid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment wherePayableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment wherePayableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment wherePaymentMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment wherePaymentMethodReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereUpdatedAt($value)
 */
	class Payment extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int $driver_id
 * @property int $rating
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Client $client
 * @property-read \App\Models\Driver $driver
 * @method static \Illuminate\Database\Eloquent\Builder|Rating newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Rating newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Rating query()
 * @method static \Illuminate\Database\Eloquent\Builder|Rating whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Rating whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Rating whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Rating whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Rating whereRating($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Rating whereUpdatedAt($value)
 */
	class Rating extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $car_classification_id
 * @property string $amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deposit
 * @property string|null $refundable_deposit
 * @property-read \App\Models\CarClassification $carClassification
 * @method static \Illuminate\Database\Eloquent\Builder|RentalPrice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RentalPrice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RentalPrice query()
 * @method static \Illuminate\Database\Eloquent\Builder|RentalPrice whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RentalPrice whereCarClassificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RentalPrice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RentalPrice whereDeposit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RentalPrice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RentalPrice whereRefundableDeposit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RentalPrice whereUpdatedAt($value)
 */
	class RentalPrice extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|RentalStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RentalStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RentalStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|RentalStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RentalStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RentalStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RentalStatus whereUpdatedAt($value)
 */
	class RentalStatus extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int|null $driver_id
 * @property string $pick_up_latitude
 * @property string $pick_up_longitude
 * @property string $drop_off_latitude
 * @property string $drop_off_longitude
 * @property string $trip_date
 * @property string $trip_time
 * @property int $trip_status_id
 * @property int $shuttle_city_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $trip_cost
 * @property string $trip_total_distance
 * @property string $trip_route_points
 * @property string $trip_encoded_points
 * @property string|null $pickup_address
 * @property string|null $destination_address
 * @property-read \App\Models\Client $client
 * @property-read \App\Models\Driver|null $driver
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $payment
 * @property-read \App\Models\ShuttleCity $shuttleCity
 * @property-read \App\Models\TripStatus $tripStatus
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereDestinationAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereDropOffLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereDropOffLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking wherePickUpLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking wherePickUpLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking wherePickupAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereShuttleCityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereTripCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereTripDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereTripEncodedPoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereTripRoutePoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereTripStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereTripTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereTripTotalDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleBooking whereUpdatedAt($value)
 */
	class ShuttleBooking extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $car_classification_id
 * @property int $city_id
 * @property string $price_per_km
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\CarClassification $carClassification
 * @property-read \App\Models\City $city
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleCity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleCity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleCity query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleCity whereCarClassificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleCity whereCityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleCity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleCity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleCity wherePricePerKm($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleCity whereUpdatedAt($value)
 */
	class ShuttleCity extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $shuttle_booking_id
 * @property int $shuttle_trip_id
 * @property string $latitude
 * @property string $longitude
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\ShuttleBooking $booking
 * @property-read \App\Models\ShuttleTrip $trip
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleDriverLocation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleDriverLocation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleDriverLocation query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleDriverLocation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleDriverLocation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleDriverLocation whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleDriverLocation whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleDriverLocation whereShuttleBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleDriverLocation whereShuttleTripId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleDriverLocation whereUpdatedAt($value)
 */
	class ShuttleDriverLocation extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $shuttle_booking_id
 * @property string $lat
 * @property string $lon
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTracking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTracking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTracking query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTracking whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTracking whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTracking whereLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTracking whereLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTracking whereShuttleBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTracking whereUpdatedAt($value)
 */
	class ShuttleTracking extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int $driver_id
 * @property string $driver_start_lat
 * @property string $driver_start_lon
 * @property string $pickup_lat
 * @property string $pickup_lon
 * @property string $destination_lat
 * @property string $destination_lon
 * @property string $fare
 * @property int $trip_status_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $shuttle_booking_id
 * @property-read \App\Models\TaxiBooking|null $booking
 * @property-read \App\Models\Client $client
 * @property-read \App\Models\Driver $driver
 * @property-read \App\Models\TripStatus $status
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereDestinationLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereDestinationLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereDriverStartLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereDriverStartLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereFare($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip wherePickupLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip wherePickupLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereShuttleBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereTripStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShuttleTrip whereUpdatedAt($value)
 */
	class ShuttleTrip extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $driver_id
 * @property int $accident_status_id
 * @property int $accident_type_id
 * @property string|null $details
 * @property string|null $report_file
 * @property string $accident_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseAccidentStatus $accidentStatus
 * @property-read \App\Models\SupabaseModels\SupabaseAccidentType $accidentType
 * @property-read \App\Models\SupabaseModels\SupabaseDriver $driver
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport whereAccidentDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport whereAccidentStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport whereAccidentTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport whereReportFile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentReport whereUpdatedAt($value)
 */
	class SupabaseAccidentReport extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SupabaseModels\SupabaseAccidentReport> $accidentReports
 * @property-read int|null $accident_reports_count
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentStatus whereUpdatedAt($value)
 */
	class SupabaseAccidentStatus extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SupabaseModels\SupabaseAccidentReport> $accidentReports
 * @property-read int|null $accident_reports_count
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentType query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentType whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseAccidentType whereUpdatedAt($value)
 */
	class SupabaseAccidentType extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $bank
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBank newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBank newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBank query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBank whereBank($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBank whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBank whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBank whereUpdatedAt($value)
 */
	class SupabaseBank extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $classification_id
 * @property string $amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseCarClassification|null $carClassification
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBookingPrice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBookingPrice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBookingPrice query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBookingPrice whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBookingPrice whereClassificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBookingPrice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBookingPrice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBookingPrice whereUpdatedAt($value)
 */
	class SupabaseBookingPrice extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int|null $client_id
 * @property int|null $driver_id
 * @property int|null $drivers_licence_id
 * @property string $car_description
 * @property string $comments
 * @property string $phonenumber
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $hire_driver_rate_id
 * @property string $date_of_collection
 * @property int|null $hire_driver_status_id
 * @property-read \App\Models\SupabaseModels\SupabaseClient|null $client
 * @property-read \App\Models\SupabaseModels\SupabaseDriver|null $driver
 * @property-read \App\Models\SupabaseModels\SupabaseDriversLicence|null $driversLicence
 * @property-read \App\Models\SupabaseModels\SupabaseHireDriverStatus|null $hireDriverStatus
 * @property-read \App\Models\SupabaseModels\SupabaseHireDriverRate|null $hireRate
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereCarDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereDateOfCollection($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereDriversLicenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereHireDriverRateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereHireDriverStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring wherePhonenumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseBorderDriverHiring whereUpdatedAt($value)
 */
	class SupabaseBorderDriverHiring extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $taxi_booking_id
 * @property int $cab_trip_id
 * @property string $latitude
 * @property string $longitude
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseTaxiBooking $booking
 * @property-read \App\Models\SupabaseModels\SupabaseCabTrip $trip
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabDriverLocation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabDriverLocation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabDriverLocation query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabDriverLocation whereCabTripId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabDriverLocation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabDriverLocation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabDriverLocation whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabDriverLocation whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabDriverLocation whereTaxiBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabDriverLocation whereUpdatedAt($value)
 */
	class SupabaseCabDriverLocation extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $car_classification_id
 * @property string $amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseCarClassification $carClassification
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabRate query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabRate whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabRate whereCarClassificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabRate whereUpdatedAt($value)
 */
	class SupabaseCabRate extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int $driver_id
 * @property string $driver_start_lat
 * @property string $driver_start_lon
 * @property string $pickup_lat
 * @property string $pickup_lon
 * @property string $destination_lat
 * @property string $destination_lon
 * @property string $fare
 * @property int $trip_status_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $taxi_booking_id
 * @property-read \App\Models\SupabaseModels\SupabaseTaxiBooking $booking
 * @property-read \App\Models\SupabaseModels\SupabaseClient $client
 * @property-read \App\Models\SupabaseModels\SupabaseDriver $driver
 * @property-read \App\Models\SupabaseModels\SupabaseTripStatus $status
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereDestinationLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereDestinationLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereDriverStartLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereDriverStartLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereFare($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip wherePickupLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip wherePickupLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereTaxiBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereTripStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCabTrip whereUpdatedAt($value)
 */
	class SupabaseCabTrip extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $classification
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseBookingPrice|null $bookingPrice
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SupabaseModels\SupabaseCarModel> $carModels
 * @property-read int|null $car_models_count
 * @property-read \App\Models\SupabaseModels\SupabaseRentalPrice|null $rentalPrice
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarClassification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarClassification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarClassification query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarClassification whereClassification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarClassification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarClassification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarClassification whereUpdatedAt($value)
 */
	class SupabaseCarClassification extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property int $car_classification_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseCarClassification $carClassification
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarModel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarModel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarModel query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarModel whereCarClassificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarModel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarModel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarModel whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarModel whereUpdatedAt($value)
 */
	class SupabaseCarModel extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int $rental_price_id
 * @property string $drivers_licence
 * @property string $booking_date
 * @property string $from_date
 * @property string $to_date
 * @property string $comments
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $rental_status_id
 * @property string|null $phonenumber
 * @property string|null $total_cost
 * @property-read \App\Models\SupabaseModels\SupabaseClient $client
 * @property-read \App\Models\SupabaseModels\SupabaseRentalPrice $rentalPrice
 * @property-read \App\Models\SupabaseModels\SupabaseRentalStatus|null $rentalStatus
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereBookingDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereDriversLicence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereFromDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental wherePhonenumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereRentalPriceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereRentalStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereToDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereTotalCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCarRental whereUpdatedAt($value)
 */
	class SupabaseCarRental extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $city_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCity query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCity whereCityName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseCity whereUpdatedAt($value)
 */
	class SupabaseCity extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string|null $name
 * @property string|null $phonenumber
 * @property string|null $token
 * @property string $supabase_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $supabase_image_url
 * @property string|null $one_signal_id
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient whereOneSignalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient wherePhonenumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient whereSupabaseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient whereSupabaseImageUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseClient whereUpdatedAt($value)
 */
	class SupabaseClient extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $driver_firstname
 * @property string $driver_lastname
 * @property string $driver_mobile
 * @property string $driver_second_mobile
 * @property string $driver_email
 * @property string $driver_address
 * @property int $driving_experience
 * @property string $drivers_licence
 * @property string $account_number
 * @property string $national_id
 * @property string|null $passport_number
 * @property string|null $ecocash_number
 * @property string|null $innbucks_number
 * @property int $gender_id
 * @property int $drivers_licence_id
 * @property int $bank_id
 * @property string $driver_dob
 * @property string $licence_issue_date
 * @property string $defence_licence_expiry_date
 * @property string $licence_file_path
 * @property string $defence_licence_file_path
 * @property string $medical_test_file_path
 * @property string $police_clearance_file_path
 * @property string $proof_of_residence_file_path
 * @property string $national_id_file_path
 * @property string|null $passport_file_path
 * @property string|null $first_aid_certificate_file_path
 * @property string|null $idl_licence_path
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $medical_tests_issue_date
 * @property string $profile_photo_file
 * @property string $police_clearance_issue_date
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SupabaseModels\SupabaseAccidentReport> $accidentReports
 * @property-read int|null $accident_reports_count
 * @property-read \App\Models\SupabaseModels\SupabaseBank $bank
 * @property-read \App\Models\SupabaseModels\SupabaseDriversLicence $driversLicence
 * @property-read \App\Models\SupabaseModels\SupabaseGender $gender
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SupabaseModels\SupabaseInternalComplain> $internalComplains
 * @property-read int|null $internal_complains_count
 * @property-read \App\Models\SupabaseModels\SupabaseNextOfKin|null $nextOfKin
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SupabaseModels\SupabaseTraining> $trainings
 * @property-read int|null $trainings_count
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver search($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereAccountNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereBankId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDefenceLicenceExpiryDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDefenceLicenceFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDriverAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDriverDob($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDriverEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDriverFirstname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDriverLastname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDriverMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDriverSecondMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDriversLicence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDriversLicenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereDrivingExperience($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereEcocashNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereFirstAidCertificateFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereGenderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereIdlLicencePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereInnbucksNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereLicenceFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereLicenceIssueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereMedicalTestFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereMedicalTestsIssueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereNationalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereNationalIdFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver wherePassportFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver wherePassportNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver wherePoliceClearanceFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver wherePoliceClearanceIssueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereProfilePhotoFile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereProofOfResidenceFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriver whereUpdatedAt($value)
 */
	class SupabaseDriver extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string|null $token
 * @property string|null $supabase_id
 * @property string|null $one_signal_id
 * @property int $status
 * @property string|null $code
 * @property string|null $phonenumber
 * @property int $driver_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseDriver $driver
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth whereOneSignalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth wherePhonenumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth whereSupabaseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverAuth whereUpdatedAt($value)
 */
	class SupabaseDriverAuth extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int|null $client_id
 * @property int|null $driver_id
 * @property string $comments
 * @property string $phonenumber
 * @property string $date_of_hire
 * @property int|null $drivers_licence_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $hire_driver_rate_id
 * @property int|null $hire_driver_status_id
 * @property-read \App\Models\SupabaseModels\SupabaseClient|null $client
 * @property-read \App\Models\SupabaseModels\SupabaseDriver|null $driver
 * @property-read \App\Models\SupabaseModels\SupabaseHireDriverRate|null $hireDriverRate
 * @property-read \App\Models\SupabaseModels\SupabaseHireDriverStatus|null $hireDriverStatus
 * @property-read \App\Models\SupabaseModels\SupabaseDriversLicence|null $licence
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking whereComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking whereDateOfHire($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking whereDriversLicenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking whereHireDriverRateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking whereHireDriverStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking wherePhonenumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriverBooking whereUpdatedAt($value)
 */
	class SupabaseDriverBooking extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $licence_class
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriversLicence newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriversLicence newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriversLicence query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriversLicence whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriversLicence whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriversLicence whereLicenceClass($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseDriversLicence whereUpdatedAt($value)
 */
	class SupabaseDriversLicence extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $gender
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseGender newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseGender newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseGender query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseGender whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseGender whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseGender whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseGender whereUpdatedAt($value)
 */
	class SupabaseGender extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $hire_driver_type_id
 * @property string $amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseHireDriverType $hireDriverType
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverRate query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverRate whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverRate whereHireDriverTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverRate whereUpdatedAt($value)
 */
	class SupabaseHireDriverRate extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverStatus whereUpdatedAt($value)
 */
	class SupabaseHireDriverStatus extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $hire_type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseHireDriverRate|null $hireDriverRate
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverType query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverType whereHireType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseHireDriverType whereUpdatedAt($value)
 */
	class SupabaseHireDriverType extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $certificate
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseImpalaCertification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseImpalaCertification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseImpalaCertification query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseImpalaCertification whereCertificate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseImpalaCertification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseImpalaCertification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseImpalaCertification whereUpdatedAt($value)
 */
	class SupabaseImpalaCertification extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property int $driver_id
 * @property string $details
 * @property string $date_of_report
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseDriver $driver
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseInternalComplain newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseInternalComplain newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseInternalComplain query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseInternalComplain whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseInternalComplain whereDateOfReport($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseInternalComplain whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseInternalComplain whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseInternalComplain whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseInternalComplain whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseInternalComplain whereUserId($value)
 */
	class SupabaseInternalComplain extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int|null $driver_id
 * @property string $fullname
 * @property string $mobile_number
 * @property string|null $address
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseDriver|null $driver
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseNextOfKin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseNextOfKin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseNextOfKin query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseNextOfKin whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseNextOfKin whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseNextOfKin whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseNextOfKin whereFullname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseNextOfKin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseNextOfKin whereMobileNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseNextOfKin whereUpdatedAt($value)
 */
	class SupabaseNextOfKin extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $payable
 * @method static \Illuminate\Database\Eloquent\Builder|SupabasePayment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabasePayment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabasePayment query()
 */
	class SupabasePayment extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int $driver_id
 * @property int $rating
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseClient $client
 * @property-read \App\Models\SupabaseModels\SupabaseDriver $driver
 * @property-read \Illuminate\Database\Eloquent\Collection<int, SupabaseRating> $ratings
 * @property-read int|null $ratings_count
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRating newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRating newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRating query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRating whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRating whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRating whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRating whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRating whereRating($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRating whereUpdatedAt($value)
 */
	class SupabaseRating extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $car_classification_id
 * @property string $amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deposit
 * @property string|null $refundable_deposit
 * @property-read \App\Models\SupabaseModels\SupabaseCarClassification $carClassification
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalPrice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalPrice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalPrice query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalPrice whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalPrice whereCarClassificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalPrice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalPrice whereDeposit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalPrice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalPrice whereRefundableDeposit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalPrice whereUpdatedAt($value)
 */
	class SupabaseRentalPrice extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseRentalStatus whereUpdatedAt($value)
 */
	class SupabaseRentalStatus extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int|null $driver_id
 * @property string $pick_up_latitude
 * @property string $pick_up_longitude
 * @property string $drop_off_latitude
 * @property string $drop_off_longitude
 * @property string $trip_date
 * @property string $trip_time
 * @property int $trip_status_id
 * @property int $shuttle_city_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $trip_cost
 * @property string $trip_total_distance
 * @property string $trip_route_points
 * @property string $trip_encoded_points
 * @property string|null $pickup_address
 * @property string|null $destination_address
 * @property-read \App\Models\SupabaseModels\SupabaseClient $client
 * @property-read \App\Models\SupabaseModels\SupabaseDriver|null $driver
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $payment
 * @property-read \App\Models\SupabaseModels\SupabaseShuttleCity $shuttleCity
 * @property-read \App\Models\SupabaseModels\SupabaseTripStatus $tripStatus
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereDestinationAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereDropOffLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereDropOffLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking wherePickUpLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking wherePickUpLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking wherePickupAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereShuttleCityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereTripCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereTripDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereTripEncodedPoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereTripRoutePoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereTripStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereTripTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereTripTotalDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleBooking whereUpdatedAt($value)
 */
	class SupabaseShuttleBooking extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $car_classification_id
 * @property int $city_id
 * @property string $price_per_km
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseCarClassification $carClassification
 * @property-read \App\Models\SupabaseModels\SupabaseCity $city
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleCity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleCity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleCity query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleCity whereCarClassificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleCity whereCityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleCity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleCity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleCity wherePricePerKm($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleCity whereUpdatedAt($value)
 */
	class SupabaseShuttleCity extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $shuttle_booking_id
 * @property int $shuttle_trip_id
 * @property string $latitude
 * @property string $longitude
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseShuttleBooking $booking
 * @property-read \App\Models\SupabaseModels\SupabaseShuttleTrip $trip
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleDriverLocation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleDriverLocation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleDriverLocation query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleDriverLocation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleDriverLocation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleDriverLocation whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleDriverLocation whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleDriverLocation whereShuttleBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleDriverLocation whereShuttleTripId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleDriverLocation whereUpdatedAt($value)
 */
	class SupabaseShuttleDriverLocation extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTracking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTracking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTracking query()
 */
	class SupabaseShuttleTracking extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int $driver_id
 * @property string $driver_start_lat
 * @property string $driver_start_lon
 * @property string $pickup_lat
 * @property string $pickup_lon
 * @property string $destination_lat
 * @property string $destination_lon
 * @property string $fare
 * @property int $trip_status_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $taxi_booking_id
 * @property-read \App\Models\SupabaseModels\SupabaseTaxiBooking|null $booking
 * @property-read \App\Models\SupabaseModels\SupabaseClient $client
 * @property-read \App\Models\SupabaseModels\SupabaseDriver $driver
 * @property-read \App\Models\SupabaseModels\SupabaseTripStatus $status
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereDestinationLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereDestinationLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereDriverStartLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereDriverStartLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereFare($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip wherePickupLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip wherePickupLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereTaxiBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereTripStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseShuttleTrip whereUpdatedAt($value)
 */
	class SupabaseShuttleTrip extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int|null $driver_id
 * @property string $pick_up_latitude
 * @property string $pick_up_longitude
 * @property string $drop_off_latitude
 * @property string $drop_off_longitude
 * @property string $trip_date
 * @property string $trip_time
 * @property int $trip_status_id
 * @property int $cab_rate_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $trip_cost
 * @property string $trip_total_distance
 * @property string $trip_route_points
 * @property string $trip_encoded_points
 * @property string|null $pickup_address
 * @property string|null $destination_address
 * @property-read \App\Models\SupabaseModels\SupabaseCabRate $cabRate
 * @property-read \App\Models\SupabaseModels\SupabaseClient $client
 * @property-read \App\Models\SupabaseModels\SupabaseDriver|null $driver
 * @property-read \App\Models\SupabaseModels\SupabaseTripStatus $tripStatus
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking search($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereCabRateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereDestinationAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereDropOffLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereDropOffLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking wherePickUpLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking wherePickUpLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking wherePickupAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereTripCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereTripDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereTripEncodedPoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereTripRoutePoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereTripStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereTripTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereTripTotalDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiBooking whereUpdatedAt($value)
 */
	class SupabaseTaxiBooking extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int $taxi_booking_id
 * @property string $lat
 * @property string $lon
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseTaxiBooking $taxiBooking
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiTracking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiTracking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiTracking query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiTracking whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiTracking whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiTracking whereLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiTracking whereLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiTracking whereTaxiBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTaxiTracking whereUpdatedAt($value)
 */
	class SupabaseTaxiTracking extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property int|null $driver_id
 * @property int|null $training_type_id
 * @property string $training_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\SupabaseModels\SupabaseDriver|null $driver
 * @property-read \App\Models\SupabaseModels\SupabaseTrainingType|null $trainingType
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTraining newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTraining newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTraining query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTraining whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTraining whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTraining whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTraining whereTrainingDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTraining whereTrainingTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTraining whereUpdatedAt($value)
 */
	class SupabaseTraining extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SupabaseModels\SupabaseTraining> $trainings
 * @property-read int|null $trainings_count
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTrainingType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTrainingType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTrainingType query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTrainingType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTrainingType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTrainingType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTrainingType whereUpdatedAt($value)
 */
	class SupabaseTrainingType extends \Eloquent {}
}

namespace App\Models\SupabaseModels{
/**
 * 
 *
 * @property int $id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTripStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTripStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTripStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTripStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTripStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTripStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupabaseTripStatus whereUpdatedAt($value)
 */
	class SupabaseTripStatus extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $client_id
 * @property int|null $driver_id
 * @property string $pick_up_latitude
 * @property string $pick_up_longitude
 * @property string $drop_off_latitude
 * @property string $drop_off_longitude
 * @property string $trip_date
 * @property string $trip_time
 * @property int $trip_status_id
 * @property int $cab_rate_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $trip_cost
 * @property string $trip_total_distance
 * @property string $trip_route_points
 * @property string $trip_encoded_points
 * @property string|null $pickup_address
 * @property string|null $destination_address
 * @property-read \App\Models\CabRate $cabRate
 * @property-read \App\Models\Client|null $client
 * @property-read \App\Models\Driver|null $driver
 * @property-read \App\Models\Payment|null $payment
 * @property-read \App\Models\TripStatus|null $status
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking query()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereCabRateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereDestinationAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereDropOffLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereDropOffLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking wherePickUpLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking wherePickUpLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking wherePickupAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereTripCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereTripDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereTripEncodedPoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereTripRoutePoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereTripStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereTripTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereTripTotalDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiBooking whereUpdatedAt($value)
 */
	class TaxiBooking extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $taxi_booking_id
 * @property string $lat
 * @property string $lon
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\TaxiBooking $taxiBooking
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiTracking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiTracking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiTracking query()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiTracking whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiTracking whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiTracking whereLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiTracking whereLon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiTracking whereTaxiBookingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxiTracking whereUpdatedAt($value)
 */
	class TaxiTracking extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int|null $driver_id
 * @property int|null $training_type_id
 * @property string $training_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Driver|null $driver
 * @property-read \App\Models\TrainingType|null $trainingType
 * @method static \Illuminate\Database\Eloquent\Builder|Training newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Training newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Training query()
 * @method static \Illuminate\Database\Eloquent\Builder|Training whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Training whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Training whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Training whereTrainingDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Training whereTrainingTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Training whereUpdatedAt($value)
 */
	class Training extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Training> $trainings
 * @property-read int|null $trainings_count
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingType query()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingType whereUpdatedAt($value)
 */
	class TrainingType extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|TripStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TripStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TripStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|TripStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TripStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TripStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TripStatus whereUpdatedAt($value)
 */
	class TripStatus extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property mixed $password
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
 */
	class User extends \Eloquent {}
}

