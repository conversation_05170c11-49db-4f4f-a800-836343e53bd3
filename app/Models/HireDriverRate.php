<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class HireDriverRate extends Model
{
    use HasFactory;
    
    protected $guarded = [];
    protected $with = ['hireDriverType'];

    public function hireDriverType(): BelongsTo {
        return $this->belongsTo(HireDriverType::class);
    }
}
