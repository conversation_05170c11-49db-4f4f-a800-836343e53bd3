<?php

namespace App\Models;

use App\Models\SupabaseModels\SupabaseRentalStatus;
use App\Models\SupabaseModels\SupabaseClient;
use App\Models\SupabaseModels\SupabaseDriversLicence;
use App\Models\SupabaseModels\SupabaseRentalPrice;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CarRental extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $table = 'car_rentals';

    protected $with = ['client','rentalPrice','driverLicence', 'rentalStatus'];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function rentalPrice(): BelongsTo
    {
        return $this->belongsTo(RentalPrice::class);
    }

    public function driverLicence(): BelongsTo
    {
        return $this->belongsTo(DriversLicence::class);
    }

    public function rentalStatus(): BelongsTo
    {
        return $this->belongsTo(RentalStatus::class);
    }


}
  