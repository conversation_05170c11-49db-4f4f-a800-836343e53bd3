<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShuttleBooking extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function client():BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function driver():BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    public function tripStatus():BelongsTo
    {
        return $this->belongsTo(TripStatus::class);
    }

    public function shuttleCity():BelongsTo
    {
        return $this->belongsTo(ShuttleCity::class);
    }


    public function payment(){
        return $this->morphTo(Payment::class, 'payable');
    }

}