<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DriverBooking extends Model
{
    use HasFactory;

    protected $guareded = [];


    protected $connection = 'mysql';
    protected $table = 'driver_bookings';
    

    public function hireDriverType():BelongsTo
    {
        return $this->belongsTo(HireDriverType::class, 'hire_driver_type_id');
    }

    public function driver():BelongsTo
    {
        return $this->belongsTo(Driver::class, 'driver_id');
    }

    public function client():BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function licence():BelongsTo
    {
        return $this->belongsTo(DriversLicence::class, 'drivers_licence_id');
    }

    public function hireDriverStatus():BelongsTo
    {
        return $this->belongsTo(HireDriverStatus::class, 'hire_driver_status_id');
    }

}
