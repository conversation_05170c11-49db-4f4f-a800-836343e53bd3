<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AccidentReport extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $with = [
        'driver',
        'accidentStatus',
        'accidentType',      
    ];

    public function driver(): BelongsTo{

        return $this->belongsTo(Driver::class);
    }

    public function accidentStatus(): BelongsTo{
        return $this->belongsTo(AccidentStatus::class);
    }

    public function accidentType(): BelongsTo{
        return $this->belongsTo(AccidentType::class);
    }

}
