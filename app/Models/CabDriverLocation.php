<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CabDriverLocation extends Model
{
    use HasFactory;

    protected $connection = 'mysql';
    protected $table = 'cab_driver_locations';
    protected $guarded = [];

    protected $with = [
        'trip',
        'booking',
    ];


    public function trip(): BelongsTo
    {
        return $this->belongsTo(CabTrip::class, 'cab_trip_id');
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(TaxiBooking::class, 'taxi_booking_id');
    }
}
