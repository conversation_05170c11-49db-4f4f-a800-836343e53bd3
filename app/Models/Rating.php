<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Rating extends Model
{
    use HasFactory;


    protected $connection = 'mysql';
    protected $table = 'ratings';
    use HasFactory;
    protected $guarded = [];

    public function client():BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function driver():BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }
}