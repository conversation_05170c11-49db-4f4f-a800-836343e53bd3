<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DriverAuth extends Model
{
    use HasFactory;

    protected $connection = 'mysql';
    protected $table = 'driver_auths';
    protected $guarded = [];

    public function driver(): BelongsTo {
        return $this->belongsTo(Driver::class, 'driver_id');
    }
}
