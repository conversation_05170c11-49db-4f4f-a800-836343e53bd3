<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Driver extends Model
{
    use HasFactory;

    protected $connection = 'mysql';
    protected $table = 'drivers';

    protected $guarded = [];

    protected $with = [
        'trainings',
        'nextOfKin',
        'gender',
        'driversLicence',
        'bank',
    ];

    public function trainings(): HasMany
    {
        return $this->hasMany(Training::class);
    }

    public function nextOfKin(): HasOne
    {
        return $this->hasOne(NextOfKin::class);
    }

    public function gender():BelongsTo
    {
        return $this->belongsTo(Gender::class);
    }

    public function driversLicence(): BelongsTo
    {
        return $this->belongsTo(DriversLicence::class);
    }

    public function bank(): BelongsTo
    {
        return $this->belongsTo(Bank::class);
    }

    public function internalComplains(): HasMany {
        return $this->hasMany(InternalComplain::class);
    }

    public function accidentReports(): HasMany {
        return $this->hasMany(AccidentReport::class);
    }

    public function ratings(): HasMany {
        return $this->hasMany(Rating::class);
    }


    public function scopeSearch($query, $value)
    {
        $query->where('driver_firstname', 'ilike', "%{$value}%")
            ->orWhere('driver_lastname', 'ilike', "%{$value}%");

        return $query;
    }
}