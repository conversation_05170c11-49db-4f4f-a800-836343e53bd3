<?php

namespace App\Models\SupabaseModels;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseTaxiBooking extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'taxi_bookings';

    protected $guarded = [];

    protected $with = ['client','tripStatus','cabRate', 'paymentPaid'];

    public function client(): BelongsTo
    {
        return $this->belongsTo(SupabaseClient::class, 'client_id');
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(SupabaseDriver::class, 'driver_id');
    }

    public function tripStatus(): BelongsTo
    {
        return $this->belongsTo(SupabaseTripStatus::class, 'trip_status_id');
    }

    public function cabRate(): BelongsTo
    {
        return $this->belongsTo(SupabaseCabRate::class, 'cab_rate_id');
    }

    public function paymentPaid():BelongsTo
    {
        return $this->belongsTo(SupabasePayment::class, 'payment_id');
    }


    public function scopeSearch($query, $value)
    {
        $query->where('driver_firstname', 'like', "%{$value}%")
            ->orWhere('driver_lastname', 'like', "%{$value}%");

        return $query;
    }

    public function payment()
    {
        return $this->morphOne(SupabasePayment::class,"payable");
    }


}
