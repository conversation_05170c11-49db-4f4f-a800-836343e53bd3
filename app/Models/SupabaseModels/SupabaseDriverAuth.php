<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseDriverAuth extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'driver_auths';
    protected $guarded = [];

    public function driver(): BelongsTo {
        return $this->belongsTo(SupabaseDriver::class, 'driver_id');
    }
}
