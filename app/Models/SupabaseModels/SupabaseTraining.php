<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupabaseTraining extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'trainings';
    protected $guarded = [];

    public function driver()
    {
        return $this->belongsTo(SupabaseDriver::class, 'driver_id');
    }

    public function trainingType()
    {
        return $this->belongsTo(SupabaseTrainingType::class, 'training_type_id');
    }
}
