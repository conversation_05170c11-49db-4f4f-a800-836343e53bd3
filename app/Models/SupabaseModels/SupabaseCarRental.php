<?php

namespace App\Models\SupabaseModels;

use App\Models\SupabaseModels\SupabaseRentalStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseCarRental extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'car_rentals';
    use HasFactory;

    protected $guarded = [];

    protected $with = ['client','rentalPrice', 'rentalStatus'];

    public function client(): BelongsTo
    {
        return $this->belongsTo(SupabaseClient::class, 'client_id');
    }

    public function rentalPrice(): BelongsTo
    {
        return $this->belongsTo(SupabaseRentalPrice::class, 'rental_price_id');
    }

    public function rentalStatus(): BelongsTo
    {
        return $this->belongsTo(SupabaseRentalStatus::class, 'rental_status_id');
    }
}
