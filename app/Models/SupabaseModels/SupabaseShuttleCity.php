<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseShuttleCity extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'shuttle_cities';

    protected $guarded = [];

    public function carClassification():BelongsTo  {
        return $this->belongsTo(SupabaseCarClassification::class, 'car_classification_id');
    }

    public function city(): BelongsTo {
        return $this->belongsTo(SupabaseCity::class, 'city_id');
    }
}
