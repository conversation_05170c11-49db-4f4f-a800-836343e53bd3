<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseCabTrip extends Model
{
    use HasFactory;

    protected $connection = 'mysql';
    protected $table = 'cab_trips';
    protected $guarded = [];

    protected $with = [
        'client',
        'driver',
        'status',
        'booking',
    ];

    public function client(): BelongsTo {
        return $this->belongsTo(SupabaseClient::class, 'client_id');
    }

    public function driver(): BelongsTo {
        return $this->belongsTo(SupabaseDriver::class, 'driver_id');
    }

    public function status(): BelongsTo {
        return $this->belongsTo(SupabaseTripStatus::class, 'trip_status_id');
    }

    public function booking(): BelongsTo {
        return $this->belongsTo(SupabaseTaxiBooking::class, 'taxi_booking_id');
    }
}
