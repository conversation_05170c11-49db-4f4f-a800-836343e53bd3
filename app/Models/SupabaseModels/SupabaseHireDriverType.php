<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Models\HireDriverRate;

class SupabaseHireDriverType extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'hire_driver_types';

    protected $guarded = [];

    public  function hireDriverRate(): HasOne {
        return $this->hasOne(SupabaseHireDriverRate::class, 'hire_driver_type_id');
    }
}
