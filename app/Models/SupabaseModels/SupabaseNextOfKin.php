<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupabaseNextOfKin extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'next_of_kins';
    protected $guarded = [];

    public function driver()
    {
        return $this->belongsTo(SupabaseDriver::class, 'driver_id');
    }
}
