<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\AccidentReport;

class SupabaseAccidentType extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'accident_types';

    protected $guarded = [];

    public function accidentReports(): HasMany{
        return $this->hasMany(SupabaseAccidentReport::class, 'accident_type_id');
    }
}
