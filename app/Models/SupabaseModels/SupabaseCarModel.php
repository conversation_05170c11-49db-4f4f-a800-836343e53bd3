<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Models\CarClassification;
use App\Models\ShuttleCity;

class SupabaseCarModel extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'car_models';

    protected $guarded = [];

    protected $with = ['carClassification'];

    public function carClassification(): BelongsTo{
        return $this->belongsTo(SupabaseCarClassification::class, 'car_classification_id');
    }



    // public function shuttleCity(): HasOne{
    //     return $this->hasOne(SupabaseShuttleCity::class, '');
    // }
}
