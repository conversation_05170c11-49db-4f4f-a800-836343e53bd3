<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupabaseTrainingType extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'training_types';
    protected $guarded = [];
    public function trainings()
    {
        return $this->hasMany(SupabaseTraining::class, 'training_type_id');
    }
}
 