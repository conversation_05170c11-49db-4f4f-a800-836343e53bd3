<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Foundation\Auth\User as Authenticatable;

class SupabaseUser extends Authenticatable implements AuthenticatableContract
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'users';
    protected $guarded = [];

    public function scopeSearch($query, $value)
    {
        $query->where('name', 'like', "%{$value}%");

        return $query;
    }

}
