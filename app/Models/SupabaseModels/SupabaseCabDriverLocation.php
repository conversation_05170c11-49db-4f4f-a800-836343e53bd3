<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseCabDriverLocation extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'cab_driver_locations';

    protected $guarded = [];

    protected $with = [
        'trip',
        'booking',
    ];


    public function trip(): BelongsTo
    {
        return $this->belongsTo(SupabaseCabTrip::class, 'cab_trip_id');
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(SupabaseTaxiBooking::class, 'taxi_booking_id');
    }

}
