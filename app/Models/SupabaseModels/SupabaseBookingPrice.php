<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\CarClassification;

class SupabaseBookingPrice extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'booking_prices';

    protected $guarded = [];

    public function carClassification(){

        return $this->belongsTo(SupabaseCarClassification::class, 'car_classification_id');
    }
}
