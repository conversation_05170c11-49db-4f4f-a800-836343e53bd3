<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;




class SupabaseDriverBooking extends Model
{
    
    protected $connection = 'pgsql';
    protected $table = 'driver_bookings';
    protected $guareded = [];

    protected $with = ['hireDriverRate', 'driver', 'client', 'licence', 'hireDriverStatus'];

    public function hireDriverRate():BelongsTo
    {
        return $this->belongsTo(SupabaseHireDriverRate::class, 'hire_driver_rate_id');
    }

    public function driver():BelongsTo
    {
        return $this->belongsTo(SupabaseDriver::class, 'driver_id');
    }

    public function client():BelongsTo
    {
        return $this->belongsTo(SupabaseClient::class, 'client_id');
    }

    public function licence():BelongsTo
    {
        return $this->belongsTo(SupabaseDriversLicence::class, 'drivers_licence_id');
    }

    public function hireDriverStatus():BelongsTo
    {
        return $this->belongsTo(SupabaseHireDriverStatus::class, 'hire_driver_status_id');
    }

}
