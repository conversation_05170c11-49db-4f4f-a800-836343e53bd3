<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseBorderDriverHiring extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'border_driver_hirings';
    protected $guarded = [];

    public function client (){
        return $this->belongsTo(SupabaseClient::class, 'client_id');
    }

    public function driver (){
        return $this->belongsTo(SupabaseDriver::class, 'driver_id');
    }

    public function hireRate (){
        return $this->belongsTo(SupabaseHireDriverRate::class, 'hire_driver_rate_id');
    }

    public function driversLicence (){
        return $this->belongsTo(SupabaseDriversLicence::class, 'drivers_licence_id');
    }

    public function hireDriverStatus():BelongsTo
    {
        return $this->belongsTo(SupabaseHireDriverStatus::class, 'hire_driver_status_id');
    }
}