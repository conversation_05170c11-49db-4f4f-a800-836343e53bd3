<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseTaxiTracking extends Model
{
    
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'taxi_trackings';

    protected $guareded = [];

    public function taxiBooking(): BelongsTo
    {
        return $this->belongsTo(SupabaseTaxiBooking::class, 'taxi_booking_id');
    }
}
