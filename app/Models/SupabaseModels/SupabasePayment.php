<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabasePayment extends Model
{
    use HasFactory;


    protected $guarded = [];
    protected $connection = 'pgsql';
    protected $table = 'payments';

    protected $with = [
        'client',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(SupabaseClient::class, 'client_id');
    }

    public function trip()
    {
        return $this->belongsTo(SupabaseTaxiBooking::class, 'trip_id');
    }

    public function payable()
    {
        return $this->morphTo();
    }


    public function scopeSearch($query, $value)
    {
        $query->where('payment_method_reference', 'like', "%{$value}%");

        return $query;
    }

}
