<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseAccidentReport extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';

    protected $table = "accident_reports";
    protected $guarded = [];

    protected $with = [
        'driver',
        'accidentStatus',
        'accidentType',      
    ];

    public function driver(): BelongsT<PERSON>{

        return $this->belongsTo(SupabaseDriver::class, 'driver_id');
    }

    public function accidentStatus(): BelongsTo{
        return $this->belongsTo(SupabaseAccidentStatus::class, 'accident_status_id');
    }

    public function accidentType(): BelongsTo{
        return $this->belongsTo(SupabaseAccidentType::class, 'accident_type_id');
    }
}
