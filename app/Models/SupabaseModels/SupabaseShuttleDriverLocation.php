<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseShuttleDriverLocation extends Model
{
    use HasFactory;

    protected $connection = 'mysql';
    protected $table = 'shuttle_driver_locations';
    protected $guarded = [];

    protected $with = [
        'trip',
        'booking',
    ];


    public function trip(): BelongsTo
    {
        return $this->belongsTo(SupabaseShuttleTrip::class, 'shuttle_trip_id');
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(SupabaseShuttleBooking::class, 'shuttle_booking_id');
    }
}
