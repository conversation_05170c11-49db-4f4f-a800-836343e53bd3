<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\CarClassification;

class SupabaseCabRate extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'cab_rates';

    protected $guarded = [];

    protected $with = ["carClassification"];

    public function carClassification(): BelongsTo{
        return $this->belongsTo(SupabaseCarClassification::class, 'car_classification_id');
    }
}
