<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupabaseClient extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'clients';
    protected $guarded = [];

    public function scopeSearch($query, $value)
    {
        $query->where('name', 'like', "%{$value}%")->limit(1);

        return $query;
    }

}
