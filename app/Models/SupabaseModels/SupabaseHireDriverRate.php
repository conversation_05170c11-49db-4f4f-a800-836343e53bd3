<?php

namespace App\Models\SupabaseModels;

use App\Models\HireDriverType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
;

class SupabaseHireDriverRate extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'hire_driver_rates';

    protected $guarded = [];
    protected $with = ['hireDriverType'];

    public function hireDriverType(): BelongsTo {
        return $this->belongsTo(SupabaseHireDriverType::class, 'hire_driver_type_id');
    }
}
