<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SupabaseAccidentStatus extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';

    protected $table = 'accident_statuses';

    protected $guareded = [];

    public function accidentReports(): HasMany{
        return $this->hasMany(SupabaseAccidentReport::class, 'accident_status_id');
    }
}
