<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseShuttleBooking extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'shuttle_bookings';

    protected $guarded = [];

    protected $with = ['client', 'tripStatus', 'shuttleCity', 'paymentPaid'];

    public function client():BelongsTo
    {
        return $this->belongsTo(SupabaseClient::class, 'client_id');
    }

    public function driver():BelongsTo
    {
        return $this->belongsTo(SupabaseDriver::class, 'driver_id');
    }

    public function tripStatus():BelongsTo
    {
        return $this->belongsTo(SupabaseTripStatus::class, 'trip_status_id');
    }

    public function paymentPaid():BelongsTo
    {
        return $this->belongsTo(SupabasePayment::class, 'payment_id');
    }

    public function shuttleCity():BelongsTo
    {
        return $this->belongsTo(SupabaseShuttleCity::class, 'shuttle_city_id');
    }

    public function payment()
    {
        return $this->morphOne(SupabasePayment::class, 'payable');
    }



}
