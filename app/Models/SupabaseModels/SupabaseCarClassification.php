<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Models\CarModel;
use App\Models\RentalPrice;
use App\Models\BookingPrice;

class SupabaseCarClassification extends Model
{
    use HasFactory;

     protected $connection = 'pgsql';

    protected $guarded = [];

    protected $table = 'car_classifications';

    public function carModels(): HasMany {
        return $this->hasMany(SupabaseCarModel::class, 'classification_id');
    }

    public function rentalPrice(): HasOne {
        return $this->hasOne(SupabaseRentalPrice::class, 'classification_id');
    }

    public function bookingPrice(): HasOne {
        return $this->hasOne(SupabaseBookingPrice::class, 'classification_id');
    }
}
