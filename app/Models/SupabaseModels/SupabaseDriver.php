<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupabaseDriver extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'drivers';

    protected $guarded = [];

    protected $with = [
        'trainings',
        'nextOfKin',
        'gender',
        'driversLicence',
        'bank',
    ];

    public function trainings(): HasMany
    {
        return $this->hasMany(SupabaseTraining::class, 'driver_id');
    }

    public function nextOfKin(): HasOne
    {
        return $this->hasOne(SupabaseNextOfKin::class, 'driver_id');
    }

    public function gender():BelongsTo
    {
        return $this->belongsTo(SupabaseGender::class, 'gender_id');
    }

    public function driversLicence(): BelongsTo
    {
        return $this->belongsTo(SupabaseDriversLicence::class, 'drivers_licence_id');
    }


    public function bank(): BelongsTo
    {
        return $this->belongsTo(SupabaseBank::class, 'bank_id');
    }

    public function internalComplains(): HasMany {
        return $this->hasMany(SupabaseInternalComplain::class, 'driver_id');
    }

    public function accidentReports(): HasMany {
        return $this->hasMany(SupabaseAccidentReport::class, 'driver_id');
    }

    public function scopeSearch($query, $value)
    {
        $query->where('driver_firstname', 'like', "%{$value}%")
            ->orWhere('driver_lastname', 'like', "%{$value}%");

        return $query;
    }
}