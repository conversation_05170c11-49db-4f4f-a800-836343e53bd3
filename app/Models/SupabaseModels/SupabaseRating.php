<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SupabaseRating extends Model
{
    use HasFactory;


    protected $connection = 'pgsql';
    protected $table = 'ratings';
    use HasFactory;
    protected $guarded = [];

    public function client():BelongsTo
    {
        return $this->belongsTo(SupabaseClient::class, 'client_id');
    }

    public function driver():BelongsTo
    {
        return $this->belongsTo(SupabaseDriver::class, 'driver_id');
    }

    public function ratings(): HasMany {
        return $this->hasMany(SupabaseRating::class, 'driver_id');
    }

}