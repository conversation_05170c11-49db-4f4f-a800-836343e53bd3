<?php

namespace App\Models\SupabaseModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use App\Models\SupabaseModels\SupabaseDriver;

class SupabaseInternalComplain extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'internal_complains';

    protected $guarded = [];

    public function user():BelongsTo {
        return $this->belongsTo(User::class);
    }

    public function driver():BelongsTo {
        return $this->belongsTo(SupabaseDriver::class);
    }
}
