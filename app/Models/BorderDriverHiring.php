<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BorderDriverHiring extends Model
{
    use HasFactory;

    protected $connection = 'mysql';
    protected $table = 'border_driver_hirings';
    protected $guarded = [];

    public function client (){
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function driver (){
        return $this->belongsTo(Driver::class, 'driver_id');
    }

    public function hireType (){
        return $this->belongsTo(HireDriverType::class, 'hire_driver_type_id');
    }

    public function driversLicence (){
        return $this->belongsTo(DriversLicence::class, 'drivers_licence_id');
    }


    public function hireDriverStatus()
    {
        return $this->belongsTo(HireDriverStatus::class, 'hire_driver_status_id');
    }


}
