<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class ShuttleTrip extends Model
{
    use HasFactory;

    protected $connection = 'mysql';
    protected $table = 'shuttle_trips';
    protected $guarded = [];

    protected $with = [
        'client',
        'driver',
        'status',
        'booking',
    ];

    public function client(): BelongsTo {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function driver(): BelongsTo {
        return $this->belongsTo(Driver::class, 'driver_id');
    }

    public function status(): BelongsTo {
        return $this->belongsTo(TripStatus::class, 'trip_status_id');
    }

    public function booking(): BelongsTo {
        return $this->belongsTo(TaxiBooking::class, 'taxi_booking_id');
    }

}
