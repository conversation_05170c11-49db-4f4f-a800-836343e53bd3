<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TaxiBooking extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $with = ["payment"];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(TripStatus::class);
    }

    public function cabRate(): BelongsTo
    {
        return $this->belongsTo(CabRate::class);
    }

    public function payment()
    {
        return $this->morphOne(Payment::class,"payable");
    }
}