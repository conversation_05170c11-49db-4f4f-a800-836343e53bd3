<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InternalComplain extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function user():BelongsTo {
        return $this->belongsTo(User::class);
    }

    public function driver():BelongsTo {
        return $this->belongsTo(Driver::class);
    }
}
