<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CabRate extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $with = ["carClassification"];

    public function carClassification(): BelongsTo{
        return $this->belongsTo(CarClassification::class);
    }
}
