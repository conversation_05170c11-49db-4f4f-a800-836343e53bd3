<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class CarModel extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $with = ['carClassification'];

    public function carClassification(): BelongsTo{
        return $this->belongsTo(CarClassification::class);
    }

    public function shuttleCity(): HasOne{
        return $this->hasOne(ShuttleCity::class);
    }
}
