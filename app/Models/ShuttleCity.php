<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShuttleCity extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function carClassification():BelongsTo  {
        return $this->belongsTo(CarClassification::class);
    }

    public function city(): BelongsTo {
        return $this->belongsTo(City::class);
    }

}
