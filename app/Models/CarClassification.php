<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;


class CarClassification extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $table = 'car_classifications';

    public function carModels(): HasMany {
        return $this->hasMany(CarModel::class, 'classification_id');
    }

    public function rentalPrice(): HasOne {
        return $this->hasOne(RentalPrice::class);
    }

    public function bookingPrice(): HasOne {
        return $this->hasOne(BookingPrice::class);
    }
}
 