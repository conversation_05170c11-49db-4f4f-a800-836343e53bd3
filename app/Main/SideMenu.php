<?php

namespace App\Main;

class SideMenu
{
    /**
     * List of side menu items.
     */
    public static function menu(): array
    {
        return [
            // 'dashboard' => [
            //     'icon' => 'home',
            //     'title' => 'Dashboard',
            //     'sub_menu' => [
            //         'dashboard_view' => [
            //             'icon' => 'activity',
            //             'route_name' => 'dashboard_view',
            //             'title' => 'Dashboard'
            //         ],
                  
            //     ]
            // ],
            'home' => [
                'icon' => 'home',
                'title' => 'Home',
                'sub_menu' => [
                    'view_home' => [
                        'icon' => 'activity',
                        'route_name' => 'view_home',
                        'title' => 'Home'
                    ],
                  
                ]
            ],
            'driver' => [
                'icon' => 'car-taxi-front',
                'title' => 'Driver',
                'sub_menu' => [
                    'register_driver' => [
                        'icon' => 'user',
                        'route_name' => 'register_driver',
                        'title' => 'Register Driver'
                    ],
                    'view_drivers' => [
                        'icon' => 'user',
                        'route_name' => 'view_drivers',
                        'title' => 'View Drivers'
                    ],
                    // 'custom_components' => [
                    //     'icon' => 'palette',
                    //     'route_name' => 'custom_components',
                    //     'title' => 'Custom Components'
                    // ],
                ]
            ],
            'pricing' => [
                'icon' => 'dollar-sign',
                'title' => 'Pricing',
                'sub_menu' => [
                    'view_car_models' => [
                        'icon' => 'car',
                        'route_name' => 'view_car_models',
                        'title' => 'Set Price Models'
                    ],
                    'car_rental_pricing' => [
                        'icon' => 'car',
                        'route_name' => 'car_rental_pricing',
                        'title' => 'Car Rentals'
                    ],
                    'cab_rates' => [
                        'icon' => 'car',
                        'route_name' => 'cab_rates',
                        'title' => 'Taxi Rates'
                    ],
                    'driver_rental_rates' => [
                        'icon' => 'car',
                        'route_name' => 'driver_rental_rates',
                        'title' => 'Driver Rental Rates'
                    ],
                    'city_city_shuttle_rates' => [
                        'icon' => 'bus',
                        'route_name' => 'city_city_shuttle_rates',
                        'title' => 'City to City Shuttle'
                    ],
                ]
            ],
            'accidentreports' => [
                'icon' => 'library-big',
                'title' => 'Accident Reports',
                'sub_menu' => [
                    'accident_reports' => [
                        'icon' => 'library',
                        'route_name' => 'accident_reports',
                        'title' => 'View Reports'
                    ],
                ]
            ],
            'cabbookings' => [
                'icon' => 'car-taxi-front',
                'title' => 'Cab Trips',
                'sub_menu' => [
                    'view_cab_trips' => [
                        'icon' => 'briefcase',
                        'route_name' => 'view_cab_trips',
                        'title' => 'View Cab Trips'
                    ],
                ]
            ],
            'shuttlebookings' => [
                'icon' => 'car-taxi-front',
                'title' => 'Shuttle Trips',
                'sub_menu' => [
                    'view_shuttle_trips' => [
                        'icon' => 'briefcase',
                        'route_name' => 'view_shuttle_trips',
                        'title' => 'View Shuttle Trips'
                    ],
                ]
            ],

            'driverbookings' => [
                'icon' => 'contact',
                'title' => 'Hire Driver Bookings',
                'sub_menu' => [
                    'view_hire_driver_bookings' => [
                        'icon' => 'view',
                        'route_name' => 'view_hire_driver_bookings',
                        'title' => 'View Bookings'
                    ],
                ]
            ],

            'carrentalbookings' => [
                'icon' => 'contact',
                'title' => 'Car Rental Bookings',
                'sub_menu' => [
                    'view_car_rental_bookings' => [
                        'icon' => 'view',
                        'route_name' => 'view_car_rental_bookings',
                        'title' => 'View Bookings'
                    ],
                     'car_rental_sales' => [
                        'icon' => 'view',
                        'route_name' => 'car_rental_sales',
                        'title' => 'Manage Sales Personel'
                    ],
                ]
            ],

            'payments' => [
                'icon' => 'circle-dollar-sign',
                'title' => 'Payments',
                'sub_menu' => [
                    'view_payments' => [
                        'icon' => 'circle-dollar-sign',
                        'route_name' => 'view_payments',
                        'title' => 'View Payments'
                    ],
                ]
            ],

            'system_users' => [
                'icon' => 'shield-ellipsis',
                'title' => 'System Users',
                'sub_menu' => [
                    'view_users' => [
                        'icon' => 'user',
                        'route_name' => 'view_users',
                        'title' => 'View Users'
                    ],
                ]
            ],
            // 'e-commerce' => [
            //     'icon' => 'shopping-bag',
            //     'title' => 'E-Commerce',
            //     'sub_menu' => [
            //         'categories' => [
            //             'icon' => 'activity',
            //             'route_name' => 'categories',
            //             'title' => 'Categories'
            //         ],
            //         'add-product' => [
            //             'icon' => 'activity',
            //             'route_name' => 'add-product',
            //             'title' => 'Add Product',
            //         ],
            //         'products' => [
            //             'icon' => 'activity',
            //             'title' => 'Products',
            //             'sub_menu' => [
            //                 'product-list' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'product-list',
            //                     'title' => 'Product List'
            //                 ],
            //                 'product-grid' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'product-grid',
            //                     'title' => 'Product Grid'
            //                 ]
            //             ]
            //         ],
            //         'transactions' => [
            //             'icon' => 'activity',
            //             'title' => 'Transactions',
            //             'sub_menu' => [
            //                 'transaction-list' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'transaction-list',
            //                     'title' => 'Transaction List'
            //                 ],
            //                 'transaction-detail' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'transaction-detail',
            //                     'title' => 'Transaction Detail'
            //                 ]
            //             ]
            //         ],
            //         'sellers' => [
            //             'icon' => 'activity',
            //             'title' => 'Sellers',
            //             'sub_menu' => [
            //                 'seller-list' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'seller-list',
            //                     'title' => 'Seller List'
            //                 ],
            //                 'seller-detail' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'seller-detail',
            //                     'title' => 'Seller Detail'
            //                 ]
            //             ]
            //         ],
            //         'reviews' => [
            //             'icon' => 'activity',
            //             'route_name' => 'reviews',
            //             'title' => 'Reviews'
            //         ],
            //     ]
            // ],
            // 'inbox' => [
            //     'icon' => 'inbox',
            //     'route_name' => 'inbox',
            //     'title' => 'Inbox'
            // ],
            // 'file-manager' => [
            //     'icon' => 'hard-drive',
            //     'route_name' => 'file-manager',
            //     'title' => 'File Manager'
            // ],
            // 'point-of-sale' => [
            //     'icon' => 'credit-card',
            //     'route_name' => 'point-of-sale',
            //     'title' => 'Point of Sale'
            // ],
            // 'chat' => [
            //     'icon' => 'message-square',
            //     'route_name' => 'chat',
            //     'title' => 'Chat'
            // ],
            // 'post' => [
            //     'icon' => 'file-text',
            //     'route_name' => 'post',
            //     'title' => 'Post'
            // ],
            // 'calendar' => [
            //     'icon' => 'calendar',
            //     'route_name' => 'calendar',
            //     'title' => 'Calendar'
            // ],
            // 'divider',
            // 'crud' => [
            //     'icon' => 'edit',
            //     'title' => 'Crud',
            //     'sub_menu' => [
            //         'crud-data-list' => [
            //             'icon' => 'activity',
            //             'route_name' => 'crud-data-list',
            //             'title' => 'Data List'
            //         ],
            //         'crud-form' => [
            //             'icon' => 'activity',
            //             'route_name' => 'crud-form',
            //             'title' => 'Form'
            //         ]
            //     ]
            // ],
            // 'users' => [
            //     'icon' => 'users',
            //     'title' => 'Users',
            //     'sub_menu' => [
            //         'users-layout-1' => [
            //             'icon' => 'activity',
            //             'route_name' => 'users-layout-1',
            //             'title' => 'Layout 1'
            //         ],
            //         'users-layout-2' => [
            //             'icon' => 'activity',
            //             'route_name' => 'users-layout-2',
            //             'title' => 'Layout 2'
            //         ],
            //         'users-layout-3' => [
            //             'icon' => 'activity',
            //             'route_name' => 'users-layout-3',
            //             'title' => 'Layout 3'
            //         ]
            //     ]
            // ],
            // 'profile' => [
            //     'icon' => 'trello',
            //     'title' => 'Profile',
            //     'sub_menu' => [
            //         'profile-overview-1' => [
            //             'icon' => 'activity',
            //             'route_name' => 'profile-overview-1',
            //             'title' => 'Overview 1'
            //         ],
            //         'profile-overview-2' => [
            //             'icon' => 'activity',
            //             'route_name' => 'profile-overview-2',
            //             'title' => 'Overview 2'
            //         ],
            //         'profile-overview-3' => [
            //             'icon' => 'activity',
            //             'route_name' => 'profile-overview-3',
            //             'title' => 'Overview 3'
            //         ]
            //     ]
            // ],
            // 'pages' => [
            //     'icon' => 'layout',
            //     'title' => 'Pages',
            //     'sub_menu' => [
            //         'wizards' => [
            //             'icon' => 'activity',
            //             'title' => 'Wizards',
            //             'sub_menu' => [
            //                 'wizard-layout-1' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'wizard-layout-1',
            //                     'title' => 'Layout 1'
            //                 ],
            //                 'wizard-layout-2' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'wizard-layout-2',
            //                     'title' => 'Layout 2'
            //                 ],
            //                 'wizard-layout-3' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'wizard-layout-3',
            //                     'title' => 'Layout 3'
            //                 ]
            //             ]
            //         ],
            //         'blog' => [
            //             'icon' => 'activity',
            //             'title' => 'Blog',
            //             'sub_menu' => [
            //                 'blog-layout-1' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'blog-layout-1',
            //                     'title' => 'Layout 1'
            //                 ],
            //                 'blog-layout-2' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'blog-layout-2',
            //                     'title' => 'Layout 2'
            //                 ],
            //                 'blog-layout-3' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'blog-layout-3',
            //                     'title' => 'Layout 3'
            //                 ]
            //             ]
            //         ],
            //         'pricing' => [
            //             'icon' => 'activity',
            //             'title' => 'Pricing',
            //             'sub_menu' => [
            //                 'pricing-layout-1' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'pricing-layout-1',
            //                     'title' => 'Layout 1'
            //                 ],
            //                 'pricing-layout-2' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'pricing-layout-2',
            //                     'title' => 'Layout 2'
            //                 ]
            //             ]
            //         ],
            //         'invoice' => [
            //             'icon' => 'activity',
            //             'title' => 'Invoice',
            //             'sub_menu' => [
            //                 'invoice-layout-1' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'invoice-layout-1',
            //                     'title' => 'Layout 1'
            //                 ],
            //                 'invoice-layout-2' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'invoice-layout-2',
            //                     'title' => 'Layout 2'
            //                 ]
            //             ]
            //         ],
            //         'faq' => [
            //             'icon' => 'activity',
            //             'title' => 'FAQ',
            //             'sub_menu' => [
            //                 'faq-layout-1' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'faq-layout-1',
            //                     'title' => 'Layout 1'
            //                 ],
            //                 'faq-layout-2' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'faq-layout-2',
            //                     'title' => 'Layout 2'
            //                 ],
            //                 'faq-layout-3' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'faq-layout-3',
            //                     'title' => 'Layout 3'
            //                 ]
            //             ]
            //         ],
            //         'login' => [
            //             'icon' => 'activity',
            //             'route_name' => 'login',
            //             'title' => 'Login'
            //         ],
            //         'register' => [
            //             'icon' => 'activity',
            //             'route_name' => 'register',
            //             'title' => 'Register'
            //         ],
            //         'error-page' => [
            //             'icon' => 'activity',
            //             'route_name' => 'error-page',
            //             'title' => 'Error Page'
            //         ],
            //         'update-profile' => [
            //             'icon' => 'activity',
            //             'route_name' => 'update-profile',
            //             'title' => 'Update profile'
            //         ],
            //         'change-password' => [
            //             'icon' => 'activity',
            //             'route_name' => 'change-password',
            //             'title' => 'Change Password'
            //         ]
            //     ]
            // ],
            // 'divider',
            // 'components' => [
            //     'icon' => 'inbox',
            //     'title' => 'Components',
            //     'sub_menu' => [
            //         'grid' => [
            //             'icon' => 'activity',
            //             'title' => 'Grid',
            //             'sub_menu' => [
            //                 'regular-table' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'regular-table',
            //                     'title' => 'Regular Table'
            //                 ],
            //                 'tabulator' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'tabulator',
            //                     'title' => 'Tabulator'
            //                 ]
            //             ]
            //         ],
            //         'overlay' => [
            //             'icon' => 'activity',
            //             'title' => 'Overlay',
            //             'sub_menu' => [
            //                 'modal' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'modal',
            //                     'title' => 'Modal'
            //                 ],
            //                 'slide-over' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'slide-over',
            //                     'title' => 'Slide Over'
            //                 ],
            //                 'notification' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'notification',
            //                     'title' => 'Notification'
            //                 ],
            //             ]
            //         ],
            //         'tab' => [
            //             'icon' => 'activity',
            //             'route_name' => 'tab',
            //             'title' => 'Tab'
            //         ],
            //         'accordion' => [
            //             'icon' => 'activity',
            //             'route_name' => 'accordion',
            //             'title' => 'Accordion'
            //         ],
            //         'button' => [
            //             'icon' => 'activity',
            //             'route_name' => 'button',
            //             'title' => 'Button'
            //         ],
            //         'alert' => [
            //             'icon' => 'activity',
            //             'route_name' => 'alert',
            //             'title' => 'Alert'
            //         ],
            //         'progress-bar' => [
            //             'icon' => 'activity',
            //             'route_name' => 'progress-bar',
            //             'title' => 'Progress Bar'
            //         ],
            //         'tooltip' => [
            //             'icon' => 'activity',
            //             'route_name' => 'tooltip',
            //             'title' => 'Tooltip'
            //         ],
            //         'dropdown' => [
            //             'icon' => 'activity',
            //             'route_name' => 'dropdown',
            //             'title' => 'Dropdown'
            //         ],
            //         'typography' => [
            //             'icon' => 'activity',
            //             'route_name' => 'typography',
            //             'title' => 'Typography'
            //         ],
            //         'icon' => [
            //             'icon' => 'activity',
            //             'route_name' => 'icon',
            //             'title' => 'Icon'
            //         ],
            //         'loading-icon' => [
            //             'icon' => 'activity',
            //             'route_name' => 'loading-icon',
            //             'title' => 'Loading Icon'
            //         ]
            //     ]
            // ],
            // 'forms' => [
            //     'icon' => 'sidebar',
            //     'title' => 'Forms',
            //     'sub_menu' => [
            //         'regular-form' => [
            //             'icon' => 'activity',
            //             'route_name' => 'regular-form',
            //             'title' => 'Regular Form'
            //         ],
            //         'datepicker' => [
            //             'icon' => 'activity',
            //             'route_name' => 'datepicker',
            //             'title' => 'Datepicker'
            //         ],
            //         'tom-select' => [
            //             'icon' => 'activity',
            //             'route_name' => 'tom-select',
            //             'title' => 'Tom Select'
            //         ],
            //         'file-upload' => [
            //             'icon' => 'activity',
            //             'route_name' => 'file-upload',
            //             'title' => 'File Upload'
            //         ],
            //         'wysiwyg-editor' => [
            //             'icon' => 'activity',
            //             'title' => 'Wysiwyg Editor',
            //             'sub_menu' => [
            //                 'wysiwyg-editor-classic' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'wysiwyg-editor-classic',
            //                     'title' => 'Classic'
            //                 ],
            //                 'wysiwyg-editor-inline' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'wysiwyg-editor-inline',
            //                     'title' => 'Inline'
            //                 ],
            //                 'wysiwyg-editor-balloon' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'wysiwyg-editor-balloon',
            //                     'title' => 'Balloon'
            //                 ],
            //                 'wysiwyg-editor-balloon-block' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'wysiwyg-editor-balloon-block',
            //                     'title' => 'Balloon Block'
            //                 ],
            //                 'wysiwyg-editor-document' => [
            //                     'icon' => 'zap',
            //                     'route_name' => 'wysiwyg-editor-document',
            //                     'title' => 'Document'
            //                 ],
            //             ]
            //         ],
            //         'validation' => [
            //             'icon' => 'activity',
            //             'route_name' => 'validation',
            //             'title' => 'Validation'
            //         ]
            //     ]
            // ],
            // 'widgets' => [
            //     'icon' => 'hard-drive',
            //     'title' => 'Widgets',
            //     'sub_menu' => [
            //         'chart' => [
            //             'icon' => 'activity',
            //             'route_name' => 'chart',
            //             'title' => 'Chart'
            //         ],
            //         'slider' => [
            //             'icon' => 'activity',
            //             'route_name' => 'slider',
            //             'title' => 'Slider'
            //         ],
            //         'image-zoom' => [
            //             'icon' => 'activity',
            //             'route_name' => 'image-zoom',
            //             'title' => 'Image Zoom'
            //         ]
            //     ]
            // ]
        ];
    }
}