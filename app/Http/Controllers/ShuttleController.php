<?php

namespace App\Http\Controllers;

use App\Models\SupabaseModels\SupabaseTaxiBooking;
use Illuminate\Http\Request;
use App\Models\SupabaseModels\SupabaseShuttleBooking;

class ShuttleController extends Controller
{
    public function show(SupabaseShuttleBooking $trip)
    {
        $tripInfor = SupabaseShuttleBooking::where('id', $trip->id)
            ->first();

        return view('pages.shuttle_bookings.view_shuttle_trip_view', compact('tripInfor'));
    }
}
