<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Driver;
use App\Models\SupabaseModels\SupabaseDriver; // Assuming this is the model for Supabase integration

class DriverController extends Controller
{
    
    public function show(SupabaseDriver $driver)
    {
        $driverData = SupabaseDriver::with(
        'trainings',
        'nextOfKin',
        'gender',
        'driversLicence',
        'bank',)
            ->where('id', $driver->id)
            ->first();

        return view('pages.driver.view-driver-profile', compact('driverData'));
    }

    public function edit(SupabaseDriver $driver)
    {
        $driverData = SupabaseDriver::with(
        'trainings',
        'nextOfKin',
        'gender',
        'driversLicence',
        'bank'
            )
            ->where('id', $driver->id)
            ->first();

        return view('pages.driver.edit_driver', compact('driverData'));
    }
}
