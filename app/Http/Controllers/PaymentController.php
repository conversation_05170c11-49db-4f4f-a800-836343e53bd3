<?php

namespace App\Http\Controllers;

use App\Models\SupabaseModels\SupabasePayment;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    public function show(SupabasePayment $payment)
    {
        $paymentInfo = SupabasePayment::with(['client', 'trip'])
            ->where('id', $payment->id)
            ->first();
        return view('pages.payments.view-payment-details-view', compact('paymentInfo'));
    }
}