<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\ClientDataResource;
use App\Models\Client;
use Illuminate\Http\Request;

class SupabaseAPIController extends Controller
{
    public function saveClientData(Request $request) {

        $data['phonenumber'] = request('phonenumber');
        $data['supabase_id'] = request('supabase_id');

        Client::create($data);

    
        return new ClientDataResource($data);
    }
}
