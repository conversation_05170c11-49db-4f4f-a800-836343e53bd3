<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\CabBookingRatesCollection;
use App\Models\CabRate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PricingController extends Controller
{
    
    public function index(): CabBookingRatesCollection
    {
        $cabPrices = CabRate::all();
        return new CabBookingRatesCollection($cabPrices);

    }
}
