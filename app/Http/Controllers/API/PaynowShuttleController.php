<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Member;
use App\Models\Payment;
use App\Models\Subscription;
use App\Models\SupabaseModels\SupabasePayment;
use App\Models\SupabaseModels\SupabaseShuttleBooking;
use App\Models\SupabaseModels\SupabaseTaxiBooking;
use App\Models\TaxiBooking;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Paynow\Payments\Paynow;
use Symfony\Component\HttpFoundation\Response;

class PaynowShuttleController extends Controller
{
    public function pay(Request $request)
    {
        $validator = Validator::make($request->all(), [
                'reference' => 'required|max:255',
                'amount' => 'required',
                'client_id' => 'required',
                'booking_id' => 'required',
            ]
        );

        if ($validator->fails()) {
            $response = [
                'status' => false,
                'message' => 'There were some problems with your input',
                'data' => $validator->errors()->all()
            ];
            return response($response, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $amount = $request->input('amount');
        $reference = $request->input('reference');
        $client_id = $request->input('client_id');
        $booking_id = $request->input('booking_id');
        $is_cab = $request->input('isCab');


        $refNumber = Str::uuid();

        // $paynow = new Paynow(
        //     '5865',  // Your Paynow integration ID
        //     '*************-4f7c-bbd5-7e12f19cdfc6',  // Your Paynow secret key
        //     'http://**************/api/paynow-return/'.$refNumber,  // Return URL after payment
        //     'http://**************/api/paynow-return/'.$refNumber   // Result URL for the poll request
        // );

        $paynow = new Paynow(
            '19618',  // Your Paynow integration ID
            '04b36278-ca66-4e3b-ae53-8a2f65217823',  // Your Paynow secret key
            'http://**************/api/paynow-return-shuttle/'.$refNumber,  // Return URL after payment
            'http://**************/api/paynow-return-shuttle/'.$refNumber   // Result URL for the poll request
        );


        $payment = $paynow->createPayment($reference, '<EMAIL>');

        // Add the subscription fee to the payment
        $payment->add($request->input('reference'), $amount);

        $response = $paynow->send($payment);

        if ($response->success()) {
            $shuttleBooking = SupabaseShuttleBooking::find($booking_id);

            $tripPayment = new SupabasePayment([
                'client_id' => $client_id,
                'reference' => $reference,
                'amount_invoiced' => $amount,
                'amount_paid' => $amount,
                'paynow_reference' => $refNumber,
                'poll_url' => $response->pollUrl(),
            ]);

            // This will correctly set the payable_type to SupabaseShuttleBooking
            $shuttleBooking->payment()->save($tripPayment);

            return response()->json([
                'status' => true,
                'message' => 'Payment created successfully',
                'data' => [
                    'redirectUrl' => $response->redirectUrl(),
                    'pollUrl' => $response->pollUrl(),
                ]
            ], Response::HTTP_OK);
        } else {
            return response()->json([
                'status' => false,
                'message' => 'Failed to create payment, please try again.',
                'data' => []
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function paynowReturn($referenceNumber) {
        // $paynow = new Paynow(
        //     '5865',
        //     '*************-4f7c-bbd5-7e12f19cdfc6',
        //     'http://**************/api/paynow-return/'.$referenceNumber,
        //     // The return url can be set at later stages. You might want to do this if you want to pass data to the return url (like the reference of the transaction)
        //     'http://**************/api/paynow-return/'.$referenceNumber
        // );

        $paynow = new Paynow(
            '19618',  // Your Paynow integration ID
            '04b36278-ca66-4e3b-ae53-8a2f65217823',  // Your Paynow secret key
            'http://**************/api/paynow-return-shuttle/'.$referenceNumber,  // Return URL after payment
            'http://**************/api/paynow-return-shuttle/'.$referenceNumber   // Result URL for the poll request
        );

        $payment = SupabasePayment::where('paynow_reference', $referenceNumber)->first();
      

        $response = $paynow->pollTransaction($payment->poll_url);

        if ($response->paid()) {
            $payment->update([
                'status' => $response->status(),
                'amount_paid' => $response->amount(),
                'payment_method_reference' => $response->paynowReference()
            ]);


            $shuttleBookingPaid = SupabaseShuttleBooking::where('id', $payment->payable_id)->first();
            $shuttleBookingPaid->update([
                'payment_id' => $payment->id
            ]);

         $finalPayment = SupabasePayment::where('paynow_reference', $referenceNumber)->first();

        } else {
            return view('paynow.paynow_error');
        }

        return view('paynow.paynow_confirmation', compact('finalPayment'));
    }

    public function pollPaymentStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
                'poll_url' => 'required|max:255',
            ]
        );

        if ($validator->fails()) {
            $response = [
                'status' => false,
                'message' => 'There were some problems with your input',
                'data' => $validator->errors()->all()
            ];
            return response($response, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $pollUrl = $request->input('poll_url');

        // Initialize Paynow
        $paynow = new Paynow(
            '5865',
            '*************-4f7c-bbd5-7e12f19cdfc6',
            route('paynow.return'),
            route('paynow.return')
        );

        // Poll the transaction
        $response = $paynow->pollTransaction($pollUrl);

        if ($response->paid()) {
            // Update payment and subscription status
            $payment = Payment::where('poll_url', $pollUrl)->first();

            if ($payment) {
                $payment->update([
                    'status' => $response->status(),
                ]);

                return response()->json([
                    'status' => true,
                    'message' => 'Payment successful',
                    'data' => []
                ], Response::HTTP_OK);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Payment record not found.',
                    'data' => []
                ], Response::HTTP_NOT_FOUND);
            }
        } else {
            return response()->json([
                'status' => false,
                'message' => 'Payment not completed',
                'data' => []
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function getPayments($client_id){
        $payments = SupabasePayment::with( 'payable.tripStatus')
            ->where('client_id', $client_id)
            ->where('status', 'paid')
            ->latest()
            ->get();

        return response()->json([
            'data' => $payments
        ]);
    }

  
}
