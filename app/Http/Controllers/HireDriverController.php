<?php

namespace App\Http\Controllers;

use App\Models\SupabaseModels\SupabaseDriverBooking;
use Illuminate\Http\Request;

class HireDriverController extends Controller
{
    public function show(SupabaseDriverBooking $book)
    {
        $hireInfor = SupabaseDriverBooking::where('id', $book->id)
            ->first();

        return view('pages.hire-driver.view-hire-driver-details-view', compact('hireInfor'));
    }
}
