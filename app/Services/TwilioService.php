<?php

namespace App\Services;

use Twilio\Rest\Client;

class TwilioService
{
    protected $client;
    protected $fromNumber;

    public function __construct()
    {
        // Initialize Twilio client with credentials from .env
        $this->client = new Client(
            config('services.twilio.sid'),
            config('services.twilio.auth_token')
        );
        $this->fromNumber = config('services.twilio.phone_number');
    }

    /**
     * Send SMS message
     *
     * @param string $to Recipient phone number
     * @param string $message Message content
     * @return \Twilio\Rest\Api\V2010\Account\MessageInstance
     */
    public function sendSMS($to, $message)
    {
        return $this->client->messages->create($to, [
            'from' => $this->fromNumber,
            'body' => $message
        ]);
    }

}