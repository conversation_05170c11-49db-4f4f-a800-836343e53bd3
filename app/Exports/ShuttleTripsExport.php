<?php

namespace App\Exports;

use App\Models\SupabaseModels\SupabaseShuttleBooking;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ShuttleTripsExport implements FromCollection, WithHeadings, WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return SupabaseShuttleBooking::with(['client', 'tripStatus'])
            ->latest()
            ->get();
    }

    public function headings(): array
    {
        return [
            'CUSTOMER NAME',
            'PHONE NUMBER',
            'PICKUP ADDRESS',
            'DESTINATION ADDRESS',
            'TRIP DISTANCE',
            'AMOUNT',
            'PAYMENT STATUS',
            'TRIP STATUS',
            'BOOKING DATE'
        ];
    }

    public function map($trip): array
    {
        return [
            optional($trip->client)->name ?? 'N/A',
            optional($trip->client)->phonenumber ?? 'N/A',
            $trip->pickup_address,
            $trip->destination_address,
            $trip->trip_total_distance,
            $trip->trip_cost,
            $trip->payment_status,
            optional($trip->tripStatus)->status ?? 'N/A',
            convertIsoDateFormat($trip->created_at)
        ];
    }
}