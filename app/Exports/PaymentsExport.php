<?php

namespace App\Exports;

use App\Models\SupabaseModels\SupabasePayment;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class PaymentsExport implements FromCollection, WithHeadings, WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $qry =  SupabasePayment::with('payable')
        ->where('status', 'paid')
        ->latest()
        ->get();

        return $qry;
    }

    public function headings(): array
    {
        return ['CUSTOMER NAME', 'REF_NUMBER', 'DATE', 'TYPE', 'STATUS', 'TRIP_DISTANCE', 'AMOUNT', 'PICKUP ADDRESS', 'DESTINATION ADDRESS'];
}

    public function map($payment): array
    {
        return [
            optional(optional($payment->payable)->client)->name ?? 'N/A',
            $payment->payment_method_reference,
            convertIsoDateFormat($payment->updated_at),
            $payment->reference,
            $payment->status,
            optional($payment->payable)->trip_total_distance,
            $payment->amount_paid,
            optional($payment->payable)->pickup_address,
            optional($payment->payable)->destination_address,  
        ];
            
    }
}
