<?php

namespace App\Exports;

use App\Models\SupabaseModels\SupabaseDriverBooking;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class DriverBookingsExport implements FromCollection, WithHeadings, WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return SupabaseDriverBooking::with(['hireDriverRate', 'driver', 'client', 'licence', 'hireDriverStatus'])
            ->latest()
            ->get();
    }

    public function headings(): array
    {
        return [
            'CUSTOMER NAME',
            'BOOKING TYPE',
            'COST',
            'BOOKING DATE',
            'REQUIRED CLASS',
            'BOOKING STATUS',
        ];
    }

    public function map($booking): array
    {
        return [
            optional($booking->client)->name ?? 'N/A',
            optional($booking->hireDriverRate->hireDriverType)->hire_type ?? 'N/A',
            optional($booking->hireDriverRate)->amount ?? 'N/A',
            $booking->date_of_hire,
            optional($booking->licence)->licence_class ?? 'N/A',
            optional($booking->hireDriverStatus)->status ?? 'N/A',
           
        ];
    }
}