<?php

namespace App\Livewire\CarRental;

use App\Models\SupabaseModels\SupabaseCarRental;
use Livewire\Component;
use Livewire\WithPagination;

class ViewCarRentalsComponent extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 5;

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function render()
    {
        $bookings = SupabaseCarRental::whereHas('client', function($query) {
            $query->where('name', 'like', '%' . $this->search . '%');
        })
        ->orderBy('id', 'desc')
        ->paginate($this->perPage);

        return view('livewire.car-rental.view-car-rentals-component', compact('bookings'));
    }

    public function delete(SupabaseCarRental $booking)
    {
        $booking->delete();
        session()->flash('booking_deleted', 'Booking was successfully deleted');
    }
}
