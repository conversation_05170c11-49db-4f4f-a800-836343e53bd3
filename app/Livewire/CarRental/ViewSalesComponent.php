<?php

namespace App\Livewire\CarRental;

use Livewire\Component;
use App\Models\SupabaseModels\SupabaseSale;
use Livewire\Attributes\Rule;

class ViewSalesComponent extends Component
{
    #[Rule('required|max:255')]
    public $fullname;

    #[Rule('required|max:20')]
    public $mobile_number;

    // For editing
    public $editId = null;
    public $editFullname = '';
    public $editPhone = '';

    public function edit($id)
    {
        $sale = SupabaseSale::findOrFail($id);
        $this->editId = $id;
        $this->editFullname = $sale->fullname;
        $this->editPhone = $sale->phone;
    }

    public function update($id)
    {
        $this->validate([
            'editFullname' => 'required|max:255',
            'editPhone' => 'required|max:20',
        ]);

        SupabaseSale::where('id', $id)->update([
            'fullname' => $this->editFullname,
            'phone' => $this->editPhone,
        ]);

        $this->editId = null;
        $this->editFullname = '';
        $this->editPhone = '';
        session()->flash('sale_created', 'Sale record updated successfully!');
    }

    public function cancelEdit()
    {
        $this->editId = null;
        $this->editFullname = '';
        $this->editPhone = '';
    }

    public function delete($id)
    {
        SupabaseSale::where('id', $id)->delete();
        session()->flash('sale_created', 'Sale record deleted successfully!');
    }

    public function saveSale()
    {
        $this->validate([
            'fullname' => 'required|max:255',
            'mobile_number' => 'required|max:20',
        ]);

        SupabaseSale::create([
            'fullname' => $this->fullname,
            'phone' => $this->mobile_number,
        ]);

        $this->fullname = '';
        $this->mobile_number = '';
        session()->flash('sale_created', 'Sale record created successfully!');
    }

    public function getAllRecords()
    {
        return SupabaseSale::all();
    }

    public function render()
    {
        $sales = SupabaseSale::latest()->get();
        return view('livewire.car-rental.view-sales-component', [
            'sales' => $sales,
        ]);
    }
}
