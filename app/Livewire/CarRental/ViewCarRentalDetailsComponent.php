<?php

namespace App\Livewire\CarRental;

use App\Models\SupabaseModels\SupabaseCarRental;
use Livewire\Component;

class ViewCarRentalDetailsComponent extends Component
{
    public SupabaseCarRental $book;

    public function render()
    {
        return view('livewire.car-rental.view-car-rental-details-component');
    }

    public function bookCar($id){
        $carRentalDetails = SupabaseCarRental::find($id);

        $carRentalDetails->rental_status_id = 3;


        if( $carRentalDetails->save()){
            $this->dispatch('carHired');
        }
    }

    public function cancel()
    {
        return redirect()->back();
    }

    public function mount($book)
    {
        $this->book = $book;
        return view('livewire.car-rental.view-car-rental-details-component');
    }
}
