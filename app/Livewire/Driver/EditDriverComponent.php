<?php

namespace App\Livewire\Driver;

use App\Models\NextOfKin;
use Livewire\Component;

use App\Models\SupabaseModels\SupabaseBank;
use App\Models\SupabaseModels\SupabaseDriver;
use Livewire\WithFileUploads;
use Spatie\LivewireFilepond\WithFilePond;
use App\Models\SupabaseModels\SupabaseDriversLicence;
use App\Models\SupabaseModels\SupabaseGender;
use App\Models\SupabaseModels\SupabaseTrainingType;
use App\Models\SupabaseModels\SupabaseTraining;
use App\Models\SupabaseModels\SupabaseNextOfKin;

class EditDriverComponent extends Component
{

    public SupabaseDriver $driver;

    use withFileUploads;
    use WithFilePond;

    public $file;
    public $currentStep = 1;
    public $totalSteps = 6;
    public $driverFirstname;
    public $driverLastname;
    public $driverDOB;
    public $selectedGenderId;
    public $driverMobileNumber;
    public $driverSecondMobileNumber;
    public $driverEmail;
    public $driverAddress;
    public $nextOfKinFullname;
    public $nextOfKinMobileNumber;
    public $nextOfKinAddress;
    public $drivingExperience;
    public $driversLicenceNumber;
    public $licenceIssueDate;
    public $selectedClassId;
    public $licenceFile;
    public $defencelicenceFile;
    public $defencelicenceExpiryDate;
    public $medicalTestsIssueDate;
    public $medicalTestFile;
    public $policeClearanceIssueDate;
    public $policeClearanceFile;
    public $impalaCeritificationDate;
    public $impalaCertificateFile;
    public $proofOfResidenceFile;
    public $selectedBankId;
    public $accountNumber;
    public $nationalIdNumber;
    public $nationalIdFile;
    public $passportNumber;
    public $passportFile;
    public $firstAidCertificateFile;
    public $cmedVipCertificateFile;
    public $internationalDrivingLicenceFile;
    public $ecocashNumber;
    public $innBucksNumber;
    public $isImpalaInductionTrainingChecked;
    public $impalaInductionTrainingDate;
    public $impalaInductionTrainingId;
    public $isImpalaDriversCodeOfConductChecked;
    public $impalaDriversCodeOfConductDate;
    public $impalaDriversCodeOfConductId;
    public $isImpalaSafeDrivingChecked;
    public $impalaSafeDrivingDate;
    public $impalaSafeDrivingId;
    public $isImpalaChaufferDriversCourseChecked;
    public $impalaChaufferDriversCourseDate;
    public $impalaChaufferDriversCourseId;
    public $isImpalaCMEDVipDrivingCourseChecked;
    public $impalaCMEDVipDrivingCourseDate;
    public $impalaCMEDVipDrivingCourseId;
    public $isImpalaDriversInHouseTrainingChecked;
    public $impalaDriversInHouseTrainingDate;
    public $isImpalaDriversInHouseTrainingId;
    public $driverProfilePhotoFile;


    public $impalaInductionTrainingDateVal = null;
    public $impalaDriversCodeOfConductDateVal = null;
    public $impalaSafeDrivingDateVal = null;
    public $impalaChaufferDriversCourseDateVal = null;
    public $impalaCMEDVipDrivingCourseDateVal = null;
    public $impalaDriversInHouseTrainingDateVal = null;

    public function mount($driver) {

        $this->driverFirstname = $driver->driver_firstname;
        $this->driverLastname =  $driver->driver_lastname;
        $this->driverDOB = convertToDefaultDateFormat($driver->driver_dob);
        $this->selectedGenderId = $driver->gender_id;
        $this->driverMobileNumber = $driver->driver_mobile;
        $this->driverSecondMobileNumber = $driver->driver_second_mobile;
        $this->driverEmail = $driver->driver_email;
        $this->driverAddress = $driver->driver_address;
        $this->nextOfKinFullname = $driver->nextOfKin->fullname;
        $this->nextOfKinMobileNumber = $driver->nextOfKin->mobile_number;
        $this->nextOfKinAddress = $driver->nextOfKin->address;
        $this->drivingExperience = $driver->driving_experience;
        $this->driversLicenceNumber = $driver->drivers_licence;
        $this->licenceIssueDate = convertToDefaultDateFormat($driver->licence_issue_date);
        $this->selectedClassId = $driver->drivers_licence_id;
        $this->defencelicenceExpiryDate = convertToDefaultDateFormat($driver->defence_licence_expiry_date);
        $this->policeClearanceIssueDate = convertToDefaultDateFormat($driver->police_clearance_issue_date);
        $this->selectedBankId = $driver->bank_id;
        $this->accountNumber = $driver->account_number;
        $this->nationalIdNumber = $driver-> national_id;
        $this->passportNumber = $driver->passport_number;
        $this->ecocashNumber = $driver->ecocash_number;
        $this->innBucksNumber = $driver->innbucks_number;
        $this->medicalTestsIssueDate = convertToDefaultDateFormat($driver->medical_tests_issue_date);

        $this->driver = $driver;    

        return view('livewire.driver.edit-driver-component');
    }

    public function render() {

        $licenceClasses = SupabaseDriversLicence::pluck('licence_class', 'id');
        $selectedLicenceClass = 0;
        $selectedCertification = 0;
        $banks = SupabaseBank::pluck('bank', 'id');
        $selectedBank = 0;
        $genders = SupabaseGender::pluck('gender', 'id');
        $selectedGenderId = 0;
        $trainingTypes = SupabaseTrainingType::all();

        $trainings = SupabaseTraining::where('driver_id', $this->driver->id)
        ->get();

        foreach ($trainings as $training) {

            switch ($training->training_type_id) {
                case $training->training_type_id == 1:
                        $this->isImpalaInductionTrainingChecked = true;
                        $this->impalaInductionTrainingDate = convertToDefaultDateFormat($training->training_date);
                        $this->impalaInductionTrainingId = $training->training_type_id;
                    break;

                case $training->training_type_id == 2:
                        $this->isImpalaDriversCodeOfConductChecked = true;
                        $this->impalaDriversCodeOfConductDate = convertToDefaultDateFormat($training->training_date);
                        $this->impalaDriversCodeOfConductId = $training->training_type_id;
                    break;
                
                    case $training->training_type_id == 3:
                        $this->isImpalaSafeDrivingChecked = true;
                        $this->impalaSafeDrivingDate = convertToDefaultDateFormat($training->training_date);
                        $this->impalaSafeDrivingId = $training->training_type_id;
                    break;

                    case $training->training_type_id == 4:
                        $this->isImpalaChaufferDriversCourseChecked = true;
                        $this->impalaChaufferDriversCourseDate = convertToDefaultDateFormat($training->training_date);
                        $this->impalaChaufferDriversCourseId = $training->training_type_id;
                    break;

                    case $training->training_type_id == 5:
                        $this->isImpalaCMEDVipDrivingCourseChecked = true;
                        $this->impalaCMEDVipDrivingCourseDate = convertToDefaultDateFormat($training->training_date);
                        $this->impalaCMEDVipDrivingCourseId = $training->training_type_id;
                    break;

                    case $training->training_type_id == 6:
                        $this->isImpalaDriversInHouseTrainingChecked = true;
                        $this->impalaDriversInHouseTrainingDate = convertToDefaultDateFormat($training->training_date);
                        $this->isImpalaDriversInHouseTrainingId = $training->training_type_id;
                    break;
                }
        }

        return view('livewire.driver.edit-driver-component', [
            'selectedLicenceClass' => $selectedLicenceClass,
            'licenceClasses' => $licenceClasses,
            'selectedCertification' => $selectedCertification,
            'selectedBank' => $selectedBank,
            'banks' => $banks,
            'genders' => $genders,
            'selectedGenderId' => $selectedGenderId,
            'trainingTypes' => $trainingTypes,
            'trainings' => $trainings,
        ]);
    }


    public function saveDriverDetailsChanges(){
        $studentDetailsChanges = [];

        

        if($this->driverFirstname != '' || $this->driverFirstname !== null){
            $studentDetailsChanges['driver_firstname'] = $this->driverFirstname;
         } 
         if ($this->driverLastname != '' || $this->driverLastname !== null){
            $studentDetailsChanges['driver_lastname'] = $this->driverLastname;
         } 
         
         if ($this->driverDOB != '' || $this->driverDOB !== null){ 
            $studentDetailsChanges['driver_dob'] = convertDateFormat($this->driverDOB);
         }

         if($this->selectedGenderId != '' || $this->selectedGenderId !== null) {
            $studentDetailsChanges['gender_id'] = $this->selectedGenderId;
         }

         if($this->driverMobileNumber != '' || $this->driverMobileNumber !== null) {
            $studentDetailsChanges['driver_mobile'] = $this->driverMobileNumber;
         }

         if($this->driverSecondMobileNumber != '' || $this->driverSecondMobileNumber !== null) {
            $studentDetailsChanges['driver_second_mobile'] = $this->driverSecondMobileNumber;
         }

         if($this->driverEmail != '' || $this->driverEmail !== null) {
            $studentDetailsChanges['driver_email'] = $this->driverEmail;
         }

         if($this->driverAddress != '' || $this->driverAddress !== null) {
            $studentDetailsChanges['driver_address'] = $this->driverAddress;
         }

         if($this->nationalIdNumber != '' || $this->nationalIdNumber !== null) {
            $studentDetailsChanges['national_id'] = $this->nationalIdNumber;
         }


         if($this->passportNumber != '' || $this->passportNumber !== null) {
            $studentDetailsChanges['passport_number'] = $this->passportNumber;
         }

         if($this->nationalIdFile != '' || $this->nationalIdFile !== null) {
            $nationalIdPhotoFilePath = $this->nationalIdFile->store('uploads', 'public');
            $studentDetailsChanges['national_id_file_path'] = $nationalIdPhotoFilePath;
         }

         if($this->passportFile != '' || $this->passportFile !== null) {
            $passportPhotoFilePath = $this->passportFile->store('uploads', 'public');
            $studentDetailsChanges['passport_file_path'] = $passportPhotoFilePath;
         }

         if($this->driverProfilePhotoFile != '' || $this->driverProfilePhotoFile !== null) {
            $driverPhotoFilePath = $this->driverProfilePhotoFile->store('uploads', 'public');
            $studentDetailsChanges['profile_photo_file'] = $driverPhotoFilePath;
         }
         
         SupabaseDriver::where('id', $this->driver->id)->update($studentDetailsChanges);

         session()->flash('driver_changes_saved', 'Driver details updated successfully!');


    }

    public function saveNextOfKinChanges(){
        $nextOfKinChanges = [];

        if($this->nextOfKinFullname != '' || $this->nextOfKinFullname !== null) {
            $nextOfKinChanges['fullname'] = $this->nextOfKinFullname;
        }

        if($this->nextOfKinMobileNumber != '' || $this->nextOfKinMobileNumber !== null) {
            $nextOfKinChanges['mobile_number'] = $this->nextOfKinMobileNumber;
        }


        if($this->nextOfKinAddress != '' || $this->nextOfKinAddress !== null) {
            $nextOfKinChanges['address'] = $this->nextOfKinAddress;
        }
        
        SupabaseNextOfKin::where('driver_id', $this->driver->id)->update($nextOfKinChanges);

        session()->flash('nextofkin_changes_saved', 'Next of kin details updated successfully!');
    }
    public function saveLicensingChanges(){
        $licensingChanges = [];
        if($this->drivingExperience != '' || $this->drivingExperience !== null) {
            $licensingChanges['driving_experience'] = $this->drivingExperience;
        }

        if($this->driversLicenceNumber != '' || $this->driversLicenceNumber !== null) {
            $licensingChanges['drivers_licence'] = $this->driversLicenceNumber;
        }

        if($this->licenceIssueDate != '' || $this->licenceIssueDate !== null) {
            $licensingChanges['licence_issue_date'] = convertDateFormat($this->licenceIssueDate);
        }

        if($this->defencelicenceExpiryDate != '' || $this->defencelicenceExpiryDate !== null) {
            $licensingChanges['defence_licence_expiry_date'] = convertDateFormat($this->defencelicenceExpiryDate);
        }

        if($this->selectedClassId != '' || $this->selectedClassId !== null) {
            $licensingChanges['drivers_licence_id'] = $this->selectedClassId;
        }

        if($this->selectedClassId != '' || $this->selectedClassId !== null) {
            $licensingChanges['drivers_licence_id'] = $this->selectedClassId;
        }

        if($this->licenceFile != '' || $this->licenceFile !== null) {
            $licenceFilePath = $this->licenceFile->store('uploads', 'public');
            $licensingChanges['licence_file_path'] = $licenceFilePath;
        }

        if($this->defencelicenceFile != '' || $this->defencelicenceFile !== null) {
            $defenceLicenceFilePath = $this->defencelicenceFile->store('uploads', 'public');
            $licensingChanges['defence_licence_file_path'] = $defenceLicenceFilePath;
         }

         if($this->internationalDrivingLicenceFile != '' || $this->internationalDrivingLicenceFile !== null) {
            $internationalDrivingLicenceFileFilePath = $this->internationalDrivingLicenceFile->store('uploads', 'public');
            $licensingChanges['idl_licence_path'] = $internationalDrivingLicenceFileFilePath;
         }

         if($this->firstAidCertificateFile != '' || $this->firstAidCertificateFile !== null) {
            $firstAidCertificateFileFilePath = $this->firstAidCertificateFile->store('uploads', 'public');
            $licensingChanges['first_aid_certificate_file_path'] = $firstAidCertificateFileFilePath;
         }

         SupabaseDriver::where('id', $this->driver->id)->update($licensingChanges);

         session()->flash('licence_changes_saved', 'Licensing details updated successfully!');
    }

    // public function saveTrainingDetails(){
    //     dd($this->impalaInductionTrainingDate);
    // }
    public function saveMedicalClearanceChanges(){
        $medicalClearanceChanges = [];

        if($this->medicalTestsIssueDate != '' || $this->medicalTestsIssueDate !== null){
            $medicalClearanceChanges['medical_tests_issue_date'] = convertDateFormat($this->medicalTestsIssueDate);
        }

        if($this->policeClearanceIssueDate != '' || $this->policeClearanceIssueDate !== null){
            $medicalClearanceChanges['police_clearance_issue_date'] = convertDateFormat($this->policeClearanceIssueDate);
        }

        if($this->medicalTestFile != '' || $this->medicalTestFile !== null) {
            $medicalTestFileFilePath = $this->medicalTestFile->store('uploads', 'public');
            $medicalClearanceChanges['medical_test_file_path'] = $medicalTestFileFilePath;
         }

         if($this->policeClearanceFile != '' || $this->policeClearanceFile !== null) {
            $policeClearanceFileFilePath = $this->policeClearanceFile->store('uploads', 'public');
            $medicalClearanceChanges['police_clearance_file_path'] = $policeClearanceFileFilePath;
         }

         if($this->proofOfResidenceFile != '' || $this->proofOfResidenceFile !== null) {
            $proofOfResidenceFilePath = $this->proofOfResidenceFile->store('uploads', 'public');
            $medicalClearanceChanges['police_clearance_file_path'] = $proofOfResidenceFilePath;
         }


         SupabaseDriver::where('id', $this->driver->id)->update($medicalClearanceChanges);

         session()->flash('medical_clearance_changes_saved', 'Licensing details updated successfully!');

        
    }
    public function saveBankingDetails(){

       
        $bankingDetailsChanges = [];

        
        if($this->selectedBankId != '' || $this->selectedBankId !== null){
            $bankingDetailsChanges['bank_id'] = $this->selectedBankId;
        }

        if($this->accountNumber != '' || $this->accountNumber !== null){
            $bankingDetailsChanges['account_number'] = $this->accountNumber;
        }

        if($this->ecocashNumber != '' || $this->ecocashNumber !== null){
            $bankingDetailsChanges['ecocash_number'] = $this->ecocashNumber;
        }

        if($this->innBucksNumber != '' || $this->innBucksNumber !== null){
            $bankingDetailsChanges['innbucks_number'] = $this->innBucksNumber;
        }

        SupabaseDriver::where('id', $this->driver->id)->update($bankingDetailsChanges);

        session()->flash('banking_changes_saved', 'Banking details updated successfully!');

    }

    public function toggleTraining($value, $isChecked): void
    {

        switch ($value) {
            case 1:
                $this->isImpalaInductionTrainingChecked = $isChecked;
                $isChecked ? $this->impalaInductionTrainingId = $value : $this->impalaInductionTrainingId = null;
                break;
            case 2:
                $this->isImpalaDriversCodeOfConductChecked = $isChecked;
                $isChecked ? $this->impalaDriversCodeOfConductId = $value : $this->impalaDriversCodeOfConductId = null;
                break;
            case 3:
                $this->isImpalaSafeDrivingChecked = $isChecked;
                $isChecked ? $this->impalaSafeDrivingId = $value : $this->impalaSafeDrivingId = null;
                break;
            case 4:
                $this->isImpalaChaufferDriversCourseChecked = $isChecked;
                $isChecked ? $this->impalaChaufferDriversCourseId = $value : $this->impalaChaufferDriversCourseId = null;
                break;
            case 5:
                $this->isImpalaCMEDVipDrivingCourseChecked = $isChecked;
                $isChecked ? $this->impalaCMEDVipDrivingCourseId = $value : $this->impalaCMEDVipDrivingCourseId = null;
                break;
            case 6:
                $this->isImpalaDriversInHouseTrainingChecked = $isChecked;
                $isChecked ? $this->isImpalaDriversInHouseTrainingId = $value : $this->isImpalaDriversInHouseTrainingId = null;
                break;
        }
    }

    public $show_driver_details = true;
    public $show_next_of_kin_details = false;
    public $show_licensing_details = false;
    public $show_impala_training_details = false;
    public $show_medical_clearance_details = false;
    public $show_banking_details = false;

    public function show_driver_details_panel(){

        $this->show_driver_details = true;
        $this->show_next_of_kin_details = false;
        $this->show_licensing_details = false;
        $this->show_impala_training_details = false;
        $this->show_medical_clearance_details = false;
        $this->show_banking_details = false;
    }

    public function show_nextofkin_panel(){

        $this->show_driver_details = false;
        $this->show_next_of_kin_details = true;
        $this->show_licensing_details = false;
        $this->show_impala_training_details = false;
        $this->show_medical_clearance_details = false;
        $this->show_banking_details = false;
    }

    public function show_licensing_panel(){

        $this->show_driver_details = false;
        $this->show_next_of_kin_details = false;
        $this->show_licensing_details = true;
        $this->show_impala_training_details = false;
        $this->show_medical_clearance_details = false;
        $this->show_banking_details = false;
    }

    public function show_training_panel(){

        $this->show_driver_details = false;
        $this->show_next_of_kin_details = false;
        $this->show_licensing_details = false;
        $this->show_impala_training_details = true;
        $this->show_medical_clearance_details = false;
        $this->show_banking_details = false;
    }

    public function show_medical_clearance_panel(){

        $this->show_driver_details = false;
        $this->show_next_of_kin_details = false;
        $this->show_licensing_details = false;
        $this->show_impala_training_details = false;
        $this->show_medical_clearance_details = true;
        $this->show_banking_details = false;
    }

    public function show_banking_panel(){

        $this->show_driver_details = false;
        $this->show_next_of_kin_details = false;
        $this->show_licensing_details = false;
        $this->show_impala_training_details = false;
        $this->show_medical_clearance_details = false;
        $this->show_banking_details = true;
    }
}
