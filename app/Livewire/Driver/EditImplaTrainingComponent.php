<?php

namespace App\Livewire\Driver;

use Livewire\Component;
use App\Models\SupabaseModels\SupabaseTrainingType;
use App\Models\SupabaseModels\SupabaseTraining;
use App\Models\SupabaseModels\SupabaseDriver;
use Livewire\WithFileUploads;
use Spatie\LivewireFilepond\WithFilePond;


class EditImplaTrainingComponent extends Component
{

    public SupabaseDriver $driver;

    use withFileUploads;
    use WithFilePond;
   
    public $isImpalaInductionTrainingChecked;
    public $impalaInductionTrainingDate;
    public $impalaInductionTrainingId;
    public $isImpalaDriversCodeOfConductChecked;
    public $impalaDriversCodeOfConductDate;
    public $impalaDriversCodeOfConductId;
    public $isImpalaSafeDrivingChecked;
    public $impalaSafeDrivingDate;
    public $impalaSafeDrivingId;
    public $isImpalaChaufferDriversCourseChecked;
    public $impalaChaufferDriversCourseDate;
    public $impalaChaufferDriversCourseId;
    public $isImpalaCMEDVipDrivingCourseChecked;
    public $impalaCMEDVipDrivingCourseDate;
    public $impalaCMEDVipDrivingCourseId;
    public $isImpalaDriversInHouseTrainingChecked;
    public $impalaDriversInHouseTrainingDate;
    public $isImpalaDriversInHouseTrainingId;
    public $driverProfilePhotoFile;
    public $impalaInductionTrainingDateVal = null;
    public $impalaDriversCodeOfConductDateVal = null;
    public $impalaSafeDrivingDateVal = null;
    public $impalaChaufferDriversCourseDateVal = null;
    public $impalaCMEDVipDrivingCourseDateVal = null;
    public $impalaDriversInHouseTrainingDateVal = null;

    public $medicalTestsIssueDate;
    public $policeClearanceIssueDate;
    public $medicalTestFile;
    public $policeClearanceFile;
    public $proofOfResidenceFile;


    public $id =  0;

    public $trainings;


    public function mount($driver, $trainings){
        $this->driver = $driver;

        $this->id = $driver->id;

        $this->trainings = $trainings;
        $this->medicalTestsIssueDate = convertToDefaultDateFormat($driver->medical_tests_issue_date);
        $this->policeClearanceIssueDate = convertToDefaultDateFormat($driver->police_clearance_issue_date);

        foreach ($trainings as $training) {

            switch ($training->training_type_id) {
                case $training->training_type_id == 1:
                        $this->impalaInductionTrainingDate = convertToDefaultDateFormat($training->training_date);
                        $this->impalaInductionTrainingId = $training->training_type_id;
                    break;

                case $training->training_type_id == 2:
                        $this->impalaDriversCodeOfConductDate = convertToDefaultDateFormat($training->training_date);
                        $this->impalaDriversCodeOfConductId = $training->training_type_id;
                    break;
                
                    case $training->training_type_id == 3:
                        $this->impalaSafeDrivingDate = convertToDefaultDateFormat($training->training_date);
                        $this->impalaSafeDrivingId = $training->training_type_id;
                    break;

                    case $training->training_type_id == 4:
                        $this->impalaChaufferDriversCourseDate = convertToDefaultDateFormat($training->training_date);
                        $this->impalaChaufferDriversCourseId = $training->training_type_id;
                    break;

                    case $training->training_type_id == 5:
                        $this->impalaCMEDVipDrivingCourseDate = convertToDefaultDateFormat($training->training_date);
                        $this->impalaCMEDVipDrivingCourseId = $training->training_type_id;
                    break;

                    case $training->training_type_id == 6:
                        $this->impalaDriversInHouseTrainingDate = convertToDefaultDateFormat($training->training_date);
                        $this->isImpalaDriversInHouseTrainingId = $training->training_type_id;
                    break;
                }
            }
      
        return view('livewire.driver.view-driver-profile-component');
    }


    public function render()
    {
        $trainingTypes = SupabaseTrainingType::all();
        return view('livewire.driver.edit-impla-training-component', 
        ['trainings' => $this->trainings,
        'trainingTypes' => $trainingTypes,
        ]);
    }


    public function toggleTraining($value, $isChecked): void
    {

        switch ($value) {
            case 1:
                $this->isImpalaInductionTrainingChecked = $isChecked;
                $isChecked ? $this->impalaInductionTrainingId = $value : $this->impalaInductionTrainingId = null;
                break;
            case 2:
                $this->isImpalaDriversCodeOfConductChecked = $isChecked;
                $isChecked ? $this->impalaDriversCodeOfConductId = $value : $this->impalaDriversCodeOfConductId = null;
                break;
            case 3:
                $this->isImpalaSafeDrivingChecked = $isChecked;
                $isChecked ? $this->impalaSafeDrivingId = $value : $this->impalaSafeDrivingId = null;
                break;
            case 4:
                $this->isImpalaChaufferDriversCourseChecked = $isChecked;
                $isChecked ? $this->impalaChaufferDriversCourseId = $value : $this->impalaChaufferDriversCourseId = null;
                break;
            case 5:
                $this->isImpalaCMEDVipDrivingCourseChecked = $isChecked;
                $isChecked ? $this->impalaCMEDVipDrivingCourseId = $value : $this->impalaCMEDVipDrivingCourseId = null;
                break;
            case 6:
                $this->isImpalaDriversInHouseTrainingChecked = $isChecked;
                $isChecked ? $this->isImpalaDriversInHouseTrainingId = $value : $this->isImpalaDriversInHouseTrainingId = null;
                break;
        }
    }

    public function saveTrainingChanges(){
     
      if($this->impalaInductionTrainingDate != '' || $this->impalaInductionTrainingDate !== null){
           
        SupabaseTraining::create(
            [
                'driver_id' => $this->id,
                'training_type_id' => 1,
                'training_date' => convertDateFormat($this->impalaInductionTrainingDate), 
            ]
        );
      }

      if($this->impalaDriversCodeOfConductDate != '' || $this->impalaDriversCodeOfConductDate !== null){
        
        SupabaseTraining::create(
            [
                'driver_id' => $this->id,
                'training_type_id' => 2,
                'training_date' => convertDateFormat($this->impalaDriversCodeOfConductDate), 
            ]
        );
      }

      if($this->impalaSafeDrivingDate != '' || $this->impalaSafeDrivingDate !== null){
        
        SupabaseTraining::create(
            [
                'driver_id' => $this->id,
                'training_type_id' => 3,
                'training_date' => convertDateFormat($this->impalaSafeDrivingDate), 
            ]
        );
      }

      if($this->impalaChaufferDriversCourseDate != '' || $this->impalaChaufferDriversCourseDate !== null){
        
        SupabaseTraining::create(
            [
                'driver_id' => $this->id,
                'training_type_id' => 4,
                'training_date' => convertDateFormat($this->impalaChaufferDriversCourseDate), 
            ]
        );
      }

      if($this->impalaCMEDVipDrivingCourseDate != '' || $this->impalaCMEDVipDrivingCourseDate !== null){
        
        SupabaseTraining::create(
            [
                'driver_id' => $this->id,
                'training_type_id' => 5,
                'training_date' => convertDateFormat($this->impalaCMEDVipDrivingCourseDate), 
            ]
        );
      }

      if($this->impalaDriversInHouseTrainingDate != '' || $this->impalaDriversInHouseTrainingDate !== null){
        
        SupabaseTraining::create(
            [
                'driver_id' => $this->id,
                'training_type_id' => 6,
                'training_date' => convertDateFormat($this->impalaDriversInHouseTrainingDate), 
            ]
        );
      }

      session()->flash('tranining_changes_saved', 'Training details updated successfully!');
    }

    public function saveMedicalChanges(){
        $medicalChanges = [];
    
        if($this->medicalTestsIssueDate != '' || $this->medicalTestsIssueDate !== null){
            $medicalChanges['medical_tests_issue_date'] = convertDateFormat($this->medicalTestsIssueDate);
         } 

         if($this->policeClearanceIssueDate != '' || $this->policeClearanceIssueDate !== null){
            $medicalChanges['police_clearance_issue_date'] = convertDateFormat($this->policeClearanceIssueDate);
         } 

         if($this->medicalTestFile != '' || $this->medicalTestFile !== null) {
            $medicalTestFilePath = $this->medicalTestFile->store('uploads', 'public');
            $medicalChanges['medical_test_file_path'] = $medicalTestFilePath;
         }

         if($this->policeClearanceFile != '' || $this->policeClearanceFile !== null) {
            $policeClearanceFilePath = $this->policeClearanceFile->store('uploads', 'public');
            $medicalChanges['police_clearance_file_path'] = $policeClearanceFilePath;
         }

         if($this->proofOfResidenceFile != '' || $this->proofOfResidenceFile !== null) {
            $proofOfResidenceFilePath = $this->proofOfResidenceFile->store('uploads', 'public');
            $medicalChanges['proof_of_residence_file_path'] = $proofOfResidenceFilePath;
         }

         SupabaseDriver::where('id',  $this->id)->update($medicalChanges);

         session()->flash('medical_changes_saved', 'Driver medical details updated successfully!');
    }
}
