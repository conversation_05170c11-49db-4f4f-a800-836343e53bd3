<?php

namespace App\Livewire\Driver;

use Livewire\Component;
use App\Models\Driver;

class EditMedicalClearanceComponent extends Component
{

    public $medicalTestsIssueDate;
    public $policeClearanceIssueDate;
    public $medicalTestFile;
    public $policeClearanceFile;
    public $proofOfResidenceFile;

    public $isEditingCarclass;

    public $isEditingId;
    
    public function mount() {
        return view('livewire.driver.edit-medical-clearance-component');
    }

    public function saveMedicalClearanceChanges(){
        // dd($this->driver);
    }

    public function render()
    {
    
        return view('livewire.driver.edit-medical-clearance-component');
    }
}
