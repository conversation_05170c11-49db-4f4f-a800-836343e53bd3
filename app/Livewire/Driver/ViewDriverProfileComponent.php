<?php

namespace App\Livewire\Driver;

use Illuminate\Support\Facades\Storage;

use Livewire\Component;
use App\Models\SupabaseModels\SupabaseDriver;
use Livewire\WithFileUploads;
use File;


class ViewDriverProfileComponent extends Component
{
    public SupabaseDriver $driver;

    public $isDownloading = false;
    public $isDownloadingPassport = false;

    public $isDownloadingLicence = false;
    public $isDownloadingDefenceLicence = false;
    public $isDownloadingIDL = false;
    public $isDownloadingFirstAid = false;
    public $isDownloadingMedicalTests = false;
    public $isDownloadingPoliceClearence = false;

    public $showBankingDetails = false;

    public function mount($driver)
    {
        $this->driver = $driver->load(['bank', 'driversLicence']); // Add bank relationship
        return view('livewire.driver.view-driver-profile-component');
    }

    public function toggleBankingDetails()
    {
        $this->showBankingDetails = !$this->showBankingDetails;
    }

    //Get file size
    function formatBytes($bytes, $precision = 2) {
        $kilobyte = 1024;
        $megabyte = $kilobyte * 1024;
        $gigabyte = $megabyte * 1024;
        
        if ($bytes < $kilobyte) {
            return $bytes . ' B';
        } elseif ($bytes < $megabyte) {
            return round($bytes / $kilobyte, $precision) . ' KB';
        } elseif ($bytes < $gigabyte) {
            return round($bytes / $megabyte, $precision) . ' MB';
        } else {
            return round($bytes / $gigabyte, $precision) . ' GB';
        }
    }

    public function getFileSize($path) {

        return $this->formatBytes(File::size('storage/'. $path));
    }

    public function downloadNationalId(){
       
        $this->isDownloading = true;
        try {
            if ($this->driver && $this->driver->national_id_file_path) {
                return Storage::disk('public')->download($this->driver->national_id_file_path);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Download failed');
        } finally {
            $this->isDownloading = false;
        }
    }

    public function downloadPassport(){
        $this->isDownloadingPassport = true;
        try {
            if ($this->driver && $this->driver->passport_file_path) {
                return Storage::disk('public')->download($this->driver->passport_file_path);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Download failed');
        } finally {
            $this->isDownloadingPassport = false;
        }
    }

    public function downloadDriversLicence(){
        $this->isDownloadingLicence = true;
        try {
            if ($this->driver && $this->driver->licence_file_path) {
                return Storage::disk('public')->download($this->driver->licence_file_path);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Download failed');
        } finally {
            $this->isDownloadingLicence = false;
        }
    }


    public function downloadDefenceDriversLicence(){
        $this->isDownloadingDefenceLicence = true;
        try {
            if ($this->driver && $this->driver->defence_licence_file_path) {
                return Storage::disk('public')->download($this->driver->defence_licence_file_path);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Download failed');
        } finally {
            $this->isDownloadingDefenceLicence = false;
        }
    }

    public function downloadIDL(){
        $this->isDownloadingIDL = true;
        try {
            if ($this->driver && $this->driver->idl_licence_path) {
                return Storage::disk('public')->download($this->driver->idl_licence_path);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Download failed');
        } finally {
            $this->isDownloadingIDL = false;
        }
    }

    public function downloadFirstAid(){
        $this->isDownloadingFirstAid = true;
        try {
            if ($this->driver && $this->driver->first_aid_certificate_file_path) {
                return Storage::disk('public')->download($this->driver->first_aid_certificate_file_path);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Download failed');
        } finally {
            $this->isDownloadingFirstAid = false;
        }
    }

    public function downloadMedicalAidTest(){
        $this->isDownloadingMedicalTests = true;
        try {
            if ($this->driver && $this->driver->medical_test_file_path) {
                return Storage::disk('public')->download($this->driver->medical_test_file_path);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Download failed');
        } finally {
            $this->isDownloadingMedicalTests = false;
        }
    }

    public function downloadPoliceClearence(){
        $this->isDownloadingPoliceClearence = true;
        try {
            if ($this->driver && $this->driver->police_clearance_file_path) {
                return Storage::disk('public')->download($this->driver->police_clearance_file_path);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Download failed');
        } finally {
            $this->isDownloadingPoliceClearence = false;
        }
    }
}
