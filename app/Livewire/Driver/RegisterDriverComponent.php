<?php

namespace App\Livewire\Driver;


use App\Models\Bank;
use App\Models\Driver;
use App\Models\Training;
use App\Models\SupabaseModels\SupabaseNextOfKin;
use App\Models\SupabaseModels\SupabaseDriver;
use App\Models\SupabaseModels\SupabaseTraining;
use App\Models\DriversLicence;
use App\Models\Gender;
use App\Models\ImpalaCertification;
use App\Models\NextOfKin;
use App\Models\TrainingType;
use Livewire\Component;
use Livewire\WithFileUploads;
use Exception;
use Illuminate\Support\Facades\DB;
use Spatie\LivewireFilepond\WithFilePond;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\Validator;
use Twilio\Rest\Client;

class RegisterDriverComponent extends Component
{
    use withFileUploads;
    use WithFilePond;


    public $file;
    public $currentStep = 1;
    public $totalSteps = 6;
    public $driverFirstname;
    public $driverLastname;
    public $driverDOB;
    public $selectedGenderId;
    public $driverMobileNumber;
    public $driverSecondMobileNumber;
    public $driverEmail;
    public $driverAddress;
    public $nextOfKinFullname;
    public $nextOfKinMobileNumber;
    public $nextOfKinAddress;
    public $drivingExperience;
    public $driversLicenceNumber;
    public $licenceIssueDate;
    public $selectedClassId;
    public $licenceFile;
    public $defencelicenceFile;
    public $defencelicenceExpiryDate;
    public $medicalTestsIssueDate;
    public $medicalTestFile;
    public $policeClearanceIssueDate;
    public $policeClearanceFile;
    public $impalaCeritificationDate;
    public $impalaCertificateFile;
    public $proofOfResidenceFile;
    public $selectedBankId;
    public $accountNumber;
    public $nationalIdNumber;
    public $nationalIdFile;
    public $passportNumber;
    public $passportFile;
    public $firstAidCertificateFile;
    public $cmedVipCertificateFile;
    public $internationalDrivingLicenceFile;
    public $ecocashNumber;
    public $innBucksNumber;
    public $isImpalaInductionTrainingChecked;
    public $impalaInductionTrainingDate;
    public $impalaInductionTrainingId;
    public $isImpalaDriversCodeOfConductChecked;
    public $impalaDriversCodeOfConductDate;
    public $impalaDriversCodeOfConductId;
    public $isImpalaSafeDrivingChecked;
    public $impalaSafeDrivingDate;
    public $impalaSafeDrivingId;
    public $isImpalaChaufferDriversCourseChecked;
    public $impalaChaufferDriversCourseDate;
    public $impalaChaufferDriversCourseId;
    public $isImpalaCMEDVipDrivingCourseChecked;
    public $impalaCMEDVipDrivingCourseDate;
    public $impalaCMEDVipDrivingCourseId;
    public $isImpalaDriversInHouseTrainingChecked;
    public $impalaDriversInHouseTrainingDate;
    public $isImpalaDriversInHouseTrainingId;
    public $driverProfilePhotoFile;


    public $impalaInductionTrainingDateVal = null;
    public $impalaDriversCodeOfConductDateVal = null;
    public $impalaSafeDrivingDateVal = null;
    public $impalaChaufferDriversCourseDateVal = null;
    public $impalaCMEDVipDrivingCourseDateVal = null;
    public $impalaDriversInHouseTrainingDateVal = null;
    public $istestCheck = false;
    public $isSaving = false;


    
    public function render()
    {

        $licenceClasses = DriversLicence::pluck('licence_class', 'id');
        $selectedLicenceClass = 0;
        $selectedCertification = 0;
        $banks = Bank::pluck('bank', 'id');
        $selectedBank = 0;
        $genders = Gender::pluck('gender', 'id');
        $selectedGenderId = 0;
        $trainingTypes = TrainingType::all();

        return view('livewire.driver.register-driver-component', [
            'selectedLicenceClass' => $selectedLicenceClass,
            'licenceClasses' => $licenceClasses,
            'selectedCertification' => $selectedCertification,
            'selectedBank' => $selectedBank,
            'banks' => $banks,
            'genders' => $genders,
            'selectedGenderId' => $selectedGenderId,
            'trainingTypes' => $trainingTypes
        ]);
    }


    public function nextStep()
    {
        if ($this->currentStep < $this->totalSteps) {
            if($this->currentStep == 1) {
                 
                $validated = Validator::make(
                // Data to validate...

              
                [
                    'driverFirstname' => $this->driverFirstname,
                    'driverLastname' => $this->driverLastname,
                    'driverMobileNumber' => $this->driverMobileNumber,
                    'driverEmail' => $this->driverEmail,
                    'driverAddress' => $this->driverAddress,
                    'nationalIdNumber' => $this->nationalIdNumber,
                    'nationalIdFile' => $this->nationalIdFile,
                    'driverProfilePhotoFile' => $this->driverProfilePhotoFile,
                    'selectedGenderId' => $this->selectedGenderId,
                    'driverDOB' => $this->driverDOB,

                ],

                // Validation rules to apply...
                [
                    'driverFirstname' => 'required|max:50',
                    'driverLastname' => 'required|max:50',
                    'driverMobileNumber' => 'required|max:50',
                    'driverEmail' => 'required|email|max:50',
                    'driverAddress' => 'required|max:50',
                    'nationalIdNumber' => 'required|max:50',
                    'nationalIdFile' => 'required|file|mimes:png,jpg,pdf|max:3072',
                    'driverProfilePhotoFile' => 'required|file|mimes:png,jpg,pdf|max:3072',
                    'selectedGenderId' => 'required|exists:genders,id',
                    'driverDOB' => 'required|date',
                ],

                // Custom validation messages...
                [
                    'required' => 'The :attribute field is required',
                    'selectedGenderId.required' => 'Please select a gender',
                    'selectedGenderId.exists' => 'The selected gender is invalid'
                ],
                )->validate();

                

                $this->currentStep++;
            } else if($this->currentStep == 2){
                Validator::make(
                    // Data to validate...
                    [
                        'nextOfKinFullname' => $this->nextOfKinFullname,
                        'nextOfKinMobileNumber' => $this->nextOfKinMobileNumber,
                        'nextOfKinAddress' => $this->nextOfKinAddress,
                    ],

                    // Validation rules to apply...
                    [
                        'nextOfKinFullname' => 'required|max:50',
                        'nextOfKinMobileNumber' => 'required|max:50',
                        'nextOfKinAddress' => 'required|max:50',
                    ],

                    // Custom validation messages...
                    ['required' => 'The :attribute field is required'],
                )->validate();

                $this->currentStep++;

            } else if($this->currentStep == 3){

            

                Validator::make(
                    // Data to validate...
                    [
                        'drivingExperience' => $this->drivingExperience,
                        'driversLicenceNumber' => $this->driversLicenceNumber,
                        'licenceFile' => $this->licenceFile,
                        'defencelicenceFile' => $this->defencelicenceFile,
                        'firstAidCertificateFile' => $this->firstAidCertificateFile,
                        'licenceIssueDate' => $this->licenceIssueDate,
                        'defencelicenceExpiryDate' => $this->defencelicenceExpiryDate,
                        'selectedClassId' => $this->selectedClassId,
                    ],
                    [
                        'drivingExperience' => 'required|numeric|min:0',
                        'driversLicenceNumber' => 'required|max:50',
                        'licenceFile' => 'required|file|mimes:png,jpg,pdf|max:3072',
                        'defencelicenceFile' => 'required|file|mimes:png,jpg,pdf|max:3072',
                        'firstAidCertificateFile' => 'required|file|mimes:png,jpg,pdf|max:3072',
                        'licenceIssueDate' => 'required|date|before:today',
                        'defencelicenceExpiryDate' => 'required|date|after:today',
                        'selectedClassId' => 'required|exists:drivers_licences,id',
                    ],
                    [
                        'required' => 'The :attribute field is required',
                        'licenceIssueDate.before' => 'Licence issue date must be in the past',
                        'defencelicenceExpiryDate.after' => 'Defensive licence expiry date must be in the future',
                        'date' => 'Please enter a valid date',
                        'selectedClassId.required' => 'Please select a licence class',
                        'selectedClassId.exists' => 'The selected licence class is invalid'
                    ]
                )->validate();

                $this->currentStep++;
            } else if($this->currentStep == 4){
                $this->currentStep++;
            } else if($this->currentStep == 5){
                Validator::make(
                    [
                        'medicalTestsIssueDate' => $this->medicalTestsIssueDate,
                        'policeClearanceIssueDate' => $this->policeClearanceIssueDate,
                        'impalaCeritificationDate' => $this->impalaCeritificationDate,
                        'medicalTestFile' => $this->medicalTestFile,
                        'policeClearanceFile' => $this->policeClearanceFile,
                        'proofOfResidenceFile' => $this->proofOfResidenceFile,
                    ],
                    [
                        'medicalTestsIssueDate' => 'required|date',
                        'policeClearanceIssueDate' => 'required|date|before:today',
                        'impalaCeritificationDate' => 'required|date|before:today',
                        'medicalTestFile' => 'required|file|mimes:png,jpg,jpeg,pdf|max:3072',
                        'policeClearanceFile' => 'required|file|mimes:png,jpg,jpeg,pdf|max:3072',
                        'proofOfResidenceFile' => 'required|file|mimes:png,jpg,jpeg,pdf|max:3072',
                    ],
                    [
                        'required' => 'The :attribute field is required',
                        'date' => 'Please enter a valid date',
                        'policeClearanceIssueDate.before' => 'Police clearance issue date must be in the past',
                        'impalaCeritificationDate.before' => 'Impala certificate issue date must be in the past',
                        'file' => 'The :attribute must be a file',
                        'mimes' => 'The :attribute must be a PNG, JPG or PDF file',
                        'max' => 'The :attribute must not be larger than 3MB'
                    ]
                )->validate();

                // Debug output at step 5
              
                
                $this->currentStep++;
            }
        }
    }

    public function previousStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }



    public function toggleTraining($value, $isChecked): void
    {
        switch ($value) {
            case 1:
                $this->isImpalaInductionTrainingChecked = $isChecked;
                $isChecked ? $this->impalaInductionTrainingId = $value : $this->impalaInductionTrainingId = null;
                break;
            case 2:

                $this->isImpalaDriversCodeOfConductChecked = $isChecked;
                $isChecked ? $this->impalaDriversCodeOfConductId = $value : $this->impalaDriversCodeOfConductId = null;
                break;
            case 3:

                $this->isImpalaSafeDrivingChecked = $isChecked;
                $isChecked ? $this->impalaSafeDrivingId = $value : $this->impalaSafeDrivingId = null;
                break;
            case 4:

                $this->isImpalaChaufferDriversCourseChecked = $isChecked;
                $isChecked ? $this->impalaChaufferDriversCourseId = $value : $this->impalaChaufferDriversCourseId = null;
                break;
            case 5:

                $this->isImpalaCMEDVipDrivingCourseChecked = $isChecked;
                $isChecked ? $this->impalaCMEDVipDrivingCourseId = $value : $this->impalaCMEDVipDrivingCourseId = null;
                break;
            case 6:
                $this->isImpalaDriversInHouseTrainingChecked = $isChecked;
                $isChecked ? $this->isImpalaDriversInHouseTrainingId = $value : $this->isImpalaDriversInHouseTrainingId = null;
                break;
        }
    }


    protected function appendCountryCode($number)
    {
        // Remove any spaces or special characters
        $number = preg_replace('/[^0-9]/', '', $number);
        
        // If number starts with 0, remove it and append +263
        if (str_starts_with($number, '0')) {
            $number = '+263' . substr($number, 1);
        }
        
        return $number;
    }

    protected function generateUniqueCode()
    {
        do {
            $code = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
        } while (SupabaseDriver::where('code', $code)->exists());
        
        return $code;
    }


    public function save()
    {
        $this->isSaving = true;

        try {
            // Store all required files first and validate they exist
            try {
                $filePaths = [
                    'licence_file_path' => $this->licenceFile->store('uploads', 'public'),
                    'defence_licence_file_path' => $this->defencelicenceFile->store('uploads', 'public'),
                    'medical_test_file_path' => $this->medicalTestFile->store('uploads', 'public'),
                    'police_clearance_file_path' => $this->policeClearanceFile->store('uploads', 'public'),
                    'proof_of_residence_file_path' => $this->proofOfResidenceFile->store('uploads', 'public'),
                    'national_id_file_path' => $this->nationalIdFile->store('uploads', 'public'),
                    'profile_photo_file' => $this->driverProfilePhotoFile->store('uploads', 'public'),
                    'first_aid_certificate_file_path' => $this->firstAidCertificateFile->store('uploads', 'public'),
                ];

                // Optional files
                if ($this->passportFile) {
                    $filePaths['passport_file_path'] = $this->passportFile->store('uploads', 'public');
                }
                if ($this->internationalDrivingLicenceFile) {
                    $filePaths['idl_licence_path'] = $this->internationalDrivingLicenceFile->store('uploads', 'public');
                }

            } catch (\Exception $e) {
                throw new \Exception('Failed to store files: ' . $e->getMessage());
            }

            // Convert dates
            $dates = [
                'driver_dob' => convertDateFormat($this->driverDOB),
                'licence_issue_date' => convertDateFormat($this->licenceIssueDate),
                'defence_licence_expiry_date' => convertDateFormat($this->defencelicenceExpiryDate),
                'medical_tests_issue_date' => convertDateFormat($this->medicalTestsIssueDate),
                'police_clearance_issue_date' => convertDateFormat($this->policeClearanceIssueDate),
            ];

            // Create driver with all fields including file paths
            $driver = SupabaseDriver::create(array_merge([
                'driver_firstname' => $this->driverFirstname,
                'driver_lastname' => $this->driverLastname,
                'driver_mobile' => $this->appendCountryCode($this->driverMobileNumber),
                'driver_second_mobile' => $this->driverSecondMobileNumber,
                'driver_email' => $this->driverEmail,
                'driver_address' => $this->driverAddress,
                'driving_experience' => (int)$this->drivingExperience,
                'drivers_licence' => $this->driversLicenceNumber,
                'account_number' => $this->accountNumber,
                'national_id' => $this->nationalIdNumber,
                'passport_number' => $this->passportNumber,
                'ecocash_number' => $this->ecocashNumber,
                'innbucks_number' => $this->innBucksNumber,
                'gender_id' => $this->selectedGenderId,
                'drivers_licence_id' => $this->selectedClassId,
                'bank_id' => $this->selectedBankId,
                'driver_dob' => convertDateFormat($this->driverDOB),
                'licence_issue_date' =>convertDateFormat($this->licenceIssueDate),
                'defence_licence_expiry_date' => convertDateFormat($this->defencelicenceExpiryDate),
                'medical_tests_issue_date' => convertDateFormat($this->medicalTestsIssueDate),
                'police_clearance_issue_date' => convertDateFormat($this->policeClearanceIssueDate),
                'code' => $this->generateUniqueCode()
            ], $filePaths, $dates));

            \Log::info('Driver created with ID: ' . $driver->id);

            // Create next of kin after successful driver creation
            if ($driver->id) {
                try {
                    $nextOfKin = SupabaseNextOfKin::create([
                        'driver_id' => $driver->id,
                        'fullname' => $this->nextOfKinFullname,
                        'mobile_number' => $this->nextOfKinMobileNumber,
                        'address' => $this->nextOfKinAddress
                    ]);
                    \Log::info('Next of kin created:', ['next_of_kin_id' => $nextOfKin->id]);

                    // Add debug logging for training records
                    \Log::info('Training data before creation:', [
                        'driver_id' => $driver->id,
                        'training_dates' => [
                            'induction' => $this->impalaInductionTrainingDateVal,
                            'code_of_conduct' => $this->impalaDriversCodeOfConductDateVal,
                            'safe_driving' => $this->impalaSafeDrivingDateVal
                        ],
                        'training_ids' => [
                            'induction' => $this->impalaInductionTrainingId,
                            'code_of_conduct' => $this->impalaDriversCodeOfConductId,
                            'safe_driving' => $this->impalaSafeDrivingId
                        ]
                    ]);

                    $this->createTrainingRecords($driver->id);
                    $this->sendSMS($driver->code, $this->driverMobileNumber);



                } catch (\Exception $e) {
                    \Log::error('Failed to save related records:', [
                        'error' => $e->getMessage(),
                        'next_of_kin' => [
                            'fullname' => $this->nextOfKinFullname,
                            'mobile' => $this->nextOfKinMobileNumber
                        ]
                    ]);
                    session()->flash('error', 'Failed to save additional records: ' . $e->getMessage());
                }

               

                session()->flash('success', 'Driver profile created successfully!');
                return redirect()->route('view_drivers');
            }

        } catch (Exception $e) {
            \Log::error('Failed to save driver: ' . $e->getMessage());
            session()->flash('error', 'Failed to save driver: ' . $e->getMessage());
        } finally {
            $this->isSaving = false;
        }
    }

    protected function sendSMS($code, $driverMobileNumber){
        try {
                $twilio = new Client('**********************************', '10377b216991ea3e894d9d95ad834991');
    
                $twilio->messages->create(
                $this->appendCountryCode($driverMobileNumber),
                    [
                        'from' => '+12089041849',
                        'body' => 'Please use this code (' . $code . ') when you sign up on Impla mobile app. ',
                    ]
                );
    
              
            } catch (Exception $e) {
                session()->flash('error', 'Failed to send message: ' . $e->getMessage());
            }
    }

    protected function storeDriverFiles($driver)
    {
        // Store files
        $updates = [];
        
        if ($this->licenceFile) {
            $updates['licence_file_path'] = $this->licenceFile->store('uploads', 'public');
        }
        if ($this->driverProfilePhotoFile) {
            $updates['profile_photo_file'] = $this->driverProfilePhotoFile->store('uploads', 'public');
        }
        if ($this->nationalIdFile) {
            $updates['national_id_file_path'] = $this->nationalIdFile->store('uploads', 'public');
        }
        
        // Update driver record with file paths if any were stored
        if (!empty($updates)) {
            $driver->update($updates);
        }
    }

    protected function createTrainingRecords($driverId)
    {
        try {
            $trainings = [];
            
            // Only collect trainings that have both checkbox checked AND date
            if ($this->isImpalaInductionTrainingChecked && !empty($this->impalaInductionTrainingDate)) {
                $trainings[] = [
                    'driver_id' => $driverId,
                    'training_type_id' => 1,
                    'training_date' => convertDateFormat($this->impalaInductionTrainingDate)
                ];
            }

            if ($this->isImpalaDriversCodeOfConductChecked && !empty($this->impalaDriversCodeOfConductDate)) {
                $trainings[] = [
                    'driver_id' => $driverId,
                    'training_type_id' => 2,
                    'training_date' => convertDateFormat($this->impalaDriversCodeOfConductDate)
                ];
            }

            if ($this->isImpalaSafeDrivingChecked && !empty($this->impalaSafeDrivingDate)) {
                $trainings[] = [
                    'driver_id' => $driverId,
                    'training_type_id' => 3,
                    'training_date' => convertDateFormat($this->impalaSafeDrivingDate)
                ];
            }

            // Only proceed if we have trainings to save
            if (!empty($trainings)) {
                foreach ($trainings as $training) {
                    SupabaseTraining::create($training);
                }
                \Log::info('Created ' . count($trainings) . ' training records');
            } else {
                \Log::info('No training records to create');
            }

        } catch (\Exception $e) {
            // Log error but don't throw exception since trainings are optional
            \Log::error('Failed to create training records:', ['error' => $e->getMessage()]);
        }
    }
}

