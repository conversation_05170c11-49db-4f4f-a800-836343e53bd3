<?php

namespace App\Livewire\Driver;

use App\Models\Bank;
use App\Models\SupabaseModels\SupabaseNextOfKin;
use App\Models\SupabaseModels\SupabaseDriver;
use App\Models\SupabaseModels\SupabaseTraining;
use App\Models\DriversLicence;
use App\Models\Gender;
use App\Models\TrainingType;
use Livewire\Component;
use Livewire\WithFileUploads;
use Exception;
use Illuminate\Support\Facades\DB;
use Spatie\LivewireFilepond\WithFilePond;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Twilio\Rest\Client;
use Illuminate\Support\Facades\Storage;

class RegisterDriverComponent extends Component
{
    use withFileUploads;
    use WithFilePond;


    public $file;
    public $currentStep = 1;
    public $totalSteps = 6;
    public $driverFirstname;
    public $driverLastname;
    public $driverDOB;
    public $selectedGenderId;
    public $driverMobileNumber;
    public $driverSecondMobileNumber;
    public $driverEmail;
    public $driverAddress;
    public $nextOfKinFullname;
    public $nextOfKinMobileNumber;
    public $nextOfKinAddress;
    public $drivingExperience;
    public $driversLicenceNumber;
    public $licenceIssueDate;
    public $selectedClassId;
    public $licenceFile;
    public $defencelicenceFile;
    public $defencelicenceExpiryDate;
    public $medicalTestsIssueDate;
    public $medicalTestFile;
    public $policeClearanceIssueDate;
    public $policeClearanceFile;
    public $impalaCeritificationDate;
    public $impalaCertificateFile;
    public $proofOfResidenceFile;
    public $selectedBankId;
    public $accountNumber;
    public $nationalIdNumber;
    public $nationalIdFile;
    public $passportNumber;
    public $passportFile;
    public $firstAidCertificateFile;
    public $cmedVipCertificateFile;
    public $internationalDrivingLicenceFile;
    public $ecocashNumber;
    public $innBucksNumber;
    public $isImpalaInductionTrainingChecked;
    public $impalaInductionTrainingDate;
    public $impalaInductionTrainingId;
    public $isImpalaDriversCodeOfConductChecked;
    public $impalaDriversCodeOfConductDate;
    public $impalaDriversCodeOfConductId;
    public $isImpalaSafeDrivingChecked;
    public $impalaSafeDrivingDate;
    public $impalaSafeDrivingId;
    public $isImpalaChaufferDriversCourseChecked;
    public $impalaChaufferDriversCourseDate;
    public $impalaChaufferDriversCourseId;
    public $isImpalaCMEDVipDrivingCourseChecked;
    public $impalaCMEDVipDrivingCourseDate;
    public $impalaCMEDVipDrivingCourseId;
    public $isImpalaDriversInHouseTrainingChecked;
    public $impalaDriversInHouseTrainingDate;
    public $isImpalaDriversInHouseTrainingId;
    public $driverProfilePhotoFile;


    public $impalaInductionTrainingDateVal = null;
    public $impalaDriversCodeOfConductDateVal = null;
    public $impalaSafeDrivingDateVal = null;
    public $impalaChaufferDriversCourseDateVal = null;
    public $impalaCMEDVipDrivingCourseDateVal = null;
    public $impalaDriversInHouseTrainingDateVal = null;
    public $istestCheck = false;
    public $isSaving = false;


    
    public function render()
    {

        $licenceClasses = DriversLicence::pluck('licence_class', 'id');
        $selectedLicenceClass = 0;
        $selectedCertification = 0;
        $banks = Bank::pluck('bank', 'id');
        $selectedBank = 0;
        $genders = Gender::pluck('gender', 'id');
        $selectedGenderId = 0;
        $trainingTypes = TrainingType::all();

        return view('livewire.driver.register-driver-component', [
            'selectedLicenceClass' => $selectedLicenceClass,
            'licenceClasses' => $licenceClasses,
            'selectedCertification' => $selectedCertification,
            'selectedBank' => $selectedBank,
            'banks' => $banks,
            'genders' => $genders,
            'selectedGenderId' => $selectedGenderId,
            'trainingTypes' => $trainingTypes
        ]);
    }


    public function nextStep()
    {
        if ($this->currentStep < $this->totalSteps) {
            $this->validateCurrentStep();
            $this->currentStep++;
        }
    }

    /**
     * Validate the current step data
     */
    protected function validateCurrentStep()
    {
        switch ($this->currentStep) {
            case 1:
                $this->validateStep1();
                break;
            case 2:
                $this->validateStep2();
                break;
            case 3:
                $this->validateStep3();
                break;
            case 4:
                // Training step - no validation needed as it's optional
                break;
            case 5:
                $this->validateStep5();
                break;
            case 6:
                $this->validateStep6();
                break;
        }
    }

    /**
     * Validate Step 1: Driver Details
     */
    protected function validateStep1()
    {
        Validator::make([
            'driverFirstname' => $this->driverFirstname,
            'driverLastname' => $this->driverLastname,
            'driverMobileNumber' => $this->driverMobileNumber,
            'driverEmail' => $this->driverEmail,
            'driverAddress' => $this->driverAddress,
            'nationalIdNumber' => $this->nationalIdNumber,
            'nationalIdFile' => $this->nationalIdFile,
            'driverProfilePhotoFile' => $this->driverProfilePhotoFile,
            'selectedGenderId' => $this->selectedGenderId,
            'driverDOB' => $this->driverDOB,
        ], [
            'driverFirstname' => 'required|string|max:255|regex:/^[a-zA-Z\s]+$/',
            'driverLastname' => 'required|string|max:255|regex:/^[a-zA-Z\s]+$/',
            'driverMobileNumber' => [
                'required',
                'string',
                'max:20',
                'regex:/^(\+263|0)[0-9]{9}$/',
                Rule::unique('pgsql.drivers', 'driver_mobile')
            ],
            'driverEmail' => [
                'required',
                'email:rfc,dns',
                'max:255',
                Rule::unique('pgsql.drivers', 'driver_email')
            ],
            'driverAddress' => 'required|string|max:500',
            'nationalIdNumber' => [
                'required',
                'string',
                'max:50',
                'regex:/^[0-9]{2}-[0-9]{6,7}[A-Z][0-9]{2}$/',
                Rule::unique('pgsql.drivers', 'national_id')
            ],
            'nationalIdFile' => 'required|file|mimes:png,jpg,jpeg,pdf|max:5120',
            'driverProfilePhotoFile' => 'required|file|mimes:png,jpg,jpeg|max:5120',
            'selectedGenderId' => 'required|exists:pgsql.genders,id',
            'driverDOB' => 'required|date|before:-18 years|after:1940-01-01',
        ], [
            'driverFirstname.regex' => 'First name should only contain letters and spaces',
            'driverLastname.regex' => 'Last name should only contain letters and spaces',
            'driverMobileNumber.regex' => 'Mobile number must be a valid Zimbabwean number (e.g., +263771234567 or 0771234567)',
            'driverMobileNumber.unique' => 'This mobile number is already registered',
            'driverEmail.unique' => 'This email address is already registered',
            'driverEmail.email' => 'Please enter a valid email address',
            'nationalIdNumber.regex' => 'National ID must be in format: XX-XXXXXXX-X-XX (e.g., 63-123456A12)',
            'nationalIdNumber.unique' => 'This national ID is already registered',
            'driverDOB.before' => 'Driver must be at least 18 years old',
            'driverDOB.after' => 'Please enter a valid date of birth',
            'selectedGenderId.required' => 'Please select a gender',
            'selectedGenderId.exists' => 'The selected gender is invalid',
            'nationalIdFile.max' => 'National ID file must not be larger than 5MB',
            'driverProfilePhotoFile.max' => 'Profile photo must not be larger than 5MB',
        ])->validate();
    }

    /**
     * Validate Step 2: Next of Kin Details
     */
    protected function validateStep2()
    {
        Validator::make([
            'nextOfKinFullname' => $this->nextOfKinFullname,
            'nextOfKinMobileNumber' => $this->nextOfKinMobileNumber,
            'nextOfKinAddress' => $this->nextOfKinAddress,
        ], [
            'nextOfKinFullname' => 'required|string|max:255|regex:/^[a-zA-Z\s]+$/',
            'nextOfKinMobileNumber' => 'required|string|max:20|regex:/^(\+263|0)[0-9]{9}$/',
            'nextOfKinAddress' => 'required|string|max:500',
        ], [
            'nextOfKinFullname.regex' => 'Next of kin name should only contain letters and spaces',
            'nextOfKinMobileNumber.regex' => 'Mobile number must be a valid Zimbabwean number',
        ])->validate();
    }

    /**
     * Validate Step 3: Licensing Information
     */
    protected function validateStep3()
    {
        Validator::make([
            'drivingExperience' => $this->drivingExperience,
            'driversLicenceNumber' => $this->driversLicenceNumber,
            'licenceFile' => $this->licenceFile,
            'defencelicenceFile' => $this->defencelicenceFile,
            'firstAidCertificateFile' => $this->firstAidCertificateFile,
            'licenceIssueDate' => $this->licenceIssueDate,
            'defencelicenceExpiryDate' => $this->defencelicenceExpiryDate,
            'selectedClassId' => $this->selectedClassId,
        ], [
            'drivingExperience' => 'required|numeric|min:0|max:50',
            'driversLicenceNumber' => 'required|string|max:50|regex:/^[A-Z0-9\-]+$/',
            'licenceFile' => 'required|file|mimes:png,jpg,jpeg,pdf|max:5120',
            'defencelicenceFile' => 'required|file|mimes:png,jpg,jpeg,pdf|max:5120',
            'firstAidCertificateFile' => 'required|file|mimes:png,jpg,jpeg,pdf|max:5120',
            'licenceIssueDate' => 'required|date|before:today|after:1980-01-01',
            'defencelicenceExpiryDate' => 'required|date|after:today|before:+10 years',
            'selectedClassId' => 'required|exists:pgsql.drivers_licences,id',
        ], [
            'drivingExperience.max' => 'Driving experience cannot exceed 50 years',
            'driversLicenceNumber.regex' => 'License number should only contain letters, numbers, and hyphens',
            'licenceIssueDate.before' => 'License issue date must be in the past',
            'licenceIssueDate.after' => 'Please enter a valid license issue date',
            'defencelicenceExpiryDate.after' => 'Defensive license must not be expired',
            'defencelicenceExpiryDate.before' => 'Defensive license expiry date seems too far in the future',
            'selectedClassId.required' => 'Please select a license class',
            'selectedClassId.exists' => 'The selected license class is invalid',
        ])->validate();
    }

    /**
     * Validate Step 5: Medical and Police Clearance
     */
    protected function validateStep5()
    {
        Validator::make([
            'medicalTestsIssueDate' => $this->medicalTestsIssueDate,
            'policeClearanceIssueDate' => $this->policeClearanceIssueDate,
            'impalaCeritificationDate' => $this->impalaCeritificationDate,
            'medicalTestFile' => $this->medicalTestFile,
            'policeClearanceFile' => $this->policeClearanceFile,
            'proofOfResidenceFile' => $this->proofOfResidenceFile,
        ], [
            'medicalTestsIssueDate' => 'required|date|before:today|after:-2 years',
            'policeClearanceIssueDate' => 'required|date|before:today|after:-1 year',
            'impalaCeritificationDate' => 'required|date|before:today|after:-5 years',
            'medicalTestFile' => 'required|file|mimes:png,jpg,jpeg,pdf|max:5120',
            'policeClearanceFile' => 'required|file|mimes:png,jpg,jpeg,pdf|max:5120',
            'proofOfResidenceFile' => 'required|file|mimes:png,jpg,jpeg,pdf|max:5120',
        ], [
            'medicalTestsIssueDate.after' => 'Medical test must be issued within the last 2 years',
            'policeClearanceIssueDate.after' => 'Police clearance must be issued within the last year',
            'impalaCeritificationDate.after' => 'Impala certificate must be issued within the last 5 years',
            'medicalTestsIssueDate.before' => 'Medical test issue date must be in the past',
            'policeClearanceIssueDate.before' => 'Police clearance issue date must be in the past',
            'impalaCeritificationDate.before' => 'Impala certificate issue date must be in the past',
        ])->validate();
    }

    /**
     * Validate Step 6: Banking Details
     */
    protected function validateStep6()
    {
        Validator::make([
            'selectedBankId' => $this->selectedBankId,
            'accountNumber' => $this->accountNumber,
            'ecocashNumber' => $this->ecocashNumber,
            'innBucksNumber' => $this->innBucksNumber,
        ], [
            'selectedBankId' => 'required|exists:banks,id',
            'accountNumber' => 'required|string|min:8|max:20|regex:/^[0-9]+$/',
            'ecocashNumber' => 'nullable|string|max:20|regex:/^(\+263|0)[0-9]{9}$/',
            'innBucksNumber' => 'nullable|string|max:20|regex:/^(\+263|0)[0-9]{9}$/',
        ], [
            'selectedBankId.required' => 'Please select a bank',
            'selectedBankId.exists' => 'The selected bank is invalid',
            'accountNumber.regex' => 'Account number should only contain numbers',
            'accountNumber.min' => 'Account number must be at least 8 digits',
            'accountNumber.max' => 'Account number cannot exceed 20 digits',
            'ecocashNumber.regex' => 'Ecocash number must be a valid Zimbabwean mobile number',
            'innBucksNumber.regex' => 'Innbucks number must be a valid Zimbabwean mobile number',
        ])->validate();
    }

    public function previousStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }



    public function toggleTraining($value, $isChecked): void
    {
        switch ($value) {
            case 1:
                $this->isImpalaInductionTrainingChecked = $isChecked;
                $isChecked ? $this->impalaInductionTrainingId = $value : $this->impalaInductionTrainingId = null;
                break;
            case 2:

                $this->isImpalaDriversCodeOfConductChecked = $isChecked;
                $isChecked ? $this->impalaDriversCodeOfConductId = $value : $this->impalaDriversCodeOfConductId = null;
                break;
            case 3:

                $this->isImpalaSafeDrivingChecked = $isChecked;
                $isChecked ? $this->impalaSafeDrivingId = $value : $this->impalaSafeDrivingId = null;
                break;
            case 4:

                $this->isImpalaChaufferDriversCourseChecked = $isChecked;
                $isChecked ? $this->impalaChaufferDriversCourseId = $value : $this->impalaChaufferDriversCourseId = null;
                break;
            case 5:

                $this->isImpalaCMEDVipDrivingCourseChecked = $isChecked;
                $isChecked ? $this->impalaCMEDVipDrivingCourseId = $value : $this->impalaCMEDVipDrivingCourseId = null;
                break;
            case 6:
                $this->isImpalaDriversInHouseTrainingChecked = $isChecked;
                $isChecked ? $this->isImpalaDriversInHouseTrainingId = $value : $this->isImpalaDriversInHouseTrainingId = null;
                break;
        }
    }


    protected function appendCountryCode($number)
    {
        // Remove any spaces or special characters
        $number = preg_replace('/[^0-9]/', '', $number);
        
        // If number starts with 0, remove it and append +263
        if (str_starts_with($number, '0')) {
            $number = '+263' . substr($number, 1);
        }
        
        return $number;
    }

    protected function generateUniqueCode()
    {
        do {
            $code = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
        } while (SupabaseDriver::where('code', $code)->exists());
        
        return $code;
    }


    public function save()
    {
        $this->isSaving = true;

        // Final validation before saving
        $this->validateAllSteps();

        DB::beginTransaction();

        try {
            // Store files first
            $filePaths = $this->storeFiles();

            // Prepare driver data
            $driverData = $this->prepareDriverData($filePaths);

            // Create driver record
            $driver = SupabaseDriver::create($driverData);
            \Log::info("Driver created with ID: {$driver->id}");

            // Create related records
            $this->createNextOfKin($driver->id);
            $this->createTrainingRecords($driver->id);

            // Send SMS notification
            $this->sendSMS($driver->code, $this->driverMobileNumber);

            DB::commit();

            session()->flash('success', 'Driver profile created successfully!');
            return redirect()->route('view_drivers');

        } catch (Exception $e) {
            DB::rollBack();

            // Clean up uploaded files on failure
            if (isset($filePaths)) {
                $this->cleanupFiles($filePaths);
            }

            \Log::error('Failed to save driver:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'Failed to save driver profile: ' . $e->getMessage());
        } finally {
            $this->isSaving = false;
        }
    }

    /**
     * Validate all steps before final submission
     */
    protected function validateAllSteps()
    {
        $this->validateStep1();
        $this->validateStep2();
        $this->validateStep3();
        $this->validateStep5();
        $this->validateStep6();
    }

    /**
     * Store all uploaded files
     */
    protected function storeFiles(): array
    {
        try {
            $filePaths = [
                'licence_file_path' => $this->licenceFile->store('driver-documents', 'public'),
                'defence_licence_file_path' => $this->defencelicenceFile->store('driver-documents', 'public'),
                'medical_test_file_path' => $this->medicalTestFile->store('driver-documents', 'public'),
                'police_clearance_file_path' => $this->policeClearanceFile->store('driver-documents', 'public'),
                'proof_of_residence_file_path' => $this->proofOfResidenceFile->store('driver-documents', 'public'),
                'national_id_file_path' => $this->nationalIdFile->store('driver-documents', 'public'),
                'profile_photo_file' => $this->driverProfilePhotoFile->store('driver-photos', 'public'),
                'first_aid_certificate_file_path' => $this->firstAidCertificateFile->store('driver-documents', 'public'),
            ];

            // Optional files
            if ($this->passportFile) {
                $filePaths['passport_file_path'] = $this->passportFile->store('driver-documents', 'public');
            }
            if ($this->internationalDrivingLicenceFile) {
                $filePaths['idl_licence_path'] = $this->internationalDrivingLicenceFile->store('driver-documents', 'public');
            }

            return $filePaths;

        } catch (Exception $e) {
            throw new Exception('Failed to store files: ' . $e->getMessage());
        }
    }

    /**
     * Prepare driver data for database insertion
     */
    protected function prepareDriverData(array $filePaths): array
    {
        return array_merge([
            'driver_firstname' => trim($this->driverFirstname),
            'driver_lastname' => trim($this->driverLastname),
            'driver_mobile' => $this->appendCountryCode($this->driverMobileNumber),
            'driver_second_mobile' => $this->driverSecondMobileNumber ? $this->appendCountryCode($this->driverSecondMobileNumber) : null,
            'driver_email' => strtolower(trim($this->driverEmail)),
            'driver_address' => trim($this->driverAddress),
            'driving_experience' => (int)$this->drivingExperience,
            'drivers_licence' => strtoupper(trim($this->driversLicenceNumber)),
            'account_number' => trim($this->accountNumber),
            'national_id' => strtoupper(trim($this->nationalIdNumber)),
            'passport_number' => $this->passportNumber ? strtoupper(trim($this->passportNumber)) : null,
            'ecocash_number' => $this->ecocashNumber ? $this->appendCountryCode($this->ecocashNumber) : null,
            'innbucks_number' => $this->innBucksNumber ? $this->appendCountryCode($this->innBucksNumber) : null,
            'gender_id' => $this->selectedGenderId,
            'drivers_licence_id' => $this->selectedClassId,
            'bank_id' => $this->selectedBankId,
            'driver_dob' => convertDateFormat($this->driverDOB),
            'licence_issue_date' => convertDateFormat($this->licenceIssueDate),
            'defence_licence_expiry_date' => convertDateFormat($this->defencelicenceExpiryDate),
            'medical_tests_issue_date' => convertDateFormat($this->medicalTestsIssueDate),
            'police_clearance_issue_date' => convertDateFormat($this->policeClearanceIssueDate),
            'code' => $this->generateUniqueCode()
        ], $filePaths);
    }

    /**
     * Create next of kin record
     */
    protected function createNextOfKin(int $driverId): void
    {
        $nextOfKin = SupabaseNextOfKin::create([
            'driver_id' => $driverId,
            'fullname' => trim($this->nextOfKinFullname),
            'mobile_number' => $this->appendCountryCode($this->nextOfKinMobileNumber),
            'address' => trim($this->nextOfKinAddress)
        ]);

        \Log::info("Next of kin created with ID: {$nextOfKin->id}");
    }

    /**
     * Clean up uploaded files in case of failure
     */
    protected function cleanupFiles(array $filePaths): void
    {
        foreach ($filePaths as $path) {
            if ($path && Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
            }
        }
    }

    protected function sendSMS($code, $driverMobileNumber){
        try {
                $twilio = new Client('**********************************', '10377b216991ea3e894d9d95ad834991');
    
                $twilio->messages->create(
                $this->appendCountryCode($driverMobileNumber),
                    [
                        'from' => '+12089041849',
                        'body' => 'Please use this code (' . $code . ') when you sign up on Impla mobile app. ',
                    ]
                );
    
              
            } catch (Exception $e) {
                session()->flash('error', 'Failed to send message: ' . $e->getMessage());
            }
    }

    protected function storeDriverFiles($driver)
    {
        // Store files
        $updates = [];
        
        if ($this->licenceFile) {
            $updates['licence_file_path'] = $this->licenceFile->store('uploads', 'public');
        }
        if ($this->driverProfilePhotoFile) {
            $updates['profile_photo_file'] = $this->driverProfilePhotoFile->store('uploads', 'public');
        }
        if ($this->nationalIdFile) {
            $updates['national_id_file_path'] = $this->nationalIdFile->store('uploads', 'public');
        }
        
        // Update driver record with file paths if any were stored
        if (!empty($updates)) {
            $driver->update($updates);
        }
    }

    protected function createTrainingRecords(int $driverId): void
    {
        try {
            $trainings = [];

            // Collect training records based on checkboxes and dates
            $trainingMappings = [
                ['checked' => $this->isImpalaInductionTrainingChecked, 'date' => $this->impalaInductionTrainingDate, 'type_id' => 1],
                ['checked' => $this->isImpalaDriversCodeOfConductChecked, 'date' => $this->impalaDriversCodeOfConductDate, 'type_id' => 2],
                ['checked' => $this->isImpalaSafeDrivingChecked, 'date' => $this->impalaSafeDrivingDate, 'type_id' => 3],
                ['checked' => $this->isImpalaChaufferDriversCourseChecked, 'date' => $this->impalaChaufferDriversCourseDate, 'type_id' => 4],
                ['checked' => $this->isImpalaCMEDVipDrivingCourseChecked, 'date' => $this->impalaCMEDVipDrivingCourseDate, 'type_id' => 5],
                ['checked' => $this->isImpalaDriversInHouseTrainingChecked, 'date' => $this->impalaDriversInHouseTrainingDate, 'type_id' => 6],
            ];

            foreach ($trainingMappings as $mapping) {
                if ($mapping['checked'] && !empty($mapping['date'])) {
                    $trainings[] = [
                        'driver_id' => $driverId,
                        'training_type_id' => $mapping['type_id'],
                        'training_date' => convertDateFormat($mapping['date'])
                    ];
                }
            }

            // Bulk create training records
            if (!empty($trainings)) {
                foreach ($trainings as $training) {
                    SupabaseTraining::create($training);
                }
                \Log::info("Created " . count($trainings) . " training records for driver {$driverId}");
            } else {
                \Log::info("No training records to create for driver {$driverId}");
            }

        } catch (Exception $e) {
            // Log error but don't throw exception since trainings are optional
            \Log::error('Failed to create training records:', [
                'driver_id' => $driverId,
                'error' => $e->getMessage()
            ]);
        }
    }
}

