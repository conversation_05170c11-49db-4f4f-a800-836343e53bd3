<?php

namespace App\Livewire\Driver;

use App\Models\Driver;
use App\Models\SupabaseModels\SupabaseDriver;
use Livewire\Component;
use Livewire\WithPagination;

class ViewDriversComponent extends Component
{

    use WithPagination;

    public $search = '';
    public $driverId = 0;
    public $sortBy = 'created_at';
    public $sortDir = 'DESC';
    public $perPage = 15;
    public $readyToLoad = false;
    
    public function mount()
    {
        $this->readyToLoad = true;
    }

    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField){
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : "ASC";
            return;
        }
        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function render()
    {
        $drivers = SupabaseDriver::with('driversLicence', 'gender')
            ->search($this->search)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perPage);
        return view('livewire.driver.view-drivers-component',
        ['drivers' => $drivers]); 
  }

  public function updateDriverId($id) {

    $this->driverId = $id;
}
 
public function delete(SupabaseDriver $driver) {
    
    $driver->delete();

    session()->flash('driver_deleted', 'Driver deleted successfully');

 }
}