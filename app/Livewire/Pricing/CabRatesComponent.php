<?php

namespace App\Livewire\Pricing;

use App\Models\CabRate;
use App\Models\SupabaseModels\SupabaseCabRate;
use Livewire\Component;
use Livewire\Attributes\Rule;
use Livewire\WithPagination;
use App\Models\SupabaseModels\SupabaseCarClassification;

class CabRatesComponent extends Component
{

    use WithPagination;

    public $selectedCarClassId;
    #[Rule('required')]
    public $amount;
    public $isEditingCabRateId;
    public $editSelectedCabRateId;
    #[Rule('required')]
    public $editAmount;
    public $isAddingCabRate = false;
    public $isDeletingCabRate = 0;

   



    public function render()
    {
        $filteredClasses = SupabaseCarClassification::pluck('classification', 'id');
        $carRates = SupabaseCabRate::latest()->paginate(5);
        return view('livewire.pricing.cab-rates-component',
        [
            'filteredClasses'=> $filteredClasses,
            'carRates'=> $carRates,
            'selectedCarClassId'=> $this->selectedCarClassId,
            'editSelectedCabRateId'=> $this->editSelectedCabRateId,
        ]
      );
    }


    public function addRate(){
        $this->isAddingCabRate = true;
        $amountValidation = $this->validateOnly('amount');
      
        SupabaseCabRate::create([
            'amount'=> $amountValidation['amount'],
            'car_classification_id' => $this->selectedCarClassId,
        ]);

        $this->amount = '';
        $this->selectedCarClassId = 0;

        session()->flash('cab_rate_created', 'Taxi rate created successfully!');
        $this->isAddingCabRate = false;
    }

    public function edit($id){
        $cabRateObj = SupabaseCabRate::where('id', $id)->first();
        $this->editSelectedCabRateId = $cabRateObj->car_classification_id;
        $this->editAmount = $cabRateObj->amount;
        $this->isEditingCabRateId = $id;   
    }

    public function saveChanges($id){
        $editAmountValidation = $this->validateOnly('editAmount');
    
        SupabaseCabRate::where('id', $id)->update(
            [
                'amount' => $editAmountValidation['editAmount'],
                'car_classification_id' => $this->editSelectedCabRateId
            ]
        );

        SupabaseCabRate::where('id', $id)->update(
            [
                'amount' => $editAmountValidation['editAmount'],
                'car_classification_id' => $this->editSelectedCabRateId
            ]
        );
        $this->isEditingCabRateId = 0;
    }

    public function cancelEdit(){
        $this->isEditingCabRateId = 0;
    }

    public function delete($id){
        $this->isDeletingCabRate = $id;
        SupabaseCabRate::where('id', $id)->delete();
        $this->isDeletingCabRate = 0;
        session()->flash('cab_rate_deleted', 'Taxi rate deleted successfully!');
    }
}
