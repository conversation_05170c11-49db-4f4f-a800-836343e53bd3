<?php

namespace App\Livewire\Pricing;

use App\Models\CarClassification;
use App\Models\CarModel;
use App\Models\SupabaseModels\SupabaseCarClassification;
use App\Models\SupabaseModels\SupabaseCarModel;
use Carbon\Carbon;
use Livewire\Attributes\Validate;
use Illuminate\Pagination\LengthAwarePaginator;


use Livewire\Component;
use Livewire\WithPagination;

class ViewCarModelsComponent extends Component
{

    use WithPagination;

    #[Validate('required|max:255')]
    public $classification;

     #[Validate('required|max:255')]
    public $name;

    #[Validate('required')]
    public $classification_id;

    public $isEditingId = 0;


    public $isEditinCarModelId = 0;

    #[Validate('required|max:255')]
    public $editClassification;

    #[Validate('required|max:255')]
    public $editCarModel;

    #[Validate('required')]
    public $selectedCarModelId;

    #[Validate('required|max:255')]
    public $carModelName;

    public $isAddingCarClassification = false;

    // public function render()
    // {
    //     $this->classification_id = 0;
    //     $filteredClasses = CarClassification::pluck('classification', 'id');
    //     $carClasses = CarClassification::latest()->get()    ;
    //     $carModels = CarModel::latest()->paginate(10);


    //     return view('livewire.pricing.view-car-models-component', 
    //     [
    //         'car_classes' => $carClasses,
    //         'classification_id' => $this->classification_id,
    //         'filteredClasses' => $filteredClasses,
    //         'carModels' => $carModels,
    //     ]
    //   );
    // }

    public function render()
    {
        $this->classification_id = 0;
        $filteredClasses = SupabaseCarClassification::pluck('classification', 'id');
        $carClasses = SupabaseCarClassification::latest()->get()    ;
        $carModels = SupabaseCarModel::latest()->paginate(10);


        return view('livewire.pricing.view-car-models-component', 
        [
            'car_classes' => $carClasses,
            'classification_id' => $this->classification_id,
            'filteredClasses' => $filteredClasses,
            'carModels' => $carModels,
        ]
      );
    }

    public function addCarClassification(){
        $this->isAddingCarClassification = true;
        $validation = $this->validateOnly('classification');
        
        SupabaseCarClassification::create($validation);

        $this->classification = '';
        $this->isAddingCarClassification = false;
        
        session()->flash('car_class_created', 'Car classification created successfully!');
    }


    public function saveCarModel(){
        $nameValidation = $this->validateOnly('name');
        $classValidation = $this->validateOnly('classification_id');
       
        // CarModel::create([
        //     'name'=> $nameValidation['name'],
        //     'classification_id' => $classValidation['classification_id']
        // ]);

        SupabaseCarModel::create([
            'name'=> $nameValidation['name'],
            'classification_id' => $classValidation['classification_id']
        ]);

        session()->flash('car_model_created', 'Car model created successfully!');
    }

    public function editCarClass($id){
        $this->editClassification = SupabaseCarClassification::where('id', $id)->first()->classification;
        $this->isEditingId  =  $id;
    }

    public function modifyCarModel($id){
       
        $carModelObj = SupabaseCarModel::where('id', $id)->first();
        $this->carModelName = $carModelObj->name;
        $this->selectedCarModelId = $carModelObj->car_classification_id;
        $this->isEditinCarModelId  =  $id;
    }

    public function saveCarModelChanges($id){
        $carModelNameValidation = $this->validateOnly('carModelName');
        $selectedCarModelIdValidation = $this->validateOnly('selectedCarModelId');

        // CarModel::where('id', $id)->update(
        //     [
        //         'name' => $carModelNameValidation['carModelName'],
        //         'car_classification_id' => $selectedCarModelIdValidation['selectedCarModelId'],
        //     ]
        // );

        SupabaseCarModel::where('id', $id)->update(
            [
                'name' => $carModelNameValidation['carModelName'],
                'car_classification_id' => $selectedCarModelIdValidation['selectedCarModelId'],
            ]
        );
        
        $this->isEditinCarModelId = 0;
    }

    public function cancelEdit(){
        $this->isEditingId = 0;
        $this->editClassification = '';
    }

    public function cancelCarModelEdit(){
        $this->isEditinCarModelId = 0;
        $this->editCarModel = '';
    }


    public function saveClassModification($id){
        $validation = $this->validateOnly('editClassification');
      
        // CarClassification::where('id', $id)->update(
        //     ['classification' => $validation['editClassification']]
        // );

        SupabaseCarClassification::where('id', $id)->update(
            ['classification' => $validation['editClassification']]
        );
        $this->isEditingId = 0;
        $this->editClassification = '';
    }

    public function deleteCarModel($id){
       
        SupabaseCarClassification::where('id', $id)->delete();
        session()->flash('car_model_deleted', 'Car model deleted successfully!');
    }
}