<?php

namespace App\Livewire\Pricing;

use App\Models\SupabaseModels\SupabaseCarClassification;
use App\Models\CarModel;
use App\Models\ShuttleCity;
use App\Models\SupabaseModels\SupabaseCity;
use App\Models\SupabaseModels\SupabaseShuttleCity;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Rule;
use App\Models\City;

class ShuttleCityComponent extends Component
{

    use WithPagination;

    #[Rule('required|max:255')]
    public $city;

    #[Rule('required|max:255')]
    public $editCity;

    #[Rule('required')]
    public $amount;

    #[Rule('required')]
    public $editAmount;

    public $selectedCity;
    public $selectedEditCity;
    public $selectedCarClass;
    public $selectedEditCarClass;
    public $selectedFilterCity = 0;

    public $isEditCityId = 0;
    public $isShuttleId = 0;

    public $filteredShuttleData = 0;

    public $shuttlePrices;



    public function render()
    {
        $this->selectedCity = 0;
        $this->selectedCarClass = 0;
        $carClasses = SupabaseCarClassification::all();
        $filteredCarClasses = SupabaseCarClassification::pluck('classification', 'id');
        $cities = SupabaseCity::orderByDesc('id')->get();
        $filteredCities = SupabaseCity::orderByDesc('id')->pluck('city_name', 'id');

        $this->shuttlePrices = SupabaseShuttleCity::latest()->paginate(10);
        $this->shuttlePrices = collect($this->shuttlePrices);  
        
        
        return view('livewire.pricing.shuttle-city-component', [
            'selectedCity' => $this->selectedCity,
            'selectedEditCity' => $this->selectedEditCity,
            'selectedCarClass' => $this->selectedCarClass,
            'selectedEditCarClass' => $this->selectedEditCarClass,
            'carClasses' => $carClasses,
            'filteredCarClasses' => $filteredCarClasses,
            'cities' => $cities,
            'filteredCities' => $filteredCities,
        ]);
    }

    public function getShuttlesProperty(): LengthAwarePaginator{
        return  SupabaseShuttleCity::latest()->paginate(10);
    }

    public function getFilteredShuttlesProperty(): LengthAwarePaginator{
        return  SupabaseShuttleCity::where('city_id', $this->selectedFilterCity)
        ->paginate(10);
    }


    public function addCity(){
        $validation = $this->validateOnly('city');

        // Check for duplicate city (case-insensitive)
        $exists = SupabaseCity::whereRaw('LOWER(city_name) = ?', [strtolower($validation['city'])])->exists();
        if ($exists) {
            session()->flash('city_error', 'City already exists!');
            return;
        }

        SupabaseCity::create(
            ['city_name' => $validation['city']]
        );

        $this->city = '';
        // No need to call $this->emit or $this->render() here; Livewire will re-render automatically.

        session()->flash('city_saved', 'City added successfully!');
    }

    public function saveShuttleRate(){
        sleep(1);
        $amountValidation = $this->validateOnly('amount');

        

        SupabaseShuttleCity::create([
            'car_classification_id'=> $this->selectedCarClass,
            'city_id'=> $this->selectedCity,
            'price_per_km' => $amountValidation['amount'],
        ]);

        $this->amount = '';
        session()->flash('shuttle_rate_created', 'Shuttle rate created successfully!');
    }

    public function modifyCity($id){
        $this->editCity = SupabaseCity::where('id', $id)->first()->city_name;
        $this->isEditCityId  =  $id;
    }

    public function modifyShuttleRate($id){
        $shuttleRateObj = SupabaseShuttleCity::where('id', $id)->first();
        $this->selectedEditCarClass = $shuttleRateObj->car_classification_id;
        $this->selectedEditCity = $shuttleRateObj->city_id;
        $this->editAmount = $shuttleRateObj->price_per_km;
        $this->isShuttleId  =  $id;
    }

    public function saveModifiedCityChanges($id){
        $validation = $this->validateOnly('editCity');
     
        SupabaseCity::where('id', $id)->update(
            ['city_name' => $validation['editCity']]
        );
        $this->isEditCityId = 0;
        $this->editCity = '';
    }

    public function saveModifiedShuttleRateChanges($id){
        $validation = $this->validateOnly('editAmount');
        // SupabaseShuttleCity::where('id', $id)->update(
        //     [
        //         'price_per_km' => $validation['editAmount'],
        //         'car_classification_id'=> $this->selectedEditCarClass,
        //         'city_id'=> $this->selectedEditCity,
        //     ]
        // );

        SupabaseShuttleCity::where('id', $id)->update(
            [
                'price_per_km' => $validation['editAmount'],
                'car_classification_id'=> $this->selectedEditCarClass,
                'city_id'=> $this->selectedEditCity,
            ]
        );
        $this->isShuttleId = 0;
        $this->editAmount = '';
    }

    public function cancelEditCity() {
        $this->editCity = '';
        $this->isEditCityId  =  0;
    }

    public function cancelEditShuttleRate() {
        $this->selectedEditCarClass = 0;
        $this->selectedEditCity = 0;
        $this->editAmount = '';
        $this->isShuttleId = 0;
    }

    public function update(){
        dd('fdsfdsfsf');
    }

    public function resetFilter(){
       
        $this->selectedFilterCity = 0;
    }

    public function filterByCity(){
    
      return $this->getFilteredShuttlesProperty();
    }

    public function deleteCity($id){
        sleep(1);
        SupabaseCity::where('id', $id)->delete();
        session()->flash('city_deleted', 'City deleted successfully!');
    }
    public function deleteShuttleRate($id){
        sleep(1);
        SupabaseShuttleCity::where('id', $id)->delete();
        session()->flash('shuttle_rate_deleted', 'Shuttle rate deleted successfully!');
    }


}
