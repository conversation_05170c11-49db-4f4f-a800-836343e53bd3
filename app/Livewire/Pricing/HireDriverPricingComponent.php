<?php

namespace App\Livewire\Pricing;

use App\Models\HireDriverRate;
use App\Models\HireDriverType;
use App\Models\SupabaseModels\SupabaseHireDriverRate;
use App\Models\SupabaseModels\SupabaseHireDriverType;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Rule;

class HireDriverPricingComponent extends Component
{

    use WithPagination;

    #[Rule('required|max:255')]
    public $hireDriverType;

    #[Rule('required|max:255')]
    public $editHireDriverType;

    #[Rule('required')]
    public $amount;


    #[Rule('required')]
    public $editHireAmount;

    public $selectedHiringTypeId;

    public $selectedHireDriverRateId;

    public $selectedEditHireDriverRateId = 0;

    public $isEditingDriverHireTypeId;
    public $isEditingDriverRateId;

    public function render()
    {
        $this->selectedHiringTypeId = 0;
        $this->selectedHireDriverRateId = 0;
        $filteredHiringTypes = SupabaseHireDriverType::pluck('hire_type', 'id');
        $hiringTypes = SupabaseHireDriverType::latest()->get()    ;
        $hireDriverRates = SupabaseHireDriverRate::latest()->paginate(10);

        return view('livewire.pricing.hire-driver-pricing-component', 
            [  
                'selectedHireDriverRateId'=> $this->selectedHireDriverRateId,
                'selectedHiringTypeId'=> $this->selectedHiringTypeId,
                'filteredHiringTypes'=> $filteredHiringTypes,
                'hiringTypes'=> $hiringTypes,
                'hireDriverRates'=> $hireDriverRates,
            ]
        );
    }

    public function addHireDriverType(){
        $validation = $this->validateOnly('hireDriverType');
        // SupabaseHireDriverType::create(
        //     ['hire_type' => $validation['hireDriverType']]
        // );

        SupabaseHireDriverType::create(
            ['hire_type' => $validation['hireDriverType']]
        );

        $this->hireDriverType = '';
        session()->flash('hire_driver_type_created', 'Driver hiring type created successfully!');
    }

    public function saveHiringRate(){
        $amountValidation = $this->validateOnly('amount');

        // HireDriverRate::create([
        //     'hire_driver_type_id'=> $this->selectedHireDriverRateId,
        //     'amount' => $amountValidation['amount'],
        // ]);

        SupabaseHireDriverRate::create([
            'hire_driver_type_id'=> $this->selectedHireDriverRateId,
            'amount' => $amountValidation['amount'],
        ]);

        $this->amount = '';

        session()->flash('driver_hiring_rate_created', 'Driver hiring rate created successfully!');
    }

    public function modifyHireDriverType($id){
        $this->editHireDriverType = SupabaseHireDriverType::where('id', $id)->first()->hire_type;
        $this->isEditingDriverHireTypeId  =  $id;
    }

    public function editHireDriverRate($id){
        $hireDriverRateObj = SupabaseHireDriverRate::where('id', $id)->first();
        $this->selectedEditHireDriverRateId = $hireDriverRateObj->hire_driver_type_id;
        $this->editHireAmount = $hireDriverRateObj->amount;
        $this->isEditingDriverRateId = $id;
    }

    public function saveHireRateChanges($id){
        $editHireAmountValidation = $this->validateOnly('editHireAmount');
        $hireDriverRateObj = SupabaseHireDriverRate::where('id', $id)->first();
        // HireDriverRate::where('id', $id)->update(
        //     [
        //         'hire_driver_type_id' => $hireDriverRateObj->hire_driver_type_id,
        //         'amount' => $editHireAmountValidation['editHireAmount'],
        //     ]
        // );

        SupabaseHireDriverRate::where('id', $id)->update(
            [
                'hire_driver_type_id' => $hireDriverRateObj->hire_driver_type_id,
                'amount' => $editHireAmountValidation['editHireAmount'],
            ]
        );
        $this->isEditingDriverRateId = 0;
    }

    public function saveHireDriverType($id){
        $validation = $this->validateOnly('editHireDriverType');
        // HireDriverType::where('id', $id)->update(
        //     ['hire_type' => $validation['editHireDriverType']]
        // );

        SupabaseHireDriverType::where('id', $id)->update(
            ['hire_type' => $validation['editHireDriverType']]
        );
        $this->isEditingDriverHireTypeId = 0;
        $this->editHireDriverType = '';
    }

    public function cancelEditHireType(){
        $this->isEditingDriverHireTypeId = 0;
        $this->editHireDriverType = '';
    }

    public function cancelEditHireDriverRate(){
        $this->isEditingDriverRateId = 0;
        $this->editHireAmount = '';
    }

    public function deleteHireDriverType($id){
        SupabaseHireDriverType::where('id', $id)->delete();
        session()->flash('hire_driver_type_deleted', 'Driver hiring type deleted successfully!');
    }
    public $isDeletingHireDriverRate = 0;

    public function deleteHireDriverRate($id){
        $this->isDeletingHireDriverRate = $id;
        SupabaseHireDriverRate::where('id', $id)->delete();
        $this->isDeletingHireDriverRate = 0;
        session()->flash('hire_driver_rate_deleted', 'Driver hiring rate deleted successfully!');
    }
}
