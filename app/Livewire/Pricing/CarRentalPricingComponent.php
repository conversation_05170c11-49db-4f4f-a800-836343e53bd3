<?php

namespace App\Livewire\Pricing;

use App\Models\RentalPrice;
use App\Models\SupabaseModels\SupabaseRentalPrice;
use Livewire\Component;
use App\Models\CarClassification;
use Livewire\Attributes\Rule;
use Livewire\WithPagination;

class CarRentalPricingComponent extends Component
{

    use WithPagination;

    #[Rule('required')]
    public $amount;

    #[Rule('required')]
    public $deposit;

    public $selectedCarClassId;

    public $isEditinCarModelId = 0;
    public $editSelectedCarModelId = 0;

    #[Rule('required')]
    public $editAmount;


    #[Rule('required')]
    public $editDeposit;

    public $isAddingCarRentalPrice = false;
    public $isDeletingCarRentalPrice = 0;

    public function render()
    {
        $filteredClasses = CarClassification::pluck('classification', 'id');
        $rentalModels = SupabaseRentalPrice::latest()->paginate(5);
        return view('livewire.pricing.car-rental-pricing-component',  
        [
            'carRentals'=> $rentalModels,
            'selectedCarClassId' => $this->selectedCarClassId,
            'filteredClasses' => $filteredClasses,
            'editSelectedCarModelId'=> $this->editSelectedCarModelId,
        ]
      );
    }


    public function addRate()
    {
        $this->isAddingCarRentalPrice = true;

        $amountValidation = $this->validateOnly('amount');
      
        // SupabaseRentalPrice::create([
        //     'amount'=> $amountValidation['amount'],
        //     'car_classification_id' => $this->selectedCarClassId,
        //     'deposit' => $this->deposit,
        // ]);

        SupabaseRentalPrice::create([
            'amount'=> $amountValidation['amount'],
            'car_classification_id' => $this->selectedCarClassId,
            'deposit' => $this->deposit,
        ]);

        $this->amount = '';
        $this->deposit = '';
        $this->selectedCarClassId = 0;

        session()->flash('car_model_created', 'Car rental rate created successfully!');

        $this->isAddingCarRentalPrice = false;
    }
    public function edit($id){
        $carModelObj = SupabaseRentalPrice::where('id', $id)->first();
        $this->editSelectedCarModelId = $carModelObj->car_classification_id;
        $this->editAmount = $carModelObj->amount;
        $this->editDeposit = $carModelObj->deposit;
        $this->isEditinCarModelId = $id;   
    }

    public function saveChanges($id){
        $editAmountValidation = $this->validateOnly('editAmount');
        $editDepositValidation = $this->validateOnly('editDeposit');
    
        // SupabaseRentalPrice::where('id', $id)->update(
        //     [
        //         'amount' => $editAmountValidation['editAmount'],
        //         'deposit' => $editDepositValidation['editDeposit'],
        //         'car_classification_id' => $this->editSelectedCarModelId
        //     ]
        // );

        SupabaseRentalPrice::where('id', $id)->update(
            [
                'amount' => $editAmountValidation['editAmount'],
                'deposit' => $editDepositValidation['editDeposit'],
                'car_classification_id' => $this->editSelectedCarModelId
            ]
        );
        
        $this->isEditinCarModelId = 0;
    }

    public function cancelEdit(){
        $this->isEditinCarModelId = 0;
    }

    public function delete($id){
        $this->isDeletingCarRentalPrice = $id;
        SupabaseRentalPrice::where('id', $id)->delete();
        $this->isDeletingCarRentalPrice = 0;
        session()->flash('car_model_deleted', 'Car rental rate deleted successfully!');
    }
}
