<?php

namespace App\Livewire\HireDriver;

use App\Models\SupabaseModels\SupabaseDriver;
use App\Models\SupabaseModels\SupabaseDriverBooking;
use Livewire\Component;
use Livewire\WithPagination;

class ViewHireDriverDetailsComponent extends Component
{

    use WithPagination;
    public SupabaseDriverBooking $book;
    public $driverName;
    public $driverImage;
    public  $supabaseDriver;
    public $assignedDriverId;

    public $isDriverAssigned = false;

    public function render()
    {
        $drivers = SupabaseDriver::latest()->paginate(20);
        return view('livewire.hire-driver.view-hire-driver-details-component',  compact('drivers'));
    }

    // public function updated($property)
    // {
    //     dd($property);
    //     if ($property === $this->isDriverAssigned) {
    //         // Trigger the notification
    //         if($this->isDriverAssigned === true){
    //             $this->dispatch('showNotification');
    //         }
    //     }
    // }

    public function mount($book)
    {
        $this->book = $book;
        if($this->book->driver_id !== null) {
           $this->assignedDriverId = $this->book->driver_id;
           $this->supabaseDriver = SupabaseDriver::find($this->book->driver_id);
        }

        //  $this->drivers = SupabaseDriver::latest()->paginate();

        return view('livewire.hire-driver.view-hire-driver-details-component');
    }

    public function assignementStatus($status){
        return $status;
    }

    public function setAssignedDriverDetails($driverId){
        $driver = SupabaseDriver::find($driverId);
        $this->supabaseDriver = $driver;
        $this->driverName = "{$driver->driver_firstname} {$driver->driver_lastname}";
        $this->driverImage = $driver->profile_photo_file;

        $this->dispatch('showNotification', [
            'driverName' => $this->driverName,
            'driverImage' => $this->driverImage
        ]);
    }

    public function getDrivers(){
        $drivers = SupabaseDriver::latest()->paginate();
        return $drivers;
    }

    public function assignDriver($driverId, $tripId){
        $tripData = SupabaseDriverBooking::find($tripId);
        $this->supabaseDriver = SupabaseDriver::find($driverId);
        $tripData->driver_id = $driverId;
        $tripData->hire_driver_status_id = 2;

        $tripData->save();
        $this->isDriverAssigned = true;
        $this->setAssignedDriverDetails($driverId);
        $this->assignementStatus('Assigned');
        session()->flash('hired_driver_assigned', 'Driver assigned successful.');
    }
}
