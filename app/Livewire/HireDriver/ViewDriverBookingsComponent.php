<?php

namespace App\Livewire\HireDriver;

use App\Exports\DriverBookingsExport;
use App\Models\SupabaseModels\SupabaseDriverBooking;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class ViewDriverBookingsComponent extends Component
{
    use WithPagination;

    public $bookId = 0;
    public $search = '';
    public $perPage = 5;

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function export()
    {
        return Excel::download(new DriverBookingsExport, 'driver-bookings.xlsx');
    }

    public function render()
    {
        $bookings = SupabaseDriverBooking::whereHas('client', function($query) {
            $query->where('name', 'like', '%' . $this->search . '%');
        })
        ->orderBy('id', 'desc')
        ->paginate($this->perPage);

        return view('livewire.hire-driver.view-driver-bookings-component', ['bookings' => $bookings]);
    }

    public function updateBookId($id)
    {
        $this->bookId = $id;
    }

    public function delete(SupabaseDriverBooking $book)
    {
        $book->delete();
        session()->flash('hire_driver_deleted', 'Booking was successfully deleted');
    }
}