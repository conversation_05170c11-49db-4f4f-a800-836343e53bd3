<?php

namespace App\Livewire\Payments;

use App\Exports\PaymentsExport;
use App\Models\SupabaseModels\SupabasePayment;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class PaymentsComponent extends Component
{

    use WithPagination;

    public $search = '';
    public $driverId = 0;
   

    public $sortBy = 'created_at';
    public $sortDir = 'DESC';
    public $perPage = 20;

    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField){
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : "ASC";
            return;
        }
        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }

    public function updatedSearch(){
        $this->resetPage();
    }

    public function render()
    {
        $payments = SupabasePayment::with('payable')
            ->search($this->search)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perPage);
        return view('livewire.payments.payments-component',
        ['payments' => $payments]); 
  }

  public function updateDriverId($id) {

    $this->driverId = $id;
}
 
// public function delete(Driver $driver) {
    
//     $driver->delete();

//     session()->flash('driver_deleted', 'Driver deleted successfully');

//  }

 public function export()
 {
     return Excel::download( new PaymentsExport, 'payments.xlsx');
 }
 
}
