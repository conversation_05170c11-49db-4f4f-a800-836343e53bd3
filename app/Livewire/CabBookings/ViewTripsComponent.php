<?php

namespace App\Livewire\CabBookings;

use App\Exports\TripsExport;
use App\Models\SupabaseModels\SupabaseTaxiBooking;
use Livewire\Component;
use Livewire\WithoutUrlPagination;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class ViewTripsComponent extends Component
{

    use WithPagination;

    public $tripId = 0;
    public $search = '';
    public $sortBy = 'created_at';
    public $sortDir = 'DESC';
    public $perPage = 10;

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function export()
    {
        return Excel::download(new TripsExport, 'taxi-trips.xlsx');
    }

    public function render()
    {
        $trips = SupabaseTaxiBooking::whereHas('client', function($query) {
            $query->where('name', 'like', '%' . $this->search . '%');
        })
        ->orderBy('id', 'desc')
        ->paginate($this->perPage);

        return view('livewire.cab-bookings.view-trips-component', ['trips' => $trips]);
    }

    public function updateTripId($id) {

        $this->tripId = $id;
    }



    public function delete(SupabaseTaxiBooking $trip) {
    
        $trip->delete();
    
        session()->flash('trip_deleted', 'Trip was successfully deleted');
    
    }
}
