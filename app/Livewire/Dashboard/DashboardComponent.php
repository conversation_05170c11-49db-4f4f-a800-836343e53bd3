<?php

namespace App\Livewire\Dashboard;

use App\Models\SupabaseModels\SupabaseCarRental;
use App\Models\SupabaseModels\SupabaseDriverBooking;
use App\Models\SupabaseModels\SupabaseShuttleBooking;
use App\Models\SupabaseModels\SupabaseTaxiBooking;
use Livewire\Component;

class DashboardComponent extends Component
{
    // public $cabBookings;
    // public $shuttleBookings;

    public function render()
    {

        $taxiBookingCount = SupabaseTaxiBooking::where('trip_status_id', 1)->count();
        $shuttleBookingCount = SupabaseShuttleBooking::where('trip_status_id', 1)->count();
        $driverBookingCount = SupabaseDriverBooking::where('hire_driver_status_id', 1)->count();
        $carRentalBookingCount = SupabaseCarRental::where('rental_status_id', 1)->count();

        $cabBookings = SupabaseTaxiBooking::where('trip_status_id', 1)
        ->latest()
        ->take(5)
        ->get();

        $shuttleBookings = SupabaseTaxiBooking::where('trip_status_id', 1)
        ->latest()
        ->take(5)
        ->get();

        return view('livewire.dashboard.dashboard-component', 
        [
         'taxiBookingCount' => $taxiBookingCount,
         'shuttleBookingCount' => $shuttleBookingCount, 
         'driverBookingCount' => $driverBookingCount,
         'carRentalBookingCount' => $carRentalBookingCount,
         'cabBookings' => $cabBookings,
         'shuttleBookings' => $shuttleBookings ]);
    }
}
