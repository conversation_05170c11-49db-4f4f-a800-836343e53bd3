<?php

namespace App\Livewire\Reports;

use App\Models\SupabaseModels\SupabaseAccidentStatus;
use App\Models\SupabaseModels\SupabaseAccidentType;
use Livewire\Component;
use App\Models\AccidentType;
use App\Models\AccidentStatus;

class EditReportComponent extends Component
{

    public $accidentDate2;
    public $selectedAccidentType = 0;

    public $selectedStatusId = 0;
    public $accidentDetails;

    public $accidentPoliceReport;

    public function render()
    {
        $accidentTypes = SupabaseAccidentType::all();
        $filteredAccidentTypes = SupabaseAccidentType::pluck("type","id");
        $accidentStatus = SupabaseAccidentStatus::all();
        $filtteredAccidentStatus = SupabaseAccidentStatus::pluck("status","id");

        return view('livewire.reports.edit-report-component', [
            'accidentTypes' => $accidentTypes,
            'filteredAccidentTypes' => $filteredAccidentTypes,
            'accidentStatus' => $accidentStatus,
            'filtteredAccidentStatus' => $filtteredAccidentStatus,
        ]);
    }
}
