<?php

namespace App\Livewire\Reports;

use App\Models\AccidentReport;
use App\Models\AccidentStatus;
use App\Models\AccidentType;
use App\Models\Driver;
use App\Models\SupabaseModels\SupabaseAccidentReport;
use App\Models\SupabaseModels\SupabaseAccidentStatus;
use App\Models\SupabaseModels\SupabaseAccidentType;
use App\Models\SupabaseModels\SupabaseDriver;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Spatie\LivewireFilepond\WithFilePond;
use Livewire\Attributes\On;

class AccidentReportComponent extends Component
{
    use withFileUploads;
    use WithFilePond;
    use WithPagination;

    public $reportToDelete = null;

    // public $searchDriver = '';
    public $accidentDate;
    public $selectedAccidentType = 0;

    public $accidentDetails;
    public $selectedStatusId = 0;
    public $accidentPoliceReport;

    public $isResult = false;

    public $filterAccidentDate;

    public $driver_id;

    public $isDateFilterOn = false;

    public $isShow = false;

    public $editReportId = 0;

    public $editAccidentDate;
    public $editSelectedAccidentType;
    public $editAccidentDetails;
    public $editAccidentPoliceReport;
    public $editSelectedStatusId;

    public $editDriverDisplayName;

    public $driverId;

    public $driverProfile;

    public $driversLicenceNumber;
    public $viewAccidentType;
    public $viewAccidentStatus;
    public $viewReportComments;

    public function render()
    {
        $accidentTypes = SupabaseAccidentType::all();
        $filteredAccidentTypes = SupabaseAccidentType::pluck("type","id");
        $accidentStatus = SupabaseAccidentStatus::all();
        $filtteredAccidentStatus = SupabaseAccidentStatus::pluck("status","id");

        return view('livewire.reports.accident-report-component', [
            'accidentTypes'=> $accidentTypes,
            'filteredAccidentTypes'=> $filteredAccidentTypes,
            'accidentStatus'=> $accidentStatus,
            'filtteredAccidentStatus'=> $filtteredAccidentStatus,
        ]);
    }

    public function save(){

        $this->validate([
            'driver_id' => 'required',
            'accidentDate' => 'required|date',
            'selectedAccidentType' => 'required|not_in:0',
            'accidentDetails' => 'required|string',
            'selectedStatusId' => 'required|not_in:0',
        ], [
            'driver_id.required' => 'Please search and select a driver before saving.',
            'accidentDate.required' => 'The accident date is required.',
            'selectedAccidentType.required' => 'Please select an accident type.',
            'selectedAccidentType.not_in' => 'Please select a valid accident type.',
            'accidentDetails.required' => 'The accident details are required.',
            'selectedStatusId.required' => 'Please select a status.',
            'selectedStatusId.not_in' => 'Please select a valid status.',
        ]);


        $driverProfilePhotoFilePath = null;

        if($this->accidentPoliceReport !==null) {
            $driverProfilePhotoFilePath = $this->accidentPoliceReport->store('uploads', 'public');
        }

        $accidentDateVal = convertDateFormat($this->accidentDate);

        // AccidentReport::create([
        //     'driver_id' => $this->driver_id,
        //     'accident_status_id' => $this->selectedStatusId,
        //     'accident_type_id' => $this->selectedAccidentType,
        //     'details' => $this->accidentDetails,
        //     'report_file' => $driverProfilePhotoFilePath,
        //     'accident_date' => $accidentDateVal,
        // ]);

        SupabaseAccidentReport::create([
            'driver_id' => $this->driver_id,
            'accident_status_id' => $this->selectedStatusId,
            'accident_type_id' => $this->selectedAccidentType,
            'details' => $this->accidentDetails,
            'report_file' => $driverProfilePhotoFilePath,
            'accident_date' => $accidentDateVal,
        ]);

        session()->flash('report_saved', 'Accideent report created successfully!');
    }

    public function getReportsProperty(): LengthAwarePaginator {
        return SupabaseAccidentReport::latest()->paginate(5);
    }

    public function getFilteredReportsProperty(): LengthAwarePaginator {
        $dateVal = convertDateFormat($this->filterAccidentDate);
        return SupabaseAccidentReport::whereDate('accident_date', $dateVal)->latest()->paginate(5);
    }

    #[On('broadcast-driver-id')]
    public function getDriverId(SupabaseDriver $driver){
        $this->driver_id = $driver->id;
    }

    public function filterByDate() {
        $this->isDateFilterOn = true;
        return $this->getFilteredReportsProperty();
    }

    public function resetDateFilter() {
        $this->isDateFilterOn = false;
    }

    public function showEdit(){
        $this->isShow = true;
    }

    public function saveModifiedReport(){

        if($this->editAccidentPoliceReport !== null) {
            $editDriverProfilePhotoFilePath = $this->editAccidentPoliceReport->store('uploads', 'public');
            // SupabaseAccidentReport::where('id', $this->editReportId)->update(
            //     [
            //         'driver_id' => $this->driverId,
            //         'accident_status_id' => $this->editSelectedStatusId,
            //         'accident_type_id' => $this->editSelectedAccidentType,
            //         'details' =>  $this->editAccidentDetails,
            //         'report_file' => $editDriverProfilePhotoFilePath,
            //         'accident_date' => convertDateFormat($this->editAccidentDate),
            //     ]
            // );

            SupabaseAccidentReport::where('id', $this->editReportId)->update(
                [
                    'driver_id' => $this->driverId,
                    'accident_status_id' => $this->editSelectedStatusId,
                    'accident_type_id' => $this->editSelectedAccidentType,
                    'details' =>  $this->editAccidentDetails,
                    'report_file' => $editDriverProfilePhotoFilePath,
                    'accident_date' => convertDateFormat($this->editAccidentDate),
                ]
            );
            $this->editReportId = 0;
        } else {
            SupabaseAccidentReport::where('id', $this->editReportId)->update(
                [
                    'driver_id' => $this->driverId,
                    'accident_status_id' => $this->editSelectedStatusId,
                    'accident_type_id' => $this->editSelectedAccidentType,
                    'details' =>  $this->editAccidentDetails,
                    'accident_date' => convertDateFormat($this->editAccidentDate),
                ]
            );
            $this->editReportId = 0;
        }
    }

    public function edit($id){
        $reportDetails = SupabaseAccidentReport::find($id);
        $this->editDriverDisplayName = $reportDetails->driver->driver_firstname .' '. $reportDetails->driver->driver_lastname;
        $this->editAccidentDate =  convertToDefaultDateFormat($reportDetails->accident_date);
        $this->editSelectedAccidentType = $reportDetails->accident_type_id;
        $this->editAccidentDetails = $reportDetails->details;
        $this->editSelectedStatusId = $reportDetails->accident_status_id;
        $this->driverId = $reportDetails->driver->id;
        $this->editReportId = $id;
    }

    public function viewReport($id){

    
        $reportDetails = SupabaseAccidentReport::find($id);
        // $this->editDriverDisplayName = $reportDetails->driver->driver_firstname .' '. $reportDetails->driver->driver_lastname;
        $this->editDriverDisplayName = $reportDetails->driver->driver_firstname .' '. $reportDetails->driver->driver_lastname;
        $this->editAccidentDate =  convertDateToHumanFormat($reportDetails->accident_date);
        $this->viewAccidentType = $reportDetails->accidentType->type;
        $this->viewReportComments = $reportDetails->details;
        $this->viewAccidentStatus = $reportDetails->accidentStatus->status;
        $this->driverId = $reportDetails->driver->id;
        $this->driverProfile = $reportDetails->driver->profile_photo_file;
        $this->driversLicenceNumber = $reportDetails->driver->drivers_licence;
        // $this->editReportId = $id;
    }

    public function cancelEdit(){
        $this->editReportId = 0;
    }

    public function confirmDelete($reportId)
    {
        $this->reportToDelete = $reportId;
        $this->dispatch("show-delete-modal");
    }

    public function deleteReport()
    {
        if ($this->reportToDelete) {
            try {
                $report = SupabaseAccidentReport::find($this->reportToDelete);
                if ($report) {
                    $report->delete();
                    session()->flash("report_deleted", "Report has been deleted successfully.");
                }
                $this->reportToDelete = null;
                $this->dispatch("hide-delete-modal");
            } catch (\Exception $e) {
                session()->flash("error", "Failed to delete report.");
            }
        }
    }

}