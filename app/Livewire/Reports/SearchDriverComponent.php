<?php

namespace App\Livewire\Reports;

use App\Models\SupabaseModels\SupabaseDriver;
use Livewire\Component;
use App\Models\Driver;

class SearchDriverComponent extends Component
{

    public $searchDriver = '';

    public $driverProfilePic;
    public $driverName;
    public $driverLicenceNumber;

    public $driverId = 0;

    public function render()
    {
        if ($this->searchDriver) {
            sleep(1);
        }
        $searchResults = SupabaseDriver::when($this->searchDriver, function ($query, $searchDriver) {
            $query->latest()->whereRaw('LOWER(driver_firstname) LIKE ?', ['%' . strtolower($searchDriver) . '%'])
            ->orWhereRaw('LOWER(driver_lastname) LIKE ?', ['%' . strtolower($searchDriver) . '%'])->limit(5);
        })->get();

        // $searchResults = Driver::latest()->where('driver_firstname', 'like', "%{$this->searchDriver}%")
        // ->orWhere('driver_lastname', 'like', "%{$this->searchDriver}%")->limit(5)
        // ->get();
        return view('livewire.reports.search-driver-component',
        [
            'searchResults'=> $searchResults
        ]
      );
    }



    public function selectDriver(SupabaseDriver $driver){
        $this->searchDriver = '';
        $this->driverId = $driver->id;
        $this->driverProfilePic = $driver->profile_photo_file;
        $this->driverName = $driver->driver_firstname . '  ' . $driver->driver_lastname;
        $this->driverLicenceNumber = $driver->drivers_licence;
        $this->dispatch('broadcast-driver-id', $driver);

    }
}
