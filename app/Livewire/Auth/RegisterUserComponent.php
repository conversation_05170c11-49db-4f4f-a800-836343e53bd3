<?php

namespace App\Livewire\Auth;

use App\Models\SupabaseModels\SupabaseUser;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Illuminate\Support\Facades\Hash;

class RegisterUserComponent extends Component
{

    public $name;
    public $email;
    public $password;
    public $password_confirmation;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email',
        'password' => 'required|min:6',
        'password_confirmation' => 'required|min:6',
    ];

    public function register()
    {
        $this->validate();

      $user =  SupabaseUser::updateOrCreate([
            'name' => $this->name,
            'email' => $this->email,
            'password' => Hash::make($this->password),
        ]);

        Auth::login($user);

        session()->flash('success', 'Registration successful! You can now log in.');

        return redirect()->route('login');
    }


    public function render()
    {
        return view('livewire.auth.register-user-component');
    }
}
