<?php

namespace App\Livewire\Users;

use App\Models\SupabaseModels\SupabaseUser;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Hash;

class UsersComponent extends Component
{

    use WithPagination;
    public $search = '';
    public $userId = 0;
   

    public $sortBy = 'created_at';
    public $sortDir = 'DESC';
    public $perPage = 5;

    public $name;
    public $email;
    public $password;
    public $password_confirmation;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email',
        'password' => 'required|min:6',
        'password_confirmation' => 'required|min:6',
    ];

    public function register()
    {
        $this->validate();

      $user =  SupabaseUser::updateOrCreate([
            'name' => $this->name,
            'email' => $this->email,
            'password' => Hash::make($this->password),
        ]);

        $this->resetFields();
        session()->flash('success', 'Registration successful! You can now log in.');       
    }


    public function resetFields()
    {
        $this->name = '';
        $this->email = '';
        $this->password = '';
        $this->password_confirmation = '';
        // Reset any other fields you have in your form
    }




    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField){
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : "ASC";
            return;
        }
        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }



    public function updatedSearch(){
        $this->resetPage();
    }




    public function render()
    {
        $users = SupabaseUser::search($this->search)
            ->orderBy($this->sortBy, $this->sortDir)
            ->paginate($this->perPage);
        
        return view('livewire.users.users-component', ['users' => $users]);
    }

    public function updateDriverId($id) {

        $this->userId = $id;
    }

    public function delete(SupabaseUser $user) {
    
        $user->delete();
    
        session()->flash('user_deleted', 'User deleted successfully');
    
     }
}
