<?php

namespace App\Livewire\ShuttleBookings;

use App\Exports\ShuttleTripsExport;
use App\Models\SupabaseModels\SupabaseShuttleBooking;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class ViewShuttleTripsComponent extends Component
{

    use WithPagination;

    public $shuttleId = 0;
    public $search = '';
    public $perPage = 10;

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function export()
    {
        return Excel::download(new ShuttleTripsExport, 'shuttle-trips.xlsx');
    }

    public function render()
    {
        $shuttles = SupabaseShuttleBooking::whereHas('client', function($query) {
            $query->where('name', 'like', '%' . $this->search . '%');
        })
        ->orderBy('id', 'desc')
        ->paginate($this->perPage);

        return view('livewire.shuttle-bookings.view-shuttle-trips-component', ['shuttles' => $shuttles]);
    }

    public function updateShuttleTripId($id) {

        $this->shuttleId = $id;
    }

    public function delete(SupabaseShuttleBooking $trip) {
    
        $trip->delete();
    
        session()->flash('shuttle_trip_deleted', 'Trip was successfully deleted');
    
    }
}

