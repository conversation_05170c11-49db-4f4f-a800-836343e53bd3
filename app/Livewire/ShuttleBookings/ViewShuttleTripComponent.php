<?php

namespace App\Livewire\ShuttleBookings;

use App\Models\SupabaseModels\SupabaseDriver;
use App\Models\SupabaseModels\SupabaseShuttleBooking;
use Livewire\Component;
use Livewire\Attributes\On;
use App\Models\Driver;
use Illuminate\Support\Facades\Http;
use App\Models\SupabaseModels\SupabaseDriverAuth;
use App\Models\SupabaseModels\SupabaseClient;
class ViewShuttleTripComponent extends Component
{
    public SupabaseShuttleBooking $trip;

    public $originLat;
    public $originLng;
    public $destinationLat;
    public $destinationLng;

    public $icon;

    public $tripId;

    public $driver_id = 0;



    public function mount($trip)
    {
        $this->trip = $trip;
        $this->originLat = floatval($trip->pick_up_latitude);
        $this->originLng = floatval($trip->pick_up_longitude);
        $this->destinationLat =  floatval($trip->drop_off_latitude);
        $this->destinationLng = floatval($trip->drop_off_longitude);
        $this->icon = $trip->client->supabase_image_url;
        $this->tripId = $trip->id;
       
        // dd( $this->formatBytes(File::size('storage/' . 'uploads/NWs9pLwP6rH9CpXmojxpOfhrwY5L0kh0OZR8jGsP.jpg'), 2));
        return view('livewire.shuttle-bookings.view-shuttle-trip-component');
    }
    

    public function render()
    {
        
        return view('livewire.shuttle-bookings.view-shuttle-trip-component');
    }

    #[On('broadcast-assigned-shuttle-driver-id')]
    public function getShuttleDriverId(SupabaseDriver $driver){
       
        $this->driver_id = $driver->id;
    }


    public function sendNotification()
    {
        $driverAuthData = SupabaseDriverAuth::where('driver_id', $this->driver_id)->first();
        if (!$driverAuthData) {
            session()->flash('error', 'Driver authentication data not found.');
            return;
        }

        $response = Http::withHeaders([
            'Authorization' => 'Basic ' . config('services.onesignal.rest_api_key'),
            'Content-Type' => 'application/json',
        ])->post('https://onesignal.com/api/v1/notifications', [
            'app_id' => config('services.onesignal.app_id'),
            'headings' => ['en' => 'New Trip Assigned'],
            'contents' => ['en' => 'You have been assigned a new trip. Please check your app for details.'],
            'include_player_ids' => [$driverAuthData->one_signal_id], // or ['All']
            // 'include_player_ids' => ['player_id_1', 'player_id_2'], // for specific users
        ]);

    
        if ($response->successful()) {
            session()->flash('message', 'Notification sent successfully!');
            // $this->reset(['title', 'message']);
        } else {
            session()->flash('error', 'Failed to send notification: ' . $response->body());
        }
    }

          public function sendNotificationToClient()
    {
        $tripData = SupabaseShuttleBooking::find($this->tripId);


        $clientData = SupabaseClient::where('id', $tripData->client_id)->first();
        if (!$clientData) {
            session()->flash('error', 'Driver authentication data not found.');
            return;
        }

        $response = Http::withHeaders([
            'Authorization' => 'Basic ' . config('services.onesignal.rest_api_key'),
            'Content-Type' => 'application/json',
        ])->post('https://onesignal.com/api/v1/notifications', [
            'app_id' => config('services.onesignal.app_id'),
            'headings' => ['en' => 'Trip Assigned'],
            'contents' => ['en' => 'You have been assigned to driver. You will be notified as the driver comes to pick you.'],
            'include_player_ids' => [$clientData->one_signal_id], // or ['All']
            // 'include_player_ids' => ['player_id_1', 'player_id_2'], // for specific users
        ]);

    
        if ($response->successful()) {
            session()->flash('message', 'Notification sent successfully!');
            // $this->reset(['title', 'message']);
        } else {
            session()->flash('error', 'Failed to send notification: ' . $response->body());
        }
    }


    public function assignShuttleDriver(){
                
        $tripData = SupabaseShuttleBooking::find($this->tripId);

        //  if($tripData->paymentPaid == null) {
        //     session()->flash('shuttle_trip_not_paid', 'Trip not paid for, cannot assign driver yet');
        //     return;
        
        // }
        if($tripData->tripStatus->status == 'Assigned' ||  $tripData->tripStatus->status == 'Pending') {
        $tripData->driver_id = $this->driver_id;
        $tripData->trip_status_id = 2;

        $tripData->save();
        $this->sendNotification();
        $this->sendNotificationToClient();
        session()->flash('shuttle_trip_assigned', 'Trip assigned to driver');
        } else {
             session()->flash('cab_trip_not_assigned', 'Trip not in a state to be assigned to a driver');
        }
    }

    


}
