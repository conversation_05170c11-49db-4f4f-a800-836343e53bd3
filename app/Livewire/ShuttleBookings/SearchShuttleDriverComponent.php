<?php

namespace App\Livewire\ShuttleBookings;

use App\Models\SupabaseModels\SupabaseDriver;
use Livewire\Component;
use App\Models\Driver;

class SearchShuttleDriverComponent extends Component
{
    public $searchDriver = '';

    public $driverProfilePic;
    public $driverName;
    public $driverLicenceNumber;

    public $driverId = 0;


    public function render()
    {
       
        $searchResults = SupabaseDriver::when($this->searchDriver, function ($query, $searchDriver) {
            if($searchDriver !== '') {
             $query->latest()->where('driver_firstname', 'like', "%{$searchDriver}%")
            ->orWhere('driver_lastname', 'like', "%{$searchDriver}%")->limit(5);
            }
        })->get();
        return view('livewire.shuttle-bookings.search-shuttle-driver-component', [
            'searchResults'=> $searchResults
        ]);
    }

    public function selectDriver(SupabaseDriver $driver){
        $this->searchDriver = '';
        $this->driverId = $driver->id;
        $this->driverProfilePic = $driver->profile_photo_file;
        $this->driverName = $driver->driver_firstname . '  ' . $driver->driver_lastname;
        $this->driverLicenceNumber = $driver->drivers_licence;
        $this->dispatch('broadcast-assigned-shuttle-driver-id', $driver);

    }
}
