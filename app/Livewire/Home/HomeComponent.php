<?php

namespace App\Livewire\Home;

use App\Models\SupabaseModels\SupabaseCarRental;
use App\Models\SupabaseModels\SupabaseDriverBooking;
use App\Models\SupabaseModels\SupabaseShuttleBooking;
use App\Models\SupabaseModels\SupabaseTaxiBooking;
use Livewire\Component;

class HomeComponent extends Component
{
    public function render()
    {
        $taxiBookingCount = SupabaseTaxiBooking::where('trip_status_id', 1)->count();
        $shuttleBookingCount = SupabaseShuttleBooking::where('trip_status_id', 1)->count();
        $driverBookingCount = SupabaseDriverBooking::where('hire_driver_status_id', 1)->count();
        $carRentalBookingCount = SupabaseCarRental::where('rental_status_id', 1)->count();

       $cabBookings = SupabaseTaxiBooking::where('trip_status_id', 1)
        ->orderBy('id', 'desc')
        ->take(5)
        ->get();

        $cabMapData = SupabaseTaxiBooking::where('trip_status_id', 1)->get();

        $shuttleBookings = SupabaseShuttleBooking::where('trip_status_id', 1)
        ->orderBy('id', 'desc')
        ->take(5)
        ->get();

        $carRentalBookings = SupabaseCarRental::where('rental_status_id', 1)
        ->orderBy('id', 'desc')
        ->take(5)
        ->get();
        return view('livewire.home.home-component', compact(
         'taxiBookingCount',
         'shuttleBookingCount', 
         'driverBookingCount',
         'carRentalBookingCount',
         'cabBookings',
         'shuttleBookings',
         'carRentalBookings',
         'cabMapData'
        ));


    }


    public function logout(){
            dd('Logout'); 
    }
}
