<?php

    use Carbon\Carbon;

    if (!function_exists('convertDateFormat')) {
        function convertDateFormat($date)
        {
                 return Carbon::createFromFormat('m/d/Y', $date)->format('Y-m-d');
        }
    }

    function convertDateToHumanFormat($date)
    {

            return Carbon::createFromFormat('Y-m-d', $date)->format('j M Y');

    }

    function convertToDefaultDateFormat($date)
    {
            return Carbon::createFromFormat( 'Y-m-d', $date)->format('m/d/Y');
    }

    function getHumanDateFromCreatedAt($date) {
        return Carbon::parse($date)->format('F j, Y');
      }

      function convertToAmPmFormat($dateTime) {
        // Create a Carbon instance from the provided date-time string
        $carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $dateTime);
        
        // Format the time to 12-hour format with a.m./p.m.
        return $carbonDate->format('g:i a');
    }

    function getDayBetweenDates($from, $to){
        // Create Carbon instances for the two dates
        $date1 = Carbon::createFromFormat('Y-m-d', $from);
        $date2 = Carbon::createFromFormat('Y-m-d', $to);

        // Calculate the difference in days
        return $date1->diffInDays($date2);
    }

    function convertIsoDateFormat($isoDate)
    {
        return Carbon::parse($isoDate)->format('d M Y');
    }
